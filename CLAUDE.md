# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the NIO-EATV (蔚来汽车转化率预测框架) project - a machine learning framework for automotive purchase conversion rate prediction using the EPMMOENet architecture. The codebase has been recently refactored (June 2025) to use a unified configuration system and adaptive model factory.

## Common Commands

### Environment Setup and Dependencies
```bash
# Install dependencies (Python 3.8+)
pip install tensorflow==2.19.0
pip install pandas numpy pyyaml

# Environment verification (recommended first run)
python scripts/unified_training_test.py --mode simple

# Complete system validation
python scripts/unified_training_test.py --mode real
```

### Training Commands
```bash
# 使用当前最优配置训练 (推荐)
python src/train.py --model_code=GOLDEN_CONFIG --epochs=3 --batch_size=1024 --data_dir=data

# 快速训练测试
python scripts/unified_training_test.py --mode simple

# 完整集成测试
python scripts/unified_training_test.py --mode real

# 传统训练方式（不推荐，使用legacy配置）
python src/train.py \
  --model_code=sample_20250311_v7-20250311 \
  --dataset_code=dataset_nio_new_car_v15 \
  --evaluate_file="20240531_随机采样1%.parquet" \
  --epochs=5 --batch_size=1024
```

### 实验管理命令
```bash
# 创建新实验（基于当前最优配置）
python scripts/experiment_manager.py create --name my_experiment --description "实验描述"

# 运行实验并自动对比性能
python scripts/experiment_manager.py run --name my_experiment --epochs 3

# 查看配置状态
python scripts/experiment_manager.py list

# 清理失败的实验配置
python scripts/experiment_manager.py cleanup
```

### Development Commands
```bash
# Add project root to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Debug with verbose logging
python scripts/unified_training_test.py --mode real --verbose

# Validate configuration files
python -c "
from src.configs.unified_config_manager import unified_config_manager
config = unified_config_manager.load_experiment_config('sample_20250311_v7-20250311')
print(f'Loaded {len(config.features)} features')
"
```

## Architecture Overview

### Core Design Principles
- **Data-driven**: Models are constructed based on actual data characteristics
- **Adaptive**: Automatic feature type inference and optimal architecture selection  
- **Configuration-intelligent**: Auto-inferred configurations reduce manual errors
- **No fallback mechanisms**: Commit to optimal models without compromising

### Key Components

**Unified Configuration System** (`src/configs/`)
- Migrated from 4135-line JSON to hierarchical YAML structure
- `unified_config_manager.py` - Central configuration handler
- `experiments/` - Experiment-specific YAML configs
- `datasets/` - Dataset configuration files
- Supports config validation, inheritance, and version management

**Adaptive Model Factory** (`src/models/adaptive_model_factory.py`)
- **Data-driven analysis**: Automatically infers feature types and processing strategies
- **Intelligent architecture decisions**: Selects optimal architecture based on feature distribution
- **Zero configuration dependency**: Eliminates manual feature mapping
- Core class: `AdaptiveModelFactory.create_model_from_data()`

**EPMMOENet Architecture** (`src/models/networks/EPMMOENet_enhanced.py`)
- Multi-modal inputs: General features, sequence features, scene features
- Time decay attention for user behavior sequences
- Feature cross layers for non-linear interactions
- Adaptive architecture based on data characteristics

**Data Pipeline** (`src/data/`, `src/features/`)
- `loader.py` - Handles parquet file loading with date partitioning
- `preprocessor.py` - Feature preprocessing and validation
- `builder.py` - TensorFlow dataset generation
- Full data validation and quality assurance

### Current Performance
- **Data utilization**: 100% real data (no fallback to simulated)
- **Model parameters**: 1,465,997 parameters in adaptive optimal model
- **Training metrics**: loss 1.9421, accuracy 20.25%, training time 23.51s
- **Feature utilization**: 355 features fully utilized

## Development Patterns

### Recommended Development Workflow
1. **Use Adaptive Factory** (preferred approach):
```python
from src.models.adaptive_model_factory import adaptive_factory

# Auto-construct optimal model
model, preprocessing_info = adaptive_factory.create_model_from_data(
    train_data=df,
    target_column="m_purchase_days_nio_new_car"
)
```

2. **Configuration-driven approach**:
```python
from src.configs.unified_config_manager import unified_config_manager

# Load experiment config
config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")
```

3. **Traditional training**:
```bash
python src/train.py --model_code=experiment_name --dataset_code=dataset_name
```

### Key File Locations
- **Main training script**: `src/train.py` 
- **Quick testing**: `scripts/unified_training_test.py`
- **Configs**: `src/configs/experiments/sample_20250311_v7-20250311.yaml`
- **Models**: `src/models/networks/EPMMOENet_enhanced.py`
- **Adaptive factory**: `src/models/adaptive_model_factory.py`

### Configuration Structure
```yaml
# Example: src/configs/experiments/sample_20250311_v7-20250311.yaml
model_config:
  network_name: "EPMMOENet_Enhanced"
  output_dimension: 6
  use_cross_layer: true

features:
  # 355 intelligently grouped features

input_modules:
  InputGeneral: {features: [...]}  # 310 general features
  InputScene: {features: [...]}    # 40 scene features  
  InputSeqSet: {...}              # 5 sequence feature groups

data_config:
  data_root: "data/dataset_nio_new_car_v15"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
```

### Logging and Monitoring
- All logs output to `logs/` directory
- Training logs: `logs/unified_training_*.log`
- Training reports: `logs/unified_training_report_*.json`
- Model configs saved: `logs/*/enhanced_model_config.json`

## Data Organization

### Data Structure
```
data/dataset_nio_new_car_v15/
├── datetime=20240430/    # Training data
│   ├── hash_group=00/
│   │   └── result.parquet
│   └── ...
├── datetime=20240531/    # Test data
└── 20240531_随机采样1%.parquet  # Evaluation data
```

### Feature Categories
- **General features** (310): User demographics, behavior patterns
- **Scene features** (40): Contextual and environmental factors
- **Sequence features** (5 groups): User action sequences with temporal information

## Troubleshooting

### Common Issues
1. **NaN losses during training**:
   - Lower learning rate to 0.0001
   - Check feature preprocessing
   - Use gradient clipping

2. **Feature configuration mismatches**:
   - Use adaptive model factory for auto-matching
   - Verify feature names in config files
   - Ensure data column names match config

3. **Memory issues**:
   - Reduce batch_size
   - Use data sampling for quick validation
   - Optimize data types (float32 vs float64)

### Quick Health Checks
```bash
# Environment check
python scripts/unified_training_test.py --mode simple

# Full integration test  
python scripts/unified_training_test.py --mode real

# Single epoch test
python src/train.py --model_code=sample_20250311_v7-20250311 --epochs=1 --batch_size=256
```

## Best Practices

1. **Prioritize adaptive model factory**: Reduces configuration errors, improves development efficiency
2. **Preserve business knowledge**: Don't rely completely on automation, retain key business binning logic
3. **Comprehensive logging**: Important operations must have log records
4. **Configuration version control**: Use git to manage configuration file changes
5. **Progressive optimization**: Ensure functionality first, then optimize performance