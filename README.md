# 蔚来汽车转化率预测框架 (NIO-EATV)

基于EPMMOENet架构的汽车购买转化率预测机器学习框架。

## 🎯 项目现状 (2025年6月)

### ✅ 已完成优化
- **模型架构统一**：消除代码重复，统一模型工厂
- **配置系统重构**：从4135行JSON拆分为分层YAML配置
- **自适应模型工厂**：数据驱动的智能模型构建
- **数据质量保证**：完整的数据验证和预处理流程
- **训练流程优化**：消除回退机制，确保最优模型训练

### 📊 当前性能
- **数据加载**：56,464条记录，513个特征
- **模型规模**：1,465,997参数的自适应最优模型
- **训练效果**：loss 1.9421，accuracy 20.25%，训练时间23.51秒
- **系统稳定性**：100%使用真实数据，0%回退机制

## 🚀 快速开始

### 推荐使用方式
```bash
# 环境验证（使用模拟数据）
python scripts/unified_training_test.py --mode simple

# 完整训练（使用真实数据）
python scripts/unified_training_test.py --mode real
```

### 完整训练流程
```bash
python src/train.py \
  --model_code=sample_20250311_v7-20250311 \
  --dataset_code=dataset_nio_new_car_v15 \
  --evaluate_file="20240531_随机采样1%.parquet" \
  --epochs=5 --batch_size=1024
```

## 📁 项目结构

```
nio-eatv/
├── README.md                           # 项目说明
├── DEVELOPMENT.md                      # 开发指南  
├── data/                              # 数据目录
│   └── dataset_nio_new_car_v15/       # 数据集
├── src/                               # 源代码
│   ├── configs/                       # 配置管理
│   │   ├── unified_config_manager.py  # 统一配置管理器
│   │   ├── datasets/                  # 数据集配置
│   │   └── experiments/               # 实验配置
│   ├── data/                         # 数据处理
│   ├── features/                     # 特征工程
│   ├── models/                       # 模型定义
│   │   ├── adaptive_model_factory.py # 自适应模型工厂
│   │   ├── networks/                 # 网络架构
│   │   └── layers/                   # 自定义层
│   ├── training/                     # 训练模块
│   └── evaluation/                   # 评估模块
├── scripts/                          # 工具脚本
│   └── unified_training_test.py      # 统一训练测试
└── logs/                            # 日志和报告
```

## 🧠 核心技术特性

### EPMMOENet架构
- **多模态输入**：通用特征、序列特征、场景特征
- **时间衰减注意力**：处理用户行为序列的时间重要性
- **特征交叉层**：捕获特征间的非线性交互
- **自适应架构**：根据数据特征自动优化模型结构

### 自适应模型工厂
- **数据驱动分析**：自动推断特征类型和处理策略
- **智能架构决策**：基于特征分布自动选择最优架构
- **无配置依赖**：消除手动特征映射，确保配置匹配

### 统一配置管理
- **分层架构**：基础配置、业务配置、实验配置分离
- **YAML格式**：标准化配置格式，易于维护
- **智能推断**：自动推断输入模块和特征分组

## ⚙️ 配置系统

### 实验配置
```yaml
# src/configs/experiments/sample_20250311_v7-20250311.yaml
model_config:
  network_name: "EPMMOENet_Enhanced"
  output_dimension: 6
  use_cross_layer: true

features:
  # 355个特征的智能分组和配置
  
input_modules:
  InputGeneral: {features: [...]}  # 310个通用特征
  InputScene: {features: [...]}    # 40个场景特征
  InputSeqSet: {...}              # 5个序列特征组

data_config:
  data_root: "data/dataset_nio_new_car_v15"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
```

## 📊 模型性能

### 训练表现
- **数据利用率**：100%（无回退到模拟数据）
- **特征利用率**：355个特征全部有效利用
- **训练稳定性**：消除NaN损失，数值稳定训练
- **架构优化**：自适应选择最优模型组件

### 技术指标
- **模型参数**：1,465,997参数
- **训练速度**：23.51秒/epoch（CPU）
- **内存占用**：优化的数据类型和特征处理

## 🔧 开发指南

详细的开发指南请参考：[DEVELOPMENT.md](DEVELOPMENT.md)

包含：
- 环境设置和依赖管理
- 代码开发规范
- 测试和调试方法
- 性能优化技巧

## 📈 项目历程

2025年6月完成的关键优化：
1. **模型架构重构**：消除代码重复，统一模型接口
2. **配置系统现代化**：JSON→YAML，巨大配置→分层管理
3. **自适应能力**：数据驱动的模型构建
4. **质量保证**：完整的验证和错误处理

## 🎯 设计理念

> "我们优化模型的目的应该是生成适合场景最优的模型"

- **数据驱动**：基于实际数据特征构建模型架构
- **配置智能**：自动推断最优配置，减少人工错误
- **无回退机制**：坚持最优模型，不妥协于简化方案
- **工程化优先**：代码质量、可维护性、扩展性并重

## 📚 文档

### 核心文档
- [开发指南](DEVELOPMENT.md) - 环境设置、代码架构、开发流程
- [Claude使用指南](CLAUDE.md) - AI助手使用说明

### 技术文档 (docs/)
- [核心技术经验总结](docs/CORE_TECHNICAL_EXPERIENCE.md) - 从0.52到0.89 AUC的完整技术路径和经验教训
- [配置管理规范](docs/CONFIGURATION_MANAGEMENT.md) - 现代化YAML配置系统和实验管理流程
- [架构优化指南](docs/ARCHITECTURE_OPTIMIZATION.md) - EPMMOENet架构设计和优化路线图

### 数据分析文档 (data/)
- [数据结构分析](data/DATA_STRUCTURE_ANALYSIS.md) - 数据集结构和特征分析
- [特征分析详细报告](data/FEATURE_ANALYSIS_DETAILED.md) - 特征重要性和工程策略

---

*这是一个生产级的机器学习框架，经过工程化优化，适用于大规模转化率预测任务。*