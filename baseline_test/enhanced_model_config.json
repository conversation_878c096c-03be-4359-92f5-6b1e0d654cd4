{"model_name": "EPMMOENet", "model_config": {"_metadata": {"version": "v1.7_yaml_optimized", "creation_date": "2025-06-16", "performance": {"month_1_pr_auc": 0.2545, "month_1_recall_840": 0.5462, "month_1_roc_auc": 0.8652}, "source_config": "golden_config_exact.yaml", "description": "YAML优化版本，35个精选特征，当前最优PR-AUC = 0.2545", "last_updated": "2025-06-16T13:20:06Z", "validation_status": "VALIDATED", "baseline_requirement": "所有新实验必须达到或超过 Month_1 PR-AUC >= 0.2545"}, "model_config": {"network_name": "EPMMOENet", "train_dates": ["20240430"], "test_dates": ["20240531"], "mask_label": null, "use_multitask": false}, "training_config": {"batch_size": 1024, "loss_type": "focal", "pos_weight": 20.0, "use_month_weights": true, "focal_alpha": 0.4, "focal_gamma": 2.0}, "input_modules": {"InputGeneral": {"features": ["user_create_days", "intention_intention_fail_days", "app_search_intention_DSLA", "intention_opportunity_create_days", "user_core_unfirst_reg_leads_nio_DSLA", "user_core_first_reg_leads_nio_DSLA", "intention_create_time_days", "user_register_days", "user_core_visit_nioapp_login_180d_cnt", "user_core_visit_nioapp_login_90d_cnt", "user_core_visit_nioapp_login_30d_cnt", "user_core_visit_nioapp_login_60d_cnt", "user_core_visit_nioapp_login_7d_cnt", "user_core_visit_nioapp_login_1d_cnt", "user_core_nio_value", "user_core_unfirst_reg_leads_nio_180d_cnt", "user_core_unfirst_reg_leads_nio_90d_cnt", "user_core_unfirst_reg_leads_nio_60d_cnt", "user_core_unfirst_reg_leads_nio_30d_cnt", "user_core_unfirst_reg_leads_nio_7d_cnt", "user_core_user_curr_credit_amount", "user_core_pred_has_other_vehicle", "user_core_nio_has_inviter", "user_core_checkin_nioapp_60d_cnt", "user_core_checkin_nioapp_90d_cnt", "fellow_follow_decision_maker", "fellow_follow_intention_nio_confirm", "fellow_follow_intention_test_drive", "user_core_user_gender", "user_core_user_age_group", "user_core_resident_city", "user_core_pred_career_type", "user_core_nio_user_identity", "intention_stage", "intention_status"]}, "InputScene": {"features": []}, "InputSeqSet": {"Set": [], "SetInfo": {}}}, "labels": {"m_purchase_days_nio_new_car": {}, "d_purchase_days_nio_new_car": {}, "pos_flag": {}}, "features": {"user_create_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5]}, "intention_intention_fail_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]}, "app_search_intention_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5], "missing_rate": 0.732}, "intention_opportunity_create_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]}, "user_core_unfirst_reg_leads_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]}, "user_core_first_reg_leads_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]}, "user_core_nio_value": {"dtype": "Bucket", "bin_boundarie": [0.5, 100.5, 200.5, 300.5, 400.5, 500.5, 600.5, 700.5, 800.5, 900.5]}, "user_core_user_curr_credit_amount": {"dtype": "Bucket", "bin_boundarie": [0.5, 1000.5, 5000.5, 10000.5, 20000.5, 50000.5, 100000.5]}, "intention_create_time_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5]}, "user_register_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5, 4018.5, 4383.5, 4749.5]}, "user_core_visit_nioapp_login_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 100.5, 200.5]}, "user_core_visit_nioapp_login_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 50.5, 100.5]}, "user_core_unfirst_reg_leads_nio_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 10.5]}, "user_core_visit_nioapp_login_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]}, "user_core_visit_nioapp_login_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 50.5]}, "user_core_unfirst_reg_leads_nio_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "user_core_visit_nioapp_login_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5]}, "user_core_unfirst_reg_leads_nio_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_unfirst_reg_leads_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_visit_nioapp_login_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_unfirst_reg_leads_nio_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_checkin_nioapp_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5]}, "user_core_checkin_nioapp_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]}, "user_core_pred_has_other_vehicle": {"dtype": "StringLookup", "vocabulary": ["0", "1", "None"]}, "user_core_nio_has_inviter": {"dtype": "StringLookup", "vocabulary": ["0", "1", "None"]}, "fellow_follow_decision_maker": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]}, "fellow_follow_intention_nio_confirm": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "fellow_follow_intention_test_drive": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "user_core_user_gender": {"dtype": "StringLookup", "vocabulary": ["M", "F", "Unknown", "None"]}, "user_core_user_age_group": {"dtype": "StringLookup", "vocabulary": ["18-25", "26-30", "31-35", "36-40", "41-45", "46-50", "51+", "Unknown", "None"]}, "user_core_resident_city": {"dtype": "StringLookup", "vocabulary": ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Chengdu", "Nanjing", "<PERSON><PERSON>", "<PERSON><PERSON>", "Suzhou", "Other", "None"]}, "user_core_pred_career_type": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "None"]}, "user_core_nio_user_identity": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "None"]}, "intention_stage": {"dtype": "StringLookup", "vocabulary": ["A", "I", "D", "O", "None"]}, "intention_status": {"dtype": "StringLookup", "vocabulary": ["Active", "Inactive", "Closed", "None"]}}}, "model_params": {"default_embedding_dimension": 8, "default_gru_dimension": 32, "expert_num": 8, "use_cross_layer": true, "use_multitask": false, "use_mixed_precision": true, "use_time_attention": true, "time_decay_factor": 0.05}, "training_config": {"batch_size": 8192, "loss_type": "standard", "pos_weight": 10.0, "use_month_weights": false, "val_metric": "loss"}}