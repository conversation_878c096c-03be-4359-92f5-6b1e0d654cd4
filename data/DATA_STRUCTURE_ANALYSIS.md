# NIO转化率预测数据结构完整分析报告

## 📊 数据源概览

| 数据源 | 样本数 | 特征时点 | 预测时间窗口 | 正样本率 | 数据性质 | 文件大小 |
|--------|--------|----------|--------------|----------|----------|----------|
| **datetime=20240430** | 56,464 | 4月30日 | 5-10月购买 | 4.33% | 负采样 | 614.6 MB |
| **datetime=20240531** | 9,800 | 5月31日 | 6-11月购买 | 4.04% | 负采样 | - |
| **20240531_随机采样1%.parquet** | 212,611 | 5月31日 | 6-11月购买 | 0.25% | 真实分布 | - |

## 🕒 时间窗口详细说明

### 20240430数据（核心训练数据）
- **特征截止时间**：2024年4月30日
- **预测时间窗口**：2024年5月1日 - 2024年10月31日（未来6个月）
- **月度标签映射**：
  ```
  m_purchase_days_nio_new_car数组索引含义：
  [0] → 5月购买 (2024年5月1日-31日)
  [1] → 6月购买 (2024年6月1日-30日)  
  [2] → 7月购买 (2024年7月1日-31日)
  [3] → 8月购买 (2024年8月1日-31日)
  [4] → 9月购买 (2024年9月1日-30日)
  [5] → 10月购买 (2024年10月1日-31日)
  ```

### 20240531数据（验证/测试数据）
- **特征截止时间**：2024年5月31日
- **预测时间窗口**：2024年6月1日 - 2024年11月30日（未来6个月）
- **月度标签映射**：
  ```
  m_purchase_days_nio_new_car数组索引含义：
  [0] → 6月购买 (2024年6月1日-30日)
  [1] → 7月购买 (2024年7月1日-31日)
  [2] → 8月购买 (2024年8月1日-31日)
  [3] → 9月购买 (2024年9月1日-30日)
  [4] → 10月购买 (2024年10月1日-31日)
  [5] → 11月购买 (2024年11月1日-30日)
  ```

## 📋 标签结构详解

### 两种标签格式
1. **`purchase_days_nio_new_car_total`** - 购买天数
   - 类型：字符串，表示距离特征时点多少天后购买
   - 示例：`"13"` = 13天后购买
   - 范围：1-180天
   - 空值：用户在6个月内不购买

2. **`m_purchase_days_nio_new_car`** - 月度购买数组
   - 类型：JSON字符串，包含6个整数的数组
   - 示例：`"[1,0,0,0,0,0]"` = 第1个月购买
   - 含义：1=购买，0=未购买

### 标签一致性验证
通过分析发现购买天数与月度标签完全对应：
```
购买天数13 → [1,0,0,0,0,0] (第1个月，即5月)
购买天数57 → [0,1,0,0,0,0] (第2个月，即6月)  
购买天数95 → [0,0,0,1,0,0] (第4个月，即8月)
```

## 🔗 数据源关系图

```
全量数据生成流程：
┌─────────────────┐    ┌─────────────────┐
│   全量4月数据    │    │   全量5月数据    │
│   (未知规模)     │    │   (未知规模)     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│ 负采样4月数据   │    │    随机采样1%    │
│   56,464用户    │    │   212,611用户   │
│   4.33%转化率   │    │   0.25%转化率   │
└─────────────────┘    └─────────┬───────┘
                                 │
                                 ▼
                       ┌─────────────────┐
                       │   负采样5月数据  │
                       │    9,800用户    │
                       │   4.04%转化率   │
                       └─────────────────┘
```

## 👥 用户重叠分析

- **430与531重叠**：1,129个用户 (2.0%)
- **430与评估重叠**：9,436个用户 (16.7%)  
- **531与评估重叠**：9,800个用户 (100.0%) ← 531数据是评估数据的子集

## 🎯 采样策略分析

### 负采样数据 (4%正样本率)
- **目的**：提高训练效率，让模型能够学习到正样本模式
- **方法**：人为增加正样本比例，约为真实分布的16-18倍
- **适用**：模型训练和超参数调优

### 真实分布数据 (0.25%正样本率)  
- **目的**：反映真实业务场景
- **特点**：极度不平衡，1000个用户中只有2-3个会购车
- **适用**：最终模型评估和业务效果预测

## ⚡ 关键发现

### 1. 真实转化率极低
- 真实月度转化率仅0.25%，这是一个极度不平衡的预测任务
- 需要专门的不平衡学习技术（Focal Loss、SMOTE等）

### 2. 时间衰减效应
基于20240430数据的月度转化率分析：
```
5月购买率：2.0%  (最高)
6月购买率：1.6%  
7月购买率：1.4%
8月购买率：1.7%
9月购买率：0.7%  (最低)
10月购买率：1.4%
```

### 3. 购买行为特征
- 大部分购买发生在30-90天内
- 近期购买意愿明显高于远期
- 购买决策有明显的时间集中性

## 🚀 业务应用建议

### 最符合业务逻辑的预测任务
1. **短期预测**：基于当月数据，预测下月购买概率
2. **时间窗口**：1个月预测窗口最符合业务节奏
3. **评估指标**：PR-AUC比ROC-AUC更适合极不平衡场景

### 数据使用策略
1. **训练数据**：使用20240430的负采样数据
2. **真实评估**：使用20240531的真实分布数据
3. **时间验证**：验证模型在不同时间点的稳定性

## 📝 数据质量评估

### 优势
- ✅ 标签完整：完整的6个月购买记录
- ✅ 特征丰富：513个特征维度  
- ✅ 时间一致：特征和标签时间逻辑清晰
- ✅ 规模充足：5万+训练样本

### 挑战  
- ⚠️ 极度不平衡：0.25%的真实转化率
- ⚠️ 分布差异：训练4%与真实0.25%的巨大差距
- ⚠️ 时间跨度：不同时间点数据的用户重叠较少

## 🔮 模型开发建议

### 1. 专注单月预测
- 用4月特征预测5月购买，符合实际业务场景
- 避免6个月混合预测的复杂性

### 2. 处理极度不平衡
- 使用Focal Loss或Cost-Sensitive Learning
- 关注Precision@K和Recall@K指标
- 设置合适的决策阈值

### 3. 时间泛化验证
- 用5月数据预测6月来验证模型时间稳定性
- 建立时间序列交叉验证框架

---

**报告生成时间**：2025-06-14  
**数据分析版本**：v1.0  
**分析完成度**：✅ 完整分析