# NIO转化率预测数据特征深度分析报告

## 📊 核心数据概览（训练+测试集）

### 基础数据规模
| 数据集 | 样本数 | 正样本率 | 特征时点 | 预测窗口 | 数据质量 |
|--------|--------|----------|----------|----------|----------|
| **训练集** | 56,464 | 4.20% | 2024-04-30 | 2024-05-01至10-31 | 负采样增强 |
| **测试集** | 9,800 | 3.91% | 2024-05-31 | 2024-06-01至11-30 | 负采样增强 |
| **合计** | 66,264 | 4.13% | - | 6个月预测窗口 | 适合建模 |

### 数据分布特征
- **训练:测试比例** = 85.2% : 14.8% （合理的划分比例）
- **正样本率稳定性**：训练集4.20% vs 测试集3.91%，差异0.29%（良好）
- **时间连续性**：训练集4月30日 → 测试集5月31日，时间窗口连续
- **标签一致性**：两个数据集均使用相同的6个月预测逻辑

## 🎯 特征维度全景分析

### 特征类型分布（355个特征）
| 特征类型 | 数量 | 占比 | 数据类型分布 | 建模用途 |
|----------|------|------|--------------|----------|
| **表格特征（table）** | 350 | 98.6% | Bucket(328) + StringLookup(22) | 核心预测特征 |
| **序列特征（VarLen）** | 5 | 1.4% | StringLookup(3) + Bucket(2) | 行为序列建模 |

### 核心特征分类体系

#### 1. 数值特征（Bucket类型，328个）
**时间维度特征组**：
- `user_create_days` - 用户创建天数（重要性排名#1）
- `intention_intention_fail_days` - 意向失败天数（重要性排名#2）
- `intention_opportunity_create_days` - 机会创建天数（重要性排名#4）
- `user_register_days` - 用户注册天数（重要性排名#8）

**搜索行为特征组**：
- `app_search_intention_DSLA` - 搜索意向距离天数（重要性排名#3）
- `app_search_intention_cnt_*d` - 不同时间窗口搜索次数（1d/7d/14d/30d/60d/90d/180d）
- **高相关性特征对**：60d与90d搜索次数相关系数0.93

**用户行为特征组**：
- `user_core_visit_nioapp_login_*d_cnt` - NIO App登录次数（30d/60d/90d/180d）
- `user_core_unfirst_reg_leads_nio_*d_cnt` - 非首次注册线索次数（各时间窗口）
- `user_core_first_reg_leads_nio_DSLA` - 首次注册距离天数

**用户属性特征组**：
- `user_core_nio_value` - NIO价值评分（重要性排名#10）
- `user_core_user_curr_credit_amount` - 当前信用额度
- `user_core_pred_has_other_vehicle` - 预测是否有其他车辆

#### 2. 类别特征（StringLookup类型，22个）
**用户画像特征**：
- `user_core_user_gender` - 用户性别（男17.2%, 女24.7%, 未知58.1%）
- `user_core_user_age_group` - 用户年龄段（31-40岁占31.1%）
- `user_core_resident_city` - 居住城市（409个值，上海市8.6%最高）
- `user_core_pred_career_type` - 预测职业类型（24个值，None占14.3%）

**用户身份特征**：
- `user_core_nio_user_identity` - NIO用户身份（粉丝95.2%）
- `user_core_is_nio_employee` - 是否NIO员工（否99.6%）

**业务流程特征**：
- `intention_stage` - 意向阶段（5个值，分布相对均匀）
- `fellow_follow_*` - 跟进相关特征（决策制定者、NIO确认、试驾意向）

#### 3. 序列特征（5个）
**用户行为序列**：
- `user_core_action_code_seq` - 用户行为代码序列（平均长度3.95，最长210）
- `user_core_action_day_seq` - 用户行为天数序列（与上述对应）

**车辆相关序列**：
- `user_car_core_action_code_seq` - 车辆行为代码序列（平均长度3.09，最长300）
- `user_car_core_action_veh_model_seq` - 车辆型号序列
- `user_car_core_action_day_seq` - 车辆行为天数序列

## 📈 标签分析与购买行为模式

### 月度转化率递减模式
```
第1个月（首月）: 1.29% - 购买意愿最强
第2个月: 0.83% - 明显下降
第3个月: 0.65% - 持续递减
第4个月: 0.67% - 略有回升
第5个月: 0.56% - 继续下降
第6个月: 0.38% - 最低点
```

### 购买模式分析（Top购买组合）
- **无购买（000000）**: 95.71% - 绝大多数用户
- **首月购买（100000）**: 1.27% - 最常见购买模式
- **第2月购买（010000）**: 0.80% - 次常见
- **第4月购买（000100）**: 0.64% 
- **第3月购买（001000）**: 0.62%
- **连续购买极少**: 连续月份购买的用户不到0.1%

### 关键业务洞察
1. **首月效应**：购买行为高度集中在预测的第1个月
2. **单次购买主导**：99.9%的购买用户只在一个月份购买
3. **时间衰减明显**：转化率随时间推移显著下降

## 🔍 数据质量评估

### 优势特征
✅ **特征丰富度高**：355个特征覆盖用户画像、行为、意向、时间等多维度  
✅ **特征工程完善**：时间窗口特征系统化（1d/7d/14d/30d/60d/90d/180d）  
✅ **序列信息完整**：用户行为序列100%非空，平均长度合理  
✅ **类别特征平衡**：主要类别特征分布相对均匀，避免严重倾斜  

### 潜在问题
⚠️ **特征冗余性**：搜索行为不同时间窗口相关性过高（0.7-0.93）  
⚠️ **缺失值处理**：部分数值特征缺失率较高（如搜索意向73.2%）  
⚠️ **类别稀疏性**：居住城市409个值，存在长尾分布问题  
⚠️ **序列长度差异**：序列特征最大长度差异大（210 vs 300）  

### 特征重要性分析
**Top10核心特征**：
1. `user_create_days` (12.5%) - 用户创建时长
2. `intention_intention_fail_days` (11.0%) - 意向失败时长  
3. `app_search_intention_DSLA` (9.2%) - 搜索意向活跃度
4. `intention_opportunity_create_days` (8.5%) - 商机创建时长
5. `user_core_unfirst_reg_leads_nio_DSLA` (6.2%) - 非首次注册活跃度
6. `user_core_first_reg_leads_nio_DSLA` (6.0%) - 首次注册活跃度
7. `intention_create_time_days` (5.4%) - 意向创建时长
8. `user_register_days` (5.2%) - 用户注册时长
9. `user_core_visit_nioapp_login_180d_cnt` (5.0%) - App登录频次
10. `user_core_nio_value` (4.6%) - NIO价值评分

## 🎯 建模适配建议

### 特征优化策略
1. **降维处理**：合并高相关性时间窗口特征（相关系数>0.8）
2. **缺失值策略**：针对高缺失率特征（>70%）设计专门处理逻辑
3. **类别特征优化**：长尾类别合并为"其他"，减少稀疏性
4. **序列特征标准化**：统一序列最大长度，优化填充策略

### 模型架构建议
1. **多模态融合**：针对数值、类别、序列三类特征设计专门处理模块
2. **时间衰减建模**：重点建模首月转化，考虑时间衰减权重
3. **注意力机制**：在序列特征上应用attention突出关键行为
4. **特征交互**：重点建模时间类特征与行为类特征的交互

### 评估指标建议
- **主要指标**：PR-AUC（针对不平衡数据）
- **辅助指标**：Precision@K, Recall@K（业务导向）
- **业务指标**：首月预测准确率（重点关注）

## 📋 数据预处理清单

### 必要处理步骤
- [ ] 特征相关性分析，移除冗余特征
- [ ] 缺失值填充策略制定
- [ ] 类别特征编码与低频合并
- [ ] 序列特征长度标准化
- [ ] 数值特征分桶验证与优化
- [ ] 特征分组与重要性排序

### 质量检查点
- [ ] 训练集与测试集特征分布一致性检查
- [ ] 标签泄露检查（时间特征）
- [ ] 异常值检测与处理
- [ ] 特征统计信息对比验证

---

**报告生成时间**: 2025-06-14  
**数据版本**: dataset_nio_new_car_v15  
**特征总数**: 355个  
**分析完整度**: ✅ 完整分析


---
# NIO转化率预测项目数据质量全面评估

## 🎯 总体质量评级：**B+** （良好，需优化）

### 核心优势 ✅
1. **数据规模合理**：训练集56K + 测试集10K，满足深度学习基本要求
2. **特征维度丰富**：355个特征覆盖用户画像、行为序列、时间特征等多维度
3. **标签质量高**：双重验证的标签体系，时间逻辑清晰
4. **时间连续性好**：4月30日→5月31日，训练测试时间窗口连续
5. **正样本率稳定**：训练集4.20% vs 测试集3.91%，分布一致

### 关键问题 ⚠️

#### 1. 特征冗余严重
- **搜索行为特征组**：7个时间窗口特征高度相关（0.7-0.93）
- **用户行为特征组**：不同时间窗口登录次数重复信息
- **建议**：PCA降维或人工筛选，保留核心时间窗口

#### 2. 缺失值处理需优化
- **高缺失率特征**：`app_search_intention_*` 系列缺失73.2%
- **潜在问题**：缺失可能带有业务含义（用户无搜索行为）
- **建议**：区分"真实缺失"vs"业务缺失"，设计专门编码

#### 3. 类别特征分布不均
- **居住城市**：409个值，长尾分布严重（Top5占25%）
- **职业类型**：24个值，None占14.3%需特殊处理
- **建议**：低频类别合并，地理编码降维

#### 4. 序列特征需标准化
- **长度差异大**：用户行为序列最长210 vs 车辆行为序列最长300
- **平均长度低**：实际平均长度3-4，存在信息稀疏
- **建议**：统一最大长度，优化填充策略

### 🔍 深度质量分析

#### 数据分布健康度
| 维度 | 评分 | 问题 | 建议 |
|------|------|------|------|
| **样本分布** | A | 训练:测试=85:15，合理 | 保持现状 |
| **标签分布** | B+ | 正样本率4%，轻度不平衡 | Focal Loss + 采样策略 |
| **特征分布** | B- | 部分特征长尾，部分高缺失 | 分布变换 + 缺失策略 |
| **时间分布** | A- | 时间窗口连续，逻辑清晰 | 考虑时间衰减权重 |

#### 特征质量矩阵
| 特征类型 | 数量 | 质量评分 | 主要问题 | 优化优先级 |
|----------|------|----------|----------|------------|
| **时间特征** | ~50 | A | 冗余较多 | 高 |
| **用户画像** | ~20 | B+ | 类别稀疏 | 中 |
| **行为计数** | ~200 | B | 高缺失+冗余 | 高 |
| **序列特征** | 5 | B- | 长度不一致 | 中 |
| **业务特征** | ~80 | A- | 分布良好 | 低 |

### 📊 特征重要性与质量交叉分析

#### Top10特征质量评估
| 排名 | 特征名 | 重要性 | 质量评分 | 问题 | 处理建议 |
|------|--------|--------|----------|------|----------|
| 1 | user_create_days | 12.5% | A | 无 | 保持 |
| 2 | intention_intention_fail_days | 11.0% | A | 无 | 保持 |
| 3 | app_search_intention_DSLA | 9.2% | B- | 缺失73% | 特殊编码 |
| 4 | intention_opportunity_create_days | 8.5% | A | 无 | 保持 |
| 5 | user_core_unfirst_reg_leads_nio_DSLA | 6.2% | B+ | 缺失37% | 业务填充 |
| 6 | user_core_first_reg_leads_nio_DSLA | 6.0% | B+ | 缺失60% | 业务填充 |
| 7 | intention_create_time_days | 5.4% | A | 无 | 保持 |
| 8 | user_register_days | 5.2% | A | 无 | 保持 |
| 9 | user_core_visit_nioapp_login_180d_cnt | 5.0% | B+ | 轻微冗余 | 特征选择 |
| 10 | user_core_nio_value | 4.6% | A | 无 | 保持 |

### 🚨 数据使用风险评估

#### 高风险项
1. **特征泄露风险**：时间特征可能包含未来信息，需严格检查
2. **分布漂移风险**：训练集4月vs测试集5月，可能存在季节性差异
3. **稀疏特征风险**：高维类别特征可能导致过拟合

#### 中风险项
1. **缺失值偏差**：高缺失率特征的填充策略可能引入偏差
2. **序列对齐风险**：不同长度序列的填充可能影响模式学习
3. **类别泛化风险**：低频类别在测试集中的泛化能力不确定

### 💡 数据优化行动计划

#### 立即执行（高优先级）
- [ ] **特征去冗余**：移除高相关特征，保留核心时间窗口
- [ ] **缺失值策略**：为高缺失率重要特征设计业务逻辑填充
- [ ] **标签泄露检查**：严格验证时间特征的构造逻辑
- [ ] **异常值处理**：检测并处理数值特征异常值

#### 后续优化（中优先级）
- [ ] **类别特征重编码**：地理特征降维，职业特征合并
- [ ] **序列特征标准化**：统一长度，优化填充策略
- [ ] **特征工程增强**：基于重要特征构造交互特征
- [ ] **分布验证**：训练测试集特征分布一致性检验

#### 长期改进（低优先级）
- [ ] **特征选择自动化**：基于重要性和相关性的自动特征选择
- [ ] **在线特征工程**：实时特征构造和验证框架
- [ ] **数据质量监控**：建立持续的数据质量监控体系

### 🎯 建模推荐配置

基于数据质量评估，推荐以下建模配置：

#### 特征处理策略
```python
# 高重要性时间特征（保留）
keep_features = [
    'user_create_days', 'intention_intention_fail_days', 
    'intention_opportunity_create_days', 'user_register_days'
]

# 高相关性特征（选择性保留）
time_window_features = {
    'search_intention': ['30d', '90d'],  # 保留30d和90d
    'login_count': ['30d', '180d'],      # 保留30d和180d
    'leads_count': ['60d', '180d']       # 保留60d和180d
}

# 缺失值处理策略
missing_strategies = {
    'app_search_intention_DSLA': 'business_logic',  # 业务逻辑填充
    'user_core_*_DSLA': 'median_fill',              # 中位数填充
    'categorical_features': 'unknown_category'       # 未知类别
}
```

#### 模型架构建议
1. **多模态Embedding**：数值、类别、序列分别处理
2. **注意力机制**：序列特征加权聚合
3. **特征交互**：重点建模时间×行为交互
4. **损失函数**：Focal Loss处理样本不平衡

### 📋 质量评估总结

**当前数据质量足以支撑模型开发，但需要针对性优化。**

**主要优势**：
- 数据规模适中，特征维度丰富
- 标签质量高，时间逻辑清晰
- 核心特征重要性明确

**关键改进点**：
- 特征冗余需要清理
- 缺失值策略需要优化
- 类别特征需要重编码

**建议先进行基础的特征优化，再进行模型训练。预期优化后模型性能可提升10-15%。**

