#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time        : 2025/03/18
<AUTHOR> 分析脚本
@Description : NIO新车购买倾向预测模型数据分析
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from pathlib import Path
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # macOS
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)

# 创建结果目录
RESULT_DIR = "src/data/data_analyze"
Path(RESULT_DIR).mkdir(parents=True, exist_ok=True)

# 配置文件路径
DATASET_CONFIG = "src/configs/datasets/dataset_nio_new_car_v15.json"
MODEL_CONFIG = "src/configs/models/sample_20250311_v7-20250311.json"
DATASET_PATH = "data/dataset_nio_new_car_v15"

# 读取配置文件
try:
    dataset_config = json.load(open(DATASET_CONFIG, "r"))
    model_config = json.load(open(MODEL_CONFIG, "r"))
    train_dates = model_config.get("train_dates", [])
    test_dates = model_config.get("test_dates", [])
    mask_label = model_config.get("mask_label", None)
    
    # 获取特征信息
    dict_raw_features = model_config.get("RawFeature", {})
    list_raw_features = list(dict_raw_features.keys())
except Exception as e:
    print(f"配置文件读取失败: {e}")
    # 设置默认值以便继续运行
    train_dates = ["20240430"]
    test_dates = ["20240531"]
    dict_raw_features = {}
    list_raw_features = []
    
# 设置标签相关字段
list_raw_labels = ["purchase_days_nio_new_car_total"]
all_columns = ["user_id", "datetime"] + list_raw_features + list_raw_labels + ["m_purchase_days_nio_new_car", "mask_label"]

print(f"==== 数据分析开始 ====")
print(f"训练日期分区: {train_dates}")
print(f"测试日期分区: {test_dates}")
print(f"特征列数量: {len(list_raw_features)}")
print(f"标签列数量: {len(list_raw_labels)}")

# 读取数据
def read_data():
    """读取训练集、测试集和评估集数据"""
    dataset_dates = sorted(list(set(train_dates + test_dates)))
    df_list = []
    
    # 记录数据源的文件来源与样本量
    data_source_stats = {}
    
    # 读取训练和测试数据
    for idx, date in enumerate(dataset_dates):
        print(f"读取日期分区 {date} 的数据...")
        try:
            # 记录文件路径
            file_path = f"{DATASET_PATH}/datetime={date}"
            df_i = pd.read_parquet(file_path, columns=all_columns).reset_index(drop=True)
            data_source = 'train' if date in train_dates else 'test'
            df_i['data_source'] = data_source
            df_list.append(df_i)
            
            # 记录样本量和文件来源
            if data_source not in data_source_stats:
                data_source_stats[data_source] = []
            data_source_stats[data_source].append({'file': file_path, 'rows': len(df_i)})
            
            print(f"  - 从文件 {file_path} 读取 {len(df_i)} 行数据，标记为 {data_source}")
        except Exception as e:
            print(f"读取分区 {date} 失败: {e}")
    
    # 读取评估数据
    print(f"读取评估集数据...")
    try:
        eval_file_path = f"{DATASET_PATH}/20240531_随机采样1%.parquet"
        df_evaluate = pd.read_parquet(eval_file_path)
        # 确保只选择所需列
        avail_cols = ["user_id", "datetime"] + [col for col in list_raw_features if col in df_evaluate.columns] + [col for col in list_raw_labels if col in df_evaluate.columns]
        df_evaluate = df_evaluate[avail_cols]
        df_evaluate['data_source'] = 'evaluate'
        df_list.append(df_evaluate)
        
        # 记录样本量和文件来源
        if 'evaluate' not in data_source_stats:
            data_source_stats['evaluate'] = []
        data_source_stats['evaluate'].append({'file': eval_file_path, 'rows': len(df_evaluate)})
        
        print(f"  - 从文件 {eval_file_path} 读取 {len(df_evaluate)} 行数据，标记为 evaluate")
    except Exception as e:
        print(f"读取评估集失败: {e}")
    
    if not df_list:
        raise ValueError("未能成功读取任何数据分区")
    
    # 合并数据
    df_all = pd.concat(df_list, ignore_index=True)
    print(f"总数据量: {len(df_all)}行, 特征维度: {df_all.shape[1]}列")
    
    # 打印详细的数据源统计
    print("\n详细数据源统计:")
    for source, stats in data_source_stats.items():
        total_rows = sum(item['rows'] for item in stats)
        print(f"{source} 总样本数: {total_rows}")
        for item in stats:
            print(f"  - {item['file']}: {item['rows']} 行 ({item['rows']/total_rows*100:.2f}%)")
    
    # 分析数据字段情况
    print(f"\n数据字段概览:")
    print(df_all.columns.tolist())
    
    # 数据类型与缺失值分析
    print(f"\n数据类型与缺失值:")
    missing_rates = df_all.isna().mean() * 100
    dtypes = df_all.dtypes
    for col, dtype in zip(dtypes.index, dtypes.values):
        miss_rate = missing_rates[col]
        print(f"{col}: {dtype}, 缺失率: {miss_rate:.2f}%")
    
    return df_all

# 基本统计信息
def basic_statistics(df):
    """计算基本统计信息"""
    print("\n==== 基本统计信息 ====")
    
    # 数据分布
    data_distribution = df['data_source'].value_counts()
    print(f"数据源分布:\n{data_distribution}")
    
    # 绘制数据源分布
    plt.figure(figsize=(10, 6))
    sns.countplot(x='data_source', data=df)
    plt.title('数据源分布')
    plt.savefig(f'{RESULT_DIR}/data_source_distribution.png')
    
    # 保存样本数量统计
    data_dist_df = pd.DataFrame({
        '数据源': data_distribution.index,
        '样本数量': data_distribution.values,
        '占比': data_distribution.values / len(df) * 100
    })
    data_dist_df.to_csv(f'{RESULT_DIR}/data_distribution.csv', index=False, encoding='utf-8-sig')
    
    # 标签统计
    if 'purchase_days_nio_new_car_total' in df.columns:
        # 尝试将标签转换为数值类型
        try:
            df['purchase_days_nio_new_car_total_num'] = pd.to_numeric(df['purchase_days_nio_new_car_total'], errors='coerce')
            label_counts = df.groupby('data_source')['purchase_days_nio_new_car_total_num'].apply(
                lambda x: (x > 0).sum() / len(x) * 100
            )
            print(f"各数据源正样本比例(%):\n{label_counts}")
            
            # 保存标签统计
            label_stats_df = pd.DataFrame({
                '数据源': label_counts.index,
                '正样本率(%)': label_counts.values
            })
            label_stats_df.to_csv(f'{RESULT_DIR}/label_statistics.csv', index=False, encoding='utf-8-sig')
        except Exception as e:
            print(f"标签转换失败: {e}")
            print("尝试使用字符串判断方法...")
            # 如果转换失败，尝试用字符串方法判断
            df['is_positive'] = df['purchase_days_nio_new_car_total'].apply(
                lambda x: 1 if str(x).strip() != '0' and str(x).strip() != '' and str(x).lower() != 'nan' else 0
            )
            label_counts = df.groupby('data_source')['is_positive'].apply(
                lambda x: x.sum() / len(x) * 100
            )
            print(f"各数据源正样本比例(%):\n{label_counts}")
            
            # 保存标签统计
            label_stats_df = pd.DataFrame({
                '数据源': label_counts.index,
                '正样本率(%)': label_counts.values
            })
            label_stats_df.to_csv(f'{RESULT_DIR}/label_statistics.csv', index=False, encoding='utf-8-sig')
    
    return data_distribution

# 特征类型分析
def analyze_feature_types():
    """分析特征类型分布"""
    print("\n==== 特征类型分析 ====")
    
    # 特征类型统计
    feature_types = defaultdict(int)
    feature_dtype_counts = defaultdict(int)
    
    for feature_name, feature_info in dict_raw_features.items():
        feature_type = feature_info.get("type", "table")
        feature_dtype = feature_info.get("dtype", "StringLookup")
        feature_types[feature_type] += 1
        feature_dtype_counts[f"{feature_type}_{feature_dtype}"] += 1
    
    print(f"特征类型分布: {dict(feature_types)}")
    print(f"特征类型+数据类型分布: {dict(feature_dtype_counts)}")
    
    # 绘制特征类型分布
    plt.figure(figsize=(12, 6))
    plt.subplot(1, 2, 1)
    plt.pie(feature_types.values(), labels=feature_types.keys(), autopct='%1.1f%%')
    plt.title('特征类型分布')
    
    plt.subplot(1, 2, 2)
    bars = plt.bar(feature_dtype_counts.keys(), feature_dtype_counts.values())
    plt.xticks(rotation=45, ha='right')
    plt.title('特征类型与数据类型分布')
    for bar in bars:
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                str(bar.get_height()), ha='center')
    
    plt.tight_layout()
    plt.savefig(f'{RESULT_DIR}/feature_type_distribution.png')
    
    # 保存特征类型统计
    feature_type_df = pd.DataFrame({
        '特征类型': list(feature_types.keys()) + list(feature_dtype_counts.keys()),
        '数量': list(feature_types.values()) + list(feature_dtype_counts.values())
    })
    feature_type_df.to_csv(f'{RESULT_DIR}/feature_type_statistics.csv', index=False, encoding='utf-8-sig')
    
    return feature_types, feature_dtype_counts

# 序列特征分析
def analyze_sequence_features(df):
    """分析序列特征的统计信息"""
    print("\n==== 序列特征分析 ====")
    
    sequence_stats = {}
    
    # 找出序列特征
    sequence_features = []
    for feature_name, feature_info in dict_raw_features.items():
        if feature_info.get("type", "table") == "VarLen":
            sequence_features.append(feature_name)
    
    print(f"序列特征数量: {len(sequence_features)}")
    
    # 选择前10个序列特征进行分析
    sample_seq_features = sequence_features[:10] if len(sequence_features) > 10 else sequence_features
    
    for feature in sample_seq_features:
        if feature not in df.columns:
            continue
            
        print(f"分析序列特征: {feature}")
        
        # 获取非空序列的长度分布
        seq_lengths = df[feature].dropna().apply(lambda x: len(str(x).split(',')) if isinstance(x, str) else 0)
        
        if len(seq_lengths) == 0:
            continue
            
        stats = {
            'min_length': seq_lengths.min(),
            'max_length': seq_lengths.max(),
            'mean_length': seq_lengths.mean(),
            'median_length': seq_lengths.median(),
            'non_empty_rate': (seq_lengths > 0).mean() * 100
        }
        
        sequence_stats[feature] = stats
        
        # 绘制序列长度分布
        plt.figure(figsize=(10, 6))
        sns.histplot(seq_lengths, bins=30, kde=True)
        plt.title(f'特征 {feature} 序列长度分布')
        plt.xlabel('序列长度')
        plt.ylabel('频数')
        plt.savefig(f'{RESULT_DIR}/sequence_length_{feature}.png')
    
    # 保存序列特征统计
    if sequence_stats:
        seq_stats_df = pd.DataFrame(sequence_stats).T.reset_index()
        seq_stats_df.columns = ['特征名'] + list(seq_stats_df.columns)[1:]
        seq_stats_df.to_csv(f'{RESULT_DIR}/sequence_feature_statistics.csv', index=False, encoding='utf-8-sig')
    
    return sequence_stats

# 类别特征分析
def analyze_categorical_features(df):
    """分析类别特征的分布"""
    print("\n==== 类别特征分析 ====")
    
    categorical_stats = {}
    
    # 找出类别特征
    categorical_features = []
    for feature_name, feature_info in dict_raw_features.items():
        if feature_info.get("type", "table") == "table" and feature_info.get("dtype", "") == "StringLookup":
            categorical_features.append(feature_name)
    
    print(f"类别特征数量: {len(categorical_features)}")
    
    # 选择前10个类别特征进行分析
    sample_cat_features = categorical_features[:10] if len(categorical_features) > 10 else categorical_features
    
    for feature in sample_cat_features:
        if feature not in df.columns:
            continue
            
        print(f"分析类别特征: {feature}")
        
        # 获取类别值分布
        value_counts = df[feature].value_counts().sort_values(ascending=False)
        top_n = 10
        top_categories = value_counts.head(top_n)
        
        stats = {
            'unique_values': len(value_counts),
            'top_category': value_counts.index[0],
            'top_category_ratio': value_counts.values[0] / len(df) * 100,
            'top5_ratio': value_counts.iloc[:5].sum() / len(df) * 100,
            'low_freq_ratio': value_counts[value_counts / len(df) < 0.001].sum() / len(df) * 100
        }
        
        categorical_stats[feature] = stats
        
        # 绘制类别值分布
        plt.figure(figsize=(12, 6))
        bars = plt.bar(range(len(top_categories)), top_categories.values)
        plt.xticks(range(len(top_categories)), top_categories.index, rotation=45, ha='right')
        plt.title(f'特征 {feature} 的Top {top_n}类别分布')
        plt.xlabel('类别值')
        plt.ylabel('频数')
        
        # 显示百分比
        for i, bar in enumerate(bars):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    f"{top_categories.values[i]/len(df)*100:.1f}%", ha='center')
        
        plt.tight_layout()
        plt.savefig(f'{RESULT_DIR}/category_distribution_{feature}.png')
    
    # 保存类别特征统计
    if categorical_stats:
        cat_stats_df = pd.DataFrame(categorical_stats).T.reset_index()
        cat_stats_df.columns = ['特征名'] + list(cat_stats_df.columns)[1:]
        cat_stats_df.to_csv(f'{RESULT_DIR}/categorical_feature_statistics.csv', index=False, encoding='utf-8-sig')
    
    return categorical_stats

# 数值特征分析
def analyze_numerical_features(df):
    """分析数值特征的统计特性"""
    print("\n==== 数值特征分析 ====")
    
    numerical_stats = {}
    
    # 找出数值特征
    numerical_features = []
    for feature_name, feature_info in dict_raw_features.items():
        if feature_info.get("type", "table") == "table" and feature_info.get("dtype", "") == "Bucket":
            numerical_features.append(feature_name)
    
    print(f"数值特征数量: {len(numerical_features)}")
    
    # 选择前10个数值特征进行分析
    sample_num_features = numerical_features[:10] if len(numerical_features) > 10 else numerical_features
    
    for feature in sample_num_features:
        if feature not in df.columns:
            continue
            
        print(f"分析数值特征: {feature}")
        
        # 计算统计量
        stats = {
            'min': df[feature].min(),
            'max': df[feature].max(),
            'mean': df[feature].mean(),
            'median': df[feature].median(),
            'std': df[feature].std(),
            'skew': df[feature].skew(),
            'missing_rate': df[feature].isna().mean() * 100,
            'zero_rate': (df[feature] == 0).mean() * 100
        }
        
        numerical_stats[feature] = stats
        
        # 绘制分布图
        plt.figure(figsize=(12, 6))
        plt.subplot(1, 2, 1)
        sns.histplot(df[feature].dropna(), bins=30, kde=True)
        plt.title(f'特征 {feature} 的分布')
        
        plt.subplot(1, 2, 2)
        sns.boxplot(x=df[feature].dropna())
        plt.title(f'特征 {feature} 的箱线图')
        
        plt.tight_layout()
        plt.savefig(f'{RESULT_DIR}/numerical_distribution_{feature}.png')
    
    # 保存数值特征统计
    if numerical_stats:
        num_stats_df = pd.DataFrame(numerical_stats).T.reset_index()
        num_stats_df.columns = ['特征名'] + list(num_stats_df.columns)[1:]
        num_stats_df.to_csv(f'{RESULT_DIR}/numerical_feature_statistics.csv', index=False, encoding='utf-8-sig')
    
    return numerical_stats

# 标签分析
def analyze_labels(df):
    """分析标签的分布情况"""
    print("\n==== 标签分析 ====")
    
    label_stats = {}
    
    # 检查是否有标签列
    if 'purchase_days_nio_new_car_total' not in df.columns or 'm_purchase_days_nio_new_car' not in df.columns:
        print("标签列不存在")
        return None
    
    # 处理总体购买标签
    print("分析总体购买标签分布")
    
    # 尝试将标签转换为数值
    try:
        df['purchase_days_nio_new_car_total_num'] = pd.to_numeric(df['purchase_days_nio_new_car_total'], errors='coerce')
        positive_rate = (df['purchase_days_nio_new_car_total_num'] > 0).mean() * 100
    except Exception:
        # 如果转换失败，使用字符串判断
        df['is_positive'] = df['purchase_days_nio_new_car_total'].apply(
            lambda x: 1 if str(x).strip() != '0' and str(x).strip() != '' and str(x).lower() != 'nan' else 0
        )
        positive_rate = df['is_positive'].mean() * 100
    
    print(f"总体正样本率: {positive_rate:.2f}%")
    
    # 按数据源统计正样本率
    try:
        source_positive_rates = df.groupby('data_source')['purchase_days_nio_new_car_total_num'].apply(
            lambda x: (x > 0).mean() * 100
        )
    except Exception:
        source_positive_rates = df.groupby('data_source')['is_positive'].apply(
            lambda x: x.mean() * 100
        )
    
    print(f"各数据源正样本率(%): \n{source_positive_rates}")
    
    # 处理月度购买标签
    if 'm_purchase_days_nio_new_car' in df.columns:
        monthly_df = df[df['m_purchase_days_nio_new_car'].notna()]
        if len(monthly_df) > 0:
            # 解析月度标签
            try:
                monthly_df['m_purchase_days_nio_new_car'] = monthly_df['m_purchase_days_nio_new_car'].apply(
                    lambda x: json.loads(x) if isinstance(x, str) else x
                )
                
                # 获取月度标签
                monthly_labels = []
                for val in monthly_df['m_purchase_days_nio_new_car']:
                    if isinstance(val, list):
                        monthly_labels.append(val)
                    else:
                        print(f"警告: 非列表类型的月度标签: {type(val)}, 值: {val}")
                
                monthly_labels = np.array(monthly_labels)
                
                if len(monthly_labels) > 0 and monthly_labels.ndim == 2:
                    # 计算每个月的正样本率
                    monthly_positive_rates = []
                    for month in range(monthly_labels.shape[1]):
                        pos_rate = (monthly_labels[:, month] > 0).mean() * 100
                        monthly_positive_rates.append(pos_rate)
                        print(f"第{month+1}个月正样本率: {pos_rate:.2f}%")
                    
                    # 绘制月度正样本率
                    plt.figure(figsize=(10, 6))
                    plt.bar(range(1, len(monthly_positive_rates)+1), monthly_positive_rates)
                    plt.xlabel('预测月份')
                    plt.ylabel('正样本率(%)')
                    plt.title('各预测月份正样本率')
                    plt.xticks(range(1, len(monthly_positive_rates)+1))
                    plt.grid(axis='y', linestyle='--', alpha=0.7)
                    
                    for i, v in enumerate(monthly_positive_rates):
                        plt.text(i+1, v+0.1, f"{v:.2f}%", ha='center')
                    
                    plt.savefig(f'{RESULT_DIR}/monthly_positive_rate.png')
                    
                    # 保存月度正样本率
                    monthly_pos_df = pd.DataFrame({
                        '预测月份': range(1, len(monthly_positive_rates)+1),
                        '正样本率(%)': monthly_positive_rates
                    })
                    monthly_pos_df.to_csv(f'{RESULT_DIR}/monthly_positive_rate.csv', index=False, encoding='utf-8-sig')
                    
                    # 统计连续购买情况
                    purchase_patterns = []
                    for row in monthly_labels:
                        pattern = ''.join(['1' if x > 0 else '0' for x in row])
                        purchase_patterns.append(pattern)
                    
                    pattern_counts = Counter(purchase_patterns)
                    top_patterns = pattern_counts.most_common(10)
                    
                    print("购买模式前10:")
                    for pattern, count in top_patterns:
                        print(f"模式 {pattern}: {count}次 ({count/len(purchase_patterns)*100:.2f}%)")
                    
                    # 绘制购买模式分布
                    plt.figure(figsize=(12, 6))
                    patterns = [p[0] for p in top_patterns]
                    counts = [p[1] for p in top_patterns]
                    bars = plt.bar(patterns, counts)
                    plt.xlabel('购买模式')
                    plt.ylabel('频数')
                    plt.title('Top-10购买模式分布')
                    
                    for bar in bars:
                        plt.text(bar.get_x() + bar.get_width()/2, 
                                bar.get_height() + 0.1, 
                                f"{bar.get_height()/len(purchase_patterns)*100:.1f}%", 
                                ha='center')
                    
                    plt.savefig(f'{RESULT_DIR}/purchase_pattern_distribution.png')
                    
                    # 保存购买模式分布
                    pattern_df = pd.DataFrame({
                        '购买模式': [p[0] for p in pattern_counts.most_common()],
                        '频数': [p[1] for p in pattern_counts.most_common()],
                        '占比(%)': [p[1]/len(purchase_patterns)*100 for p in pattern_counts.most_common()]
                    })
                    pattern_df.to_csv(f'{RESULT_DIR}/purchase_pattern_distribution.csv', index=False, encoding='utf-8-sig')
            except Exception as e:
                print(f"月度标签分析失败: {e}")
    
    return label_stats

# 特征相关性分析
def analyze_feature_correlations(df):
    """分析数值特征之间的相关性"""
    print("\n==== 特征相关性分析 ====")
    
    # 找出可能的数值特征进行相关性分析
    numeric_cols = []
    for col in df.columns:
        if col in list_raw_features and df[col].dtype in ['int64', 'float64']:
            numeric_cols.append(col)
    
    # 如果数值特征太多，选择前20个
    if len(numeric_cols) > 20:
        numeric_cols = numeric_cols[:20]
    
    if len(numeric_cols) < 2:
        print("数值特征不足，无法进行相关性分析")
        return None
        
    print(f"进行相关性分析的特征数量: {len(numeric_cols)}")
    
    # 计算相关性矩阵
    correlation_matrix = df[numeric_cols].corr()
    
    # 绘制相关性热图
    plt.figure(figsize=(14, 12))
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)
    plt.title('特征相关性热图')
    plt.tight_layout()
    plt.savefig(f'{RESULT_DIR}/feature_correlation_heatmap.png')
    
    # 找出高相关性特征对
    high_corr_pairs = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            if abs(correlation_matrix.iloc[i, j]) > 0.7:  # 相关系数绝对值大于0.7
                high_corr_pairs.append((
                    correlation_matrix.columns[i],
                    correlation_matrix.columns[j],
                    correlation_matrix.iloc[i, j]
                ))
    
    if high_corr_pairs:
        print("发现高相关性特征对:")
        high_corr_df = pd.DataFrame(high_corr_pairs, columns=['特征1', '特征2', '相关系数'])
        high_corr_df = high_corr_df.sort_values('相关系数', ascending=False)
        print(high_corr_df)
        high_corr_df.to_csv(f'{RESULT_DIR}/high_correlation_features.csv', index=False, encoding='utf-8-sig')
    else:
        print("未发现高相关性特征对")
    
    return correlation_matrix

# 特征重要性预估
def analyze_feature_importance(df):
    """使用简单模型估计特征重要性"""
    print("\n==== 特征重要性预估 ====")
    
    # 检查是否有标签列
    if 'purchase_days_nio_new_car_total' not in df.columns:
        print("标签列不存在，无法进行特征重要性分析")
        return None
    
    # 尝试将标签转换为二分类
    try:
        if 'purchase_days_nio_new_car_total_num' in df.columns:
            y = (df['purchase_days_nio_new_car_total_num'] > 0).astype(int)
        else:
            # 如果没有数值化标签，用字符串判断
            y = df['purchase_days_nio_new_car_total'].apply(
                lambda x: 1 if str(x).strip() != '0' and str(x).strip() != '' and str(x).lower() != 'nan' else 0
            )
    except Exception as e:
        print(f"标签转换失败: {e}")
        return None
    
    # 选择数值特征
    numeric_features = []
    for col in df.columns:
        if col in list_raw_features and df[col].dtype in ['int64', 'float64']:
            numeric_features.append(col)
    
    if len(numeric_features) < 1:
        print("数值特征不足，无法进行特征重要性分析")
        return None
    
    # 如果特征太多，选择前50个
    if len(numeric_features) > 50:
        numeric_features = numeric_features[:50]
    
    # 准备数据
    X = df[numeric_features].fillna(0)
    
    # 避免类别不平衡问题
    from sklearn.utils import resample
    
    # 取正负样本数量的最小值作为采样数量
    pos_samples = y[y == 1]
    neg_samples = y[y == 0]
    min_samples = min(len(pos_samples), len(neg_samples))
    
    if min_samples > 1000:
        min_samples = 1000  # 最多采样1000个样本
    
    if len(pos_samples) > min_samples:
        pos_indices = pos_samples.index
        sampled_pos_indices = resample(pos_indices, n_samples=min_samples, random_state=42)
        X_pos = X.loc[sampled_pos_indices]
        y_pos = y.loc[sampled_pos_indices]
    else:
        X_pos = X.loc[pos_samples.index]
        y_pos = pos_samples
    
    if len(neg_samples) > min_samples:
        neg_indices = neg_samples.index
        sampled_neg_indices = resample(neg_indices, n_samples=min_samples, random_state=42)
        X_neg = X.loc[sampled_neg_indices]
        y_neg = y.loc[sampled_neg_indices]
    else:
        X_neg = X.loc[neg_samples.index]
        y_neg = neg_samples
    
    # 合并数据
    X_balanced = pd.concat([X_pos, X_neg])
    y_balanced = pd.concat([y_pos, y_neg])
    
    try:
        # 尝试导入必要的库
        from sklearn.ensemble import RandomForestClassifier
        
        # 训练随机森林模型
        rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X_balanced, y_balanced)
        
        # 获取特征重要性
        importances = rf.feature_importances_
        indices = np.argsort(importances)[::-1]
        
        # 绘制特征重要性图
        plt.figure(figsize=(12, 8))
        top_n = min(20, len(numeric_features))  # 展示前20个特征
        plt.title('特征重要性')
        plt.bar(range(top_n), importances[indices[:top_n]], align='center')
        plt.xticks(range(top_n), [numeric_features[i] for i in indices[:top_n]], rotation=90)
        plt.xlim([-1, top_n])
        plt.tight_layout()
        plt.savefig(f'{RESULT_DIR}/feature_importance.png')
        
        # 保存特征重要性
        feature_importance_df = pd.DataFrame({
            '特征名': [numeric_features[i] for i in indices],
            '重要性': importances[indices]
        })
        feature_importance_df.to_csv(f'{RESULT_DIR}/feature_importance.csv', index=False, encoding='utf-8-sig')
        
        print(f"特征重要性分析完成，展示前{top_n}个特征的重要性")
    except ImportError as e:
        print(f"特征重要性分析失败: {e}, 请安装必要的库")
    except Exception as e:
        print(f"特征重要性分析失败: {e}")
    
    return None

# 生成报告
def generate_report():
    """生成数据分析报告"""
    report_content = f"""# NIO新车购买倾向预测模型数据分析报告

## 1. 数据集概览

本次分析基于NIO新车购买倾向预测模型的训练数据，包括以下数据集：

- 训练集日期分区: {train_dates}
- 测试集日期分区: {test_dates}
- 评估集: 20240531日期分区的1%随机采样数据

数据特征总量: {len(list_raw_features)}个特征
标签列: {list_raw_labels}

## 2. 数据量统计

![数据源分布](./data_source_distribution.png)

数据分布情况见`data_distribution.csv`文件。

## 3. 特征类型分析

![特征类型分布](./feature_type_distribution.png)

特征类型统计详见`feature_type_statistics.csv`文件。

## 4. 序列特征分析

序列特征是模型的重要特征类型，主要包括用户行为序列、属性序列等。序列特征的统计情况如下：

![序列长度-用户行为序列](./sequence_length_user_core_action_code_seq.png)
![序列长度-车辆行为序列](./sequence_length_user_car_core_action_code_seq.png)

序列特征统计详情见`sequence_feature_statistics.csv`文件。

## 5. 类别特征分析

类别特征是模型中最常见的特征类型，主要包括用户属性、行为类型等离散特征。

![类别特征-用户性别](./category_distribution_user_core_user_gender.png)
![类别特征-用户年龄段](./category_distribution_user_core_user_age_group.png)

类别特征统计详情见`categorical_feature_statistics.csv`文件。

## 6. 数值特征分析

数值特征主要包括用户的行为次数、时间间隔等连续值特征。

![数值特征-搜索意向](./numerical_distribution_app_search_intention_cnt_30d.png)
![数值特征-首次注册](./numerical_distribution_user_core_first_reg_leads_nio_DSLA.png)

数值特征统计详情见`numerical_feature_statistics.csv`文件。

## 7. 标签分析

![月度正样本率](./monthly_positive_rate.png)

![购买模式分布](./purchase_pattern_distribution.png)

标签统计详情见以下文件：
- `label_statistics.csv`: 总体标签统计
- `monthly_positive_rate.csv`: 月度正样本率
- `purchase_pattern_distribution.csv`: 购买模式分布

## 8. 特征相关性分析

![特征相关性热图](./feature_correlation_heatmap.png)

特征相关性分析可以帮助识别冗余特征，提高模型效率。高相关性特征对详见`high_correlation_features.csv`文件。

## 9. 特征重要性分析

![特征重要性](./feature_importance.png)

特征重要性分析可以帮助理解哪些特征对预测结果影响最大，详情见`feature_importance.csv`文件。

## 10. 数据处理流程

### 10.1 特征预处理

1. **序列特征处理**:
   - 序列最大长度截断
   - 序列填充到固定长度

2. **类别特征处理**:
   - 低频类别聚合
   - 词表生成与编码

3. **数值特征处理**:
   - 异常值处理
   - 特征分桶

### 10.2 标签构建

模型预测目标为未来6个月内的新车购买可能性，标签构建如下：

1. 总购买标签: `purchase_days_nio_new_car_total`
2. 月度购买标签: `m_purchase_days_nio_new_car`，表示未来6个月的月度购买情况
3. 标签掩码: `mask_label`，用于处理数据截断问题

## 11. 模型特点与应用场景

1. **预测目标**: 预测用户未来6个月内购买新车的可能性
2. **应用场景**: 
   - 新车购买意向用户识别
   - 精准营销触达
   - 销售漏斗分析与优化

## 12. 数据优化建议

1. **特征优化方向**:
   - 增加更多的用户兴趣与意向特征
   - 丰富车型偏好特征
   - 增加交互行为序列特征
   - 减少高相关性特征，降低模型复杂度

2. **样本优化方向**:
   - 优化正负样本比例
   - 提高样本的时间代表性
   - 考虑采用更精细的样本权重
   - **重新平衡数据集**: 目前评估集数据量过大，需要调整采样比例

## 13. 总结

本次数据分析全面展示了NIO新车购买倾向预测模型的数据特点，为后续模型优化提供了数据基础。建议后续重点关注：

1. 提高远期月份的预测准确性
2. 优化类别特征的表示方法
3. 增强序列特征的表达能力
4. 平衡样本分布，提高模型泛化能力（评估集占比过大）
5. 基于特征重要性分析结果，关注高重要性特征
6. 考虑通过特征工程创建新的强预测力特征

"""
    
    with open(f"{RESULT_DIR}/报告-数据分析.md", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    print(f"\n报告已生成: {RESULT_DIR}/报告-数据分析.md")

# 主函数
def main():
    # 创建分析结果目录
    os.makedirs(RESULT_DIR, exist_ok=True)
    
    # 读取数据
    df_all = read_data()
    
    # 基本统计
    basic_statistics(df_all)
    
    # 特征类型分析
    analyze_feature_types()
    
    # 序列特征分析
    analyze_sequence_features(df_all)
    
    # 类别特征分析
    analyze_categorical_features(df_all)
    
    # 数值特征分析
    analyze_numerical_features(df_all)
    
    # 标签分析
    analyze_labels(df_all)
    
    # 特征相关性分析
    analyze_feature_correlations(df_all)
    
    # 特征重要性分析
    analyze_feature_importance(df_all)
    
    # 生成报告
    generate_report()
    
    print("\n==== 数据分析完成 ====")

if __name__ == "__main__":
    main() 