# NIO新车购买倾向预测模型数据分析报告

## 1. 数据集概览

本次分析基于NIO新车购买倾向预测模型的训练数据，包括以下数据集：

- 训练集日期分区: ['20240430']
- 测试集日期分区: ['20240531']
- 评估集: 20240531日期分区的1%随机采样数据

数据特征总量: 355个特征
标签列: ['purchase_days_nio_new_car_total']

## 2. 数据量统计

![数据源分布](./data_source_distribution.png)

数据分布情况见`data_distribution.csv`文件。

## 3. 特征类型分析

![特征类型分布](./feature_type_distribution.png)

特征类型统计详见`feature_type_statistics.csv`文件。

## 4. 序列特征分析

序列特征是模型的重要特征类型，主要包括用户行为序列、属性序列等。序列特征的统计情况如下：

![序列长度-用户行为序列](./sequence_length_user_core_action_code_seq.png)
![序列长度-车辆行为序列](./sequence_length_user_car_core_action_code_seq.png)

序列特征统计详情见`sequence_feature_statistics.csv`文件。

## 5. 类别特征分析

类别特征是模型中最常见的特征类型，主要包括用户属性、行为类型等离散特征。

![类别特征-用户性别](./category_distribution_user_core_user_gender.png)
![类别特征-用户年龄段](./category_distribution_user_core_user_age_group.png)

类别特征统计详情见`categorical_feature_statistics.csv`文件。

## 6. 数值特征分析

数值特征主要包括用户的行为次数、时间间隔等连续值特征。

![数值特征-搜索意向](./numerical_distribution_app_search_intention_cnt_30d.png)
![数值特征-首次注册](./numerical_distribution_user_core_first_reg_leads_nio_DSLA.png)

数值特征统计详情见`numerical_feature_statistics.csv`文件。

## 7. 标签分析

![月度正样本率](./monthly_positive_rate.png)

![购买模式分布](./purchase_pattern_distribution.png)

标签统计详情见以下文件：
- `label_statistics.csv`: 总体标签统计
- `monthly_positive_rate.csv`: 月度正样本率
- `purchase_pattern_distribution.csv`: 购买模式分布

## 8. 特征相关性分析

![特征相关性热图](./feature_correlation_heatmap.png)

特征相关性分析可以帮助识别冗余特征，提高模型效率。高相关性特征对详见`high_correlation_features.csv`文件。

## 9. 特征重要性分析

![特征重要性](./feature_importance.png)

特征重要性分析可以帮助理解哪些特征对预测结果影响最大，详情见`feature_importance.csv`文件。

## 10. 数据处理流程

### 10.1 特征预处理

1. **序列特征处理**:
   - 序列最大长度截断
   - 序列填充到固定长度

2. **类别特征处理**:
   - 低频类别聚合
   - 词表生成与编码

3. **数值特征处理**:
   - 异常值处理
   - 特征分桶

### 10.2 标签构建

模型预测目标为未来6个月内的新车购买可能性，标签构建如下：

1. 总购买标签: `purchase_days_nio_new_car_total`
2. 月度购买标签: `m_purchase_days_nio_new_car`，表示未来6个月的月度购买情况
3. 标签掩码: `mask_label`，用于处理数据截断问题

## 11. 模型特点与应用场景

1. **预测目标**: 预测用户未来6个月内购买新车的可能性
2. **应用场景**: 
   - 新车购买意向用户识别
   - 精准营销触达
   - 销售漏斗分析与优化

## 12. 数据优化建议

1. **特征优化方向**:
   - 增加更多的用户兴趣与意向特征
   - 丰富车型偏好特征
   - 增加交互行为序列特征
   - 减少高相关性特征，降低模型复杂度

2. **样本优化方向**:
   - 优化正负样本比例
   - 提高样本的时间代表性
   - 考虑采用更精细的样本权重
   - **重新平衡数据集**: 目前评估集数据量过大，需要调整采样比例

## 13. 总结

本次数据分析全面展示了NIO新车购买倾向预测模型的数据特点，为后续模型优化提供了数据基础。建议后续重点关注：

1. 提高远期月份的预测准确性
2. 优化类别特征的表示方法
3. 增强序列特征的表达能力
4. 平衡样本分布，提高模型泛化能力（评估集占比过大）
5. 基于特征重要性分析结果，关注高重要性特征
6. 考虑通过特征工程创建新的强预测力特征

