{"dataset_name": "dataset_nio_new_car_v15", "description": "NIO新车转化率预测数据集 v15", "version": "v15", "data_format": "parquet", "partition_column": "datetime", "available_dates": ["20240430", "20240531"], "train_dates": ["20240430"], "test_dates": ["20240531"], "evaluation_file": "20240531_随机采样1%.parquet", "data_statistics": {"train_samples": 56464, "test_samples": 9800, "evaluation_samples": 212611, "total_features": 513, "positive_rate_train": 0.042, "positive_rate_test": 0.039, "positive_rate_evaluation": 0.0025}, "label_columns": {"primary": "m_purchase_days_nio_new_car_consum", "original": "m_purchase_days_nio_new_car", "days": "purchase_days_nio_new_car_total"}, "feature_types": {"numeric": 328, "categorical": 22, "sequence": 5}, "data_quality": {"missing_value_rate": 0.15, "duplicate_rate": 0.0, "outlier_rate": 0.08}}