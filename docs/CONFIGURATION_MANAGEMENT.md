# 配置管理规范 📋

## 🎯 概述

本文档定义了NIO转化率预测模型的现代化配置管理规范，基于统一YAML配置系统，确保实验探索过程有序、最优方案稳定。

## 🏗️ 现代化配置架构

### 统一配置系统结构
```
src/configs/
├── unified_config_manager.py       # 统一配置管理器
├── experiments/                    # 🧪 实验配置 (YAML)
│   ├── sample_20250311_v7-20250311.yaml
│   └── enhanced_top25_features.yaml
├── architectures/                  # 🏛️ 架构配置
│   ├── epmmoe_standard.yaml
│   └── epmmoe_with_embeddings.yaml
├── features/                       # 🔧 特征注册表
│   └── feature_registry.yaml
├── datasets/                       # 📊 数据集配置
│   └── dataset_nio_new_car_v15.yaml
└── models/                        # 📁 传统配置 (JSON, 向后兼容)
    ├── GOLDEN_CONFIG.json
    └── legacy/
```

### 配置层次结构
1. **基础配置层**：标准化的特征类型和处理方法
2. **业务配置层**：领域知识和业务规则  
3. **实验配置层**：具体实验的配置组合

## ⚙️ YAML配置格式

### 实验配置示例
```yaml
# src/configs/experiments/sample_20250311_v7-20250311.yaml
experiment_name: "sample_20250311_v7-20250311"
description: "基于EPMMOENet的转化率预测实验"

model_config:
  network_name: "EPMMOENet"
  output_dimension: 6
  output_activation: "sigmoid"
  use_cross_layer: true
  use_multitask: false
  use_time_attention: true
  default_embedding_dimension: 8
  default_gru_dimension: 32

data_config:
  data_root: "data"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
  data_format: "parquet"
  partition_column: "datetime"
  missing_value_strategy: "fill_default"
  validation_split: 0.2

features:
  user_create_days:
    type: "Bucket"
    boundaries: [0.5, 30.5, 60.5, 90.5, 180.5, 365.5]
    description: "用户创建天数分桶"
  
  intention_stage:
    type: "StringLookup"
    vocabulary: ["A", "B", "C", "D", "E"]
    description: "意向阶段分类"

input_modules:
  InputGeneral:
    description: "核心一般特征模块"
    features: [
      "user_create_days",
      "intention_intention_fail_days",
      "app_search_intention_DSLA",
      # ... 更多特征
    ]
  
  InputScene:
    description: "场景特征模块"
    features: [
      "user_core_nio_user_identity",
      "intention_stage",
      "intention_status"
    ]

sequence_sets:
  UserCoreSequence:
    features: [
      "user_core_action_code_seq",
      "user_core_action_day_seq"
    ]
    gru_dimension: 32
    max_length: 50
```

### 架构配置示例
```yaml
# src/configs/architectures/epmmoe_standard.yaml
network_name: "EPMMOENet"
output_dimension: 6
output_activation: "sigmoid"

# 基础架构参数
default_embedding_dimension: 8
default_gru_dimension: 32
expert_num: 8

# 架构开关
use_cross_layer: true
use_multitask: false
use_mixed_precision: true
use_time_attention: true
time_decay_factor: 0.05

# 训练相关参数
loss_type: "focal"
pos_weight: 10.0
use_month_weights: true
val_metric: "loss"

description: "EPMMOENet标准架构 - 支持多模态特征和时间注意力机制"
version: "v1.0"
```

## 🔄 实验管理流程

### 1. 使用统一配置管理器
```python
from src.configs.unified_config_manager import unified_config_manager

# 加载实验配置
config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")

# 保存新配置
new_config = ExperimentConfig(
    experiment_name="my_experiment",
    model_config=ModelArchConfig(network_name="EPMMOENet"),
    # ... 其他配置
)
unified_config_manager.save_experiment_config(new_config)
```

### 2. 配置转换和迁移
```python
# 从传统JSON配置转换
legacy_config = unified_config_manager.convert_legacy_json_config(
    "src/configs/models/legacy/old_config.json",
    "new_experiment_name"
)

# 配置验证
config.validate()  # 自动验证配置一致性
```

### 3. 标准实验流程
```bash
# 1. 使用现有配置训练
python src/train.py --model_code=sample_20250311_v7-20250311 --epochs=3

# 2. 创建新实验配置（基于现有配置）
python scripts/create_experiment_config.py \
  --base_config=sample_20250311_v7-20250311 \
  --new_name=my_feature_experiment \
  --description="测试新特征组合"

# 3. 运行新实验
python src/train.py --model_code=my_feature_experiment --epochs=3
```

## 📊 Golden Config机制

### Golden Config特征
- **唯一性**: 系统中只有一个Golden Config
- **元数据**: 包含完整的性能指标和版本信息
- **可追溯**: 记录来源配置和历史变更
- **验证状态**: 标记为VALIDATED的配置

### 当前Golden Config
```json
{
  "_metadata": {
    "version": "enhanced_top25_features",
    "creation_date": "2025-06-16", 
    "performance": {
      "month_1_pr_auc": 0.2426,
      "month_1_recall_840": 0.5714,
      "month_1_roc_auc": 0.8691
    },
    "source_config": "enhanced_top25_features.yaml",
    "description": "Current best performing configuration with 35 selected features",
    "last_updated": "2025-06-16T07:30:00Z",
    "validation_status": "VALIDATED"
  },
  "network_name": "EPMMOENet",
  "feature_count": 35,
  "key_features": [
    "user_create_days",
    "intention_intention_fail_days", 
    "app_search_intention_DSLA",
    "intention_opportunity_create_days"
  ]
}
```

## 🎯 性能评估标准

### 提升阈值
- **Month_1 PR_AUC**: 提升 > 0.001 (0.1%)
- **Month_1 Recall@840**: 提升 > 0.01 (1%)
- **训练稳定性**: 收敛epoch数 < 5

### 决策流程
1. **显著提升** (>1%): 自动提示提升为Golden
2. **微小提升** (0.1%-1%): 需要人工确认
3. **无提升/下降**: 自动归档到failed

## 🧪 实验配置规范

### 命名规范
- **描述性**: `feature_interaction_v1`, `sequence_optimization`, `focal_loss_tuning`
- **版本化**: 使用v1, v2等后缀区分迭代
- **避免**: `test`, `new`, `final`等模糊名称

### 实验类型分类
1. **特征工程**: `feature_*`
2. **架构优化**: `arch_*`  
3. **参数调优**: `param_*`
4. **损失函数**: `loss_*`
5. **数据处理**: `data_*`

### 实验元数据
```yaml
_experiment_metadata:
  experiment_name: "feature_interaction_v1"
  description: "探索意向×搜索特征交互"
  created_from: "enhanced_top25_features"
  created_at: "2025-06-16T08:00:00Z"
  status: "CREATED"
  expected_improvement: "PR_AUC +0.005"
```

## 🧹 配置管理最佳实践

### 1. 配置创建
- ✅ 明确实验目标和假设
- ✅ 基于Golden Config创建副本
- ✅ 编写清晰的实验描述
- ✅ 确保数据和环境一致

### 2. 配置验证
- ✅ 使用`config.validate()`检查一致性
- ✅ 验证特征引用的完整性
- ✅ 检查参数类型和范围
- ✅ 确保向后兼容性

### 3. 版本管理
- ✅ 每个配置都有唯一标识
- ✅ 记录配置变更历史
- ✅ 保持配置文件的git版本控制
- ✅ 定期清理过时配置

### 4. 避免的做法
- ❌ 直接修改Golden Config
- ❌ 保留大量未命名的实验配置
- ❌ 忽略配置验证错误
- ❌ 不及时清理冗余配置

## 🔧 配置工具使用

### 统一配置管理器API
```python
from src.configs.unified_config_manager import unified_config_manager

# 加载配置
config = unified_config_manager.load_experiment_config("experiment_name")

# 保存配置
unified_config_manager.save_experiment_config(config, "new_name")

# 转换传统配置
new_config = unified_config_manager.convert_legacy_json_config(
    "legacy_path.json", "new_name"
)

# 配置验证
config.validate()
```

### 配置文件操作
```bash
# 验证配置文件
python -c "
from src.configs.unified_config_manager import load_experiment_config
config = load_experiment_config('sample_20250311_v7-20250311')
print(f'Loaded {len(config.features)} features')
"

# 列出所有实验配置
ls src/configs/experiments/*.yaml

# 检查配置差异
diff src/configs/experiments/config1.yaml src/configs/experiments/config2.yaml
```

## 📈 配置演进历史

| 版本 | 日期 | PR_AUC | 特征数 | 主要变更 |
|------|------|---------|--------|----------|
| enhanced_top25_features | 2025-06-16 | 0.2426 | 35 | 精简为核心特征 |
| enhanced_final_test | 2025-06-16 | 0.2235 | 40 | 特征整合优化 |
| high_recall_optimized | 2025-06-15 | 0.1894 | 53 | 高召回率优化 |
| sample_20250311_v7-20250311 | 2025-06-15 | 0.1600 | 355 | 历史基线复现 |

## 🚀 下一步行动

1. **完善YAML配置系统**: 将所有传统JSON配置迁移到YAML
2. **建立配置监控**: 跟踪Golden Config性能趋势
3. **自动化验证**: 集成CI/CD流程验证配置有效性
4. **团队培训**: 确保团队成员了解新的配置管理流程

---

**文档创建**: 2025-06-16  
**配置系统版本**: v2.0 (YAML统一配置)  
**当前Golden Config**: enhanced_top25_features (PR_AUC=0.2426)
