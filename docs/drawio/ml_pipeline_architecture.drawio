<mxfile host="65bd71144e">
    <diagram name="ML Pipeline Architecture" id="ml-pipeline">
        <mxGraphModel dx="1613" dy="1370" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1800" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="机器学习模型训练架构流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="20" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="config-layer" value="配置层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="80" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="config-manager" value="ConfigManager&#xa;配置管理器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="140" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="model-config" value="模型参数配置&#xa;(model_code.json)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="280" y="140" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="dataset-config" value="数据集配置&#xa;(dataset_code.json)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="480" y="140" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="network-name" value="网络架构&#xa;(network_name)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="680" y="140" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="raw-features" value="原始特征配置&#xa;(RawFeature)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="860" y="140" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="data-loading" value="1. 数据加载阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="250" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="data-loader" value="DataLoader&#xa;数据加载器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="310" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dataset-path" value="数据集路径&#xa;dataset_code" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="260" y="310" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="train-data" value="训练数据&#xa;(train_dates)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="400" y="310" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="test-data" value="测试数据&#xa;(test_dates)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="540" y="310" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="eval-data" value="评估数据&#xa;(evaluate_file)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="680" y="310" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="extra-datasets" value="额外数据集&#xa;(extra_datasets)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="820" y="310" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="preprocessing" value="2. 数据预处理阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="420" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="preprocessor" value="DataPreprocessor&#xa;数据预处理器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="480" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="feature-padding" value="特征填充字典&#xa;(feature_padding_dict)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="280" y="480" width="130" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="feature-preprocess" value="特征预处理&#xa;(基于数据集配置)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="450" y="480" width="130" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="model-feature-preprocess" value="模型特征预处理&#xa;(基于模型配置)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="620" y="480" width="130" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="label-process" value="标签处理&#xa;(purchase_labels)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="790" y="480" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="feature-engineering" value="3. 特征工程阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="590" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="feature-builder" value="FeatureBuilder&#xa;特征构建器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="650" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="train-test-split" value="训练测试分割&#xa;split_train_test" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="280" y="650" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dataset-generation" value="数据集生成&#xa;generate_dataset" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="460" y="650" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="embedding-features" value="嵌入特征处理&#xa;(embedding_data)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="640" y="650" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="model-training" value="4. 模型训练阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="760" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="model-trainer" value="ModelTrainer&#xa;模型训练器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="820" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="model-building" value="模型构建&#xa;build_model" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="280" y="820" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cross-layer" value="Cross Layer&#xa;交叉层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="420" y="800" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="time-attention" value="Time Attention&#xa;时间注意力" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="420" y="850" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="transformer" value="Transformer&#xa;架构选择" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="540" y="800" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="loss-function" value="损失函数配置&#xa;(focal/weighted)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="540" y="850" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="multitask-learning" value="多任务学习&#xa;(use_multitask)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="660" y="800" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="early-stopping" value="早停机制&#xa;(patience)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="660" y="850" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="training-process" value="训练过程控制&#xa;(epochs)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="780" y="820" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="model-inference" value="5. 模型推理阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="930" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="inference-engine" value="推理引擎&#xa;(trainer.inference)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="test-inference" value="测试集推理&#xa;(df_test_m)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="280" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="eval-inference" value="评估集推理&#xa;(df_evaluate)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="460" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="prediction-results" value="预测结果&#xa;(result column)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="640" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="model-save" value="模型保存&#xa;(checkpoints)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="820" y="990" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="model-evaluation" value="6. 模型评估阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="1100" width="1100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="model-evaluator" value="ModelEvaluator&#xa;模型评估器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="1160" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="evaluation-metrics" value="评估指标计算&#xa;(AUC, Precision, Recall)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="280" y="1160" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="mask-evaluation" value="掩码评估&#xa;(mask_label)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="490" y="1160" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="evaluation-results" value="评估结果输出&#xa;(evaluation_results)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="670" y="1160" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="output-directory" value="输出目录&#xa;(output_dir)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="870" y="1160" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" parent="1" source="config-layer" target="data-loading" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="arrow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" parent="1" source="data-loading" target="preprocessing" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="arrow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" parent="1" source="preprocessing" target="feature-engineering" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="arrow4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" parent="1" source="feature-engineering" target="model-training" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="arrow5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" parent="1" source="model-training" target="model-inference" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="arrow6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" parent="1" source="model-inference" target="model-evaluation" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>