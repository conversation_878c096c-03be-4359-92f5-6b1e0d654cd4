#!/usr/bin/env python
"""
临时脚本：从新配置结构创建测试用的JSON配置文件
"""
import json
import yaml
from pathlib import Path
import sys
import os

# Add src to path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

def load_yaml_config(file_path):
    """加载YAML配置文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def create_test_json_config():
    """从分层配置创建测试用的JSON配置"""
    configs_dir = project_root / "src" / "configs"
    
    # 加载各部分配置
    experiment_config = load_yaml_config(configs_dir / "experiments" / "nio_v7_converted.yaml")
    arch_config = load_yaml_config(configs_dir / "architectures" / "nio_v7_converted_arch.yaml")
    data_config = load_yaml_config(configs_dir / "data" / "nio_v7_converted_data.yaml")
    
    # 加载特征配置
    feature_groups_config = load_yaml_config(configs_dir / "features" / "nio_v7_converted_feature_groups.yaml")
    
    # 合并所有特征
    all_features = {}
    for i in range(1, 5):  # 特征被分成4个文件
        feature_file = configs_dir / "features" / f"nio_v7_converted_features_chunk_{i}.yaml"
        if feature_file.exists():
            chunk_features = load_yaml_config(feature_file)
            all_features.update(chunk_features)
    
    # 构建JSON配置
    json_config = {
        "network_name": experiment_config["legacy_info"]["network_name"],
        "prediction_method": "6m",
        "feature_padding_dict": all_features,
        "RawFeature": all_features,
        "train_dates": experiment_config["legacy_info"]["train_dates"],
        "test_dates": experiment_config["legacy_info"]["test_dates"],
        "labels": ["m_purchase_days_nio_new_car"],
        "output_dimension": arch_config.get("output_dimension", 6),
        "use_cross_layer": arch_config.get("use_cross_layer", True),
        "use_time_attention": arch_config.get("use_time_attention", True),
        "use_multitask": arch_config.get("use_multitask", False),
        "batch_size": experiment_config["training_params"]["batch_size"],
        "learning_rate": experiment_config["training_params"]["learning_rate"]
    }
    
    return json_config

def main():
    # 创建必要的目录
    models_dir = project_root / "src" / "configs" / "models"
    models_dir.mkdir(exist_ok=True)
    
    # 生成测试配置
    config = create_test_json_config()
    
    # 保存为JSON文件
    output_file = models_dir / "sample_20250311_v7-20250311.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 测试配置已创建: {output_file}")
    print(f"   特征数量: {len(config.get('RawFeature', {}))}")
    print(f"   网络类型: {config['network_name']}")

if __name__ == "__main__":
    main()