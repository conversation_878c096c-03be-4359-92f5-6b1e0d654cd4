"""
增强数据流程演示 - 展示如何使用新的数据质量检查功能

这个演示展示了完整的数据流程：
1. 数据加载 + 质量检查
2. 数据预处理 + 质量验证
3. 特征构建 + 最终验证
4. 模型训练准备
"""

import sys
import logging
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_demo_data():
    """创建演示用的模拟数据"""
    logger.info("🎭 创建演示数据...")
    
    # 创建模拟数据
    np.random.seed(42)
    n_samples = 1000
    
    data = {
        # 用户属性特征
        'user_age_group': np.random.choice(['18-25', '26-35', '36-45', '46-55', '55+'], n_samples),
        'user_gender': np.random.choice(['M', 'F', None], n_samples, p=[0.45, 0.45, 0.1]),
        'user_income': np.random.normal(50000, 20000, n_samples),
        
        # 行为计数特征
        'search_count_7d': np.random.poisson(5, n_samples),
        'search_count_30d': np.random.poisson(20, n_samples),
        'view_car_count_7d': np.random.poisson(3, n_samples),
        'view_car_count_30d': np.random.poisson(12, n_samples),
        
        # 意向特征
        'intention_stage': np.random.choice(['awareness', 'consideration', 'decision'], n_samples),
        'test_drive_flag': np.random.choice([0, 1], n_samples, p=[0.7, 0.3]),
        
        # 标签
        'purchase_label': np.random.choice([0, 1], n_samples, p=[0.8, 0.2])
    }
    
    # 添加一些数据质量问题（故意的）
    df = pd.DataFrame(data)
    
    # 1. 添加异常值
    df.loc[10:15, 'user_income'] = np.random.normal(500000, 50000, 6)  # 极高收入
    df.loc[20:25, 'search_count_7d'] = np.random.poisson(100, 6)  # 异常高搜索次数
    
    # 2. 添加更多缺失值
    df.loc[50:70, 'user_age_group'] = None
    df.loc[80:90, 'user_income'] = None
    
    # 3. 添加一些重复行
    df = pd.concat([df, df.iloc[:5]], ignore_index=True)
    
    # 4. 添加一个常量列
    df['constant_column'] = 'constant_value'
    
    # 5. 添加一个全空列
    df['empty_column'] = None
    
    logger.info(f"✅ 演示数据创建完成：{len(df)} 行，{len(df.columns)} 列")
    logger.info(f"   - 包含故意添加的数据质量问题用于演示")
    
    return df

def demo_enhanced_data_loading():
    """演示增强数据加载功能"""
    logger.info("🚀 =====演示增强数据加载=====")
    
    try:
        from src.data.enhanced_loader import EnhancedDataLoader
        
        # 创建演示数据
        demo_df = create_demo_data()
        
        # 保存到临时文件
        temp_dir = Path("temp_demo_data")
        temp_dir.mkdir(exist_ok=True)
        
        demo_file = temp_dir / "demo_data.csv"
        demo_df.to_csv(demo_file, index=False)
        
        # 创建演示配置
        demo_config = {
            "data_root": str(temp_dir),
            "data_format": "csv",
            "preprocessing": {
                "remove_duplicates": True,
                "remove_constant_columns": True,
                "missing_value_strategy": "intelligent",
                "outlier_detection": True,
                "outlier_threshold": 3.0
            }
        }
        
        # 初始化增强加载器
        loader = EnhancedDataLoader(
            dataset_config=demo_config,
            enable_validation=True,
            validation_level="standard"
        )
        
        # 模拟加载过程（直接加载演示数据）
        logger.info("📊 开始演示数据加载和质量检查...")
        
        # 手动验证演示数据的质量
        feature_config = {
            'user_age_group': {'type': 'StringLookup'},
            'user_gender': {'type': 'StringLookup'},
            'user_income': {'type': 'Bucket'},
            'search_count_7d': {'type': 'Bucket'},
            'search_count_30d': {'type': 'Bucket'},
            'view_car_count_7d': {'type': 'Bucket'},
            'view_car_count_30d': {'type': 'Bucket'},
            'intention_stage': {'type': 'StringLookup'},
            'test_drive_flag': {'type': 'Bucket'},
            'purchase_label': {'type': 'Bucket'}
        }
        
        # 执行质量验证
        validation_report = loader.validator.validate_dataframe(
            demo_df, feature_config, "demo_dataset"
        )
        
        logger.info("📋 数据质量检查结果：")
        if validation_report.get("errors"):
            logger.warning(f"   ❌ 发现错误：{validation_report['errors']}")
        
        feature_issues = validation_report.get("feature_issues", {})
        if feature_issues:
            logger.info("   ⚠️  特征问题：")
            for feature, issues in feature_issues.items():
                logger.info(f"      - {feature}: {issues}")
        
        if not validation_report.get("errors") and not feature_issues:
            logger.info("   ✅ 数据质量良好")
        
        # 清理临时文件
        demo_file.unlink()
        temp_dir.rmdir()
        
        return demo_df, validation_report
        
    except ImportError as e:
        logger.error(f"❌ 导入增强加载器失败：{e}")
        logger.error("请确保已创建 enhanced_loader.py 文件")
        return None, None
    
    except Exception as e:
        logger.error(f"❌ 演示加载失败：{e}")
        return None, None

def demo_enhanced_preprocessing(demo_df):
    """演示增强预处理功能"""
    logger.info("🚀 =====演示增强数据预处理=====")
    
    if demo_df is None:
        logger.error("没有可用的演示数据")
        return None, None
    
    try:
        from src.data.enhanced_preprocessor import EnhancedDataPreprocessor
        
        # 初始化增强预处理器
        preprocessor = EnhancedDataPreprocessor(
            enable_validation=True,
            auto_feature_engineering=True,
            generate_reports=True
        )
        
        # 特征配置
        feature_config = {
            'user_age_group': {'type': 'StringLookup'},
            'user_gender': {'type': 'StringLookup'},
            'user_income': {'type': 'Bucket'},
            'search_count_7d': {'type': 'Bucket'},
            'search_count_30d': {'type': 'Bucket'},
            'view_car_count_7d': {'type': 'Bucket'},
            'view_car_count_30d': {'type': 'Bucket'},
            'intention_stage': {'type': 'StringLookup'},
            'test_drive_flag': {'type': 'Bucket'},
            'purchase_label': {'type': 'Bucket'}
        }
        
        # 预处理配置
        preprocessing_config = {
            "remove_duplicates": True,
            "remove_constant_columns": True,
            "missing_value_strategy": "intelligent",
            "outlier_detection": True,
            "outlier_threshold": 3.0,
            "outlier_method": "iqr"
        }
        
        # 执行增强预处理
        processed_df, report = preprocessor.preprocess_features(
            demo_df, feature_config, preprocessing_config
        )
        
        # 显示处理结果
        logger.info("📊 预处理结果摘要：")
        summary = report.get("summary", {})
        logger.info(f"   - 处理时间：{summary.get('processing_time_seconds', 0):.2f} 秒")
        logger.info(f"   - 原始形状：{summary.get('original_shape')}")
        logger.info(f"   - 处理后形状：{summary.get('processed_shape')}")
        logger.info(f"   - 新增特征：{summary.get('features_added', 0)} 个")
        logger.info(f"   - 删除行数：{summary.get('rows_removed', 0)} 行")
        
        # 显示转换统计
        transformations = report.get("transformations", {})
        logger.info("📋 转换统计：")
        for transform_type, stats in transformations.items():
            logger.info(f"   - {transform_type}: {stats}")
        
        # 显示建议
        recommendations = report.get("recommendations", [])
        if recommendations:
            logger.info("💡 改进建议：")
            for rec in recommendations:
                logger.info(f"   {rec}")
        
        return processed_df, report
        
    except ImportError as e:
        logger.error(f"❌ 导入增强预处理器失败：{e}")
        logger.error("请确保已创建 enhanced_preprocessor.py 文件")
        return None, None
    
    except Exception as e:
        logger.error(f"❌ 演示预处理失败：{e}")
        return None, None

def demo_model_training_preparation(processed_df):
    """演示模型训练准备"""
    logger.info("🚀 =====演示模型训练准备=====")
    
    if processed_df is None:
        logger.error("没有可用的处理后数据")
        return
    
    try:
        from src.models.model_factory import create_model
        from src.training.enhanced_trainer import EnhancedModelTrainer
        
        logger.info("🔧 准备模型训练配置...")
        
        # 创建模拟的模型配置
        model_config = {
            "network_name": "EPMMOENet_Enhanced",
            "output_dimension": 1,  # 二分类
            "use_cross_layer": True,
            "use_time_attention": False,  # 演示数据没有序列特征
            "use_multitask": False
        }
        
        # 初始化增强训练器
        trainer = EnhancedModelTrainer(model_config, "demo_experiment")
        
        logger.info("✅ 模型训练准备完成")
        logger.info(f"   - 数据形状：{processed_df.shape}")
        logger.info(f"   - 特征列表：{list(processed_df.columns)}")
        logger.info(f"   - 模型类型：{model_config['network_name']}")
        
        return trainer
        
    except ImportError as e:
        logger.error(f"❌ 导入模型组件失败：{e}")
        logger.error("请确保已创建相关模型文件")
        return None
    
    except Exception as e:
        logger.error(f"❌ 模型准备失败：{e}")
        return None

def demo_complete_pipeline():
    """完整流程演示"""
    logger.info("🎬 ========增强数据流程完整演示========")
    logger.info("这个演示展示了从数据加载到模型训练准备的完整流程")
    logger.info("包含自动数据质量检查、智能预处理和质量验证")
    
    # 1. 数据加载演示
    demo_df, load_report = demo_enhanced_data_loading()
    
    if demo_df is None:
        logger.error("❌ 数据加载演示失败，停止后续流程")
        return
    
    # 2. 数据预处理演示
    processed_df, preprocessing_report = demo_enhanced_preprocessing(demo_df)
    
    if processed_df is None:
        logger.error("❌ 数据预处理演示失败，停止后续流程")
        return
    
    # 3. 模型训练准备演示
    trainer = demo_model_training_preparation(processed_df)
    
    # 4. 生成最终报告
    logger.info("📊 ========最终流程报告========")
    logger.info(f"✅ 数据流程执行成功")
    logger.info(f"   - 原始数据：{demo_df.shape[0]} 行，{demo_df.shape[1]} 列")
    logger.info(f"   - 处理后数据：{processed_df.shape[0]} 行，{processed_df.shape[1]} 列")
    
    if load_report:
        errors = len(load_report.get("errors", []))
        warnings = len(load_report.get("warnings", []))
        logger.info(f"   - 数据质量：{errors} 个错误，{warnings} 个警告")
    
    if preprocessing_report:
        transformations = len(preprocessing_report.get("transformations", {}))
        logger.info(f"   - 应用转换：{transformations} 种预处理操作")
    
    logger.info("🎯 关键改进点：")
    logger.info("   ✅ 自动数据质量检查 - 提前发现数据问题")
    logger.info("   ✅ 智能预处理策略 - 根据数据特征自动选择处理方法")
    logger.info("   ✅ 处理效果验证 - 预处理前后质量对比")
    logger.info("   ✅ 详细报告生成 - 完整的处理记录和建议")
    logger.info("   ✅ 异常数据处理 - 自动检测和处理异常值、缺失值")
    
    logger.info("🚀 下一步：可以安全地进行模型训练！")

def main():
    """主函数"""
    try:
        demo_complete_pipeline()
        
        logger.info("🎉 演示完成！")
        logger.info("💡 主要收益：")
        logger.info("   1. 训练前发现数据问题，避免模型训练失败")
        logger.info("   2. 自动化数据处理，减少手工错误")
        logger.info("   3. 详细的质量报告，便于问题追踪")
        logger.info("   4. 智能处理策略，提升数据质量")
        
    except Exception as e:
        logger.error(f"❌ 演示执行失败：{e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())