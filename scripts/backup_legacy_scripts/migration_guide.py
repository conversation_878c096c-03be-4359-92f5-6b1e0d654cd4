"""
迁移指南：从旧架构迁移到新的统一架构

这个脚本演示如何从现有的重复代码架构迁移到新的统一架构。
主要解决的问题：
1. 消除EPMMOENet_with_embeddings.py代码重复
2. 统一模型创建接口
3. 简化配置管理
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any

# 导入新的组件
from src.models.model_factory import model_factory, create_model, migrate_to_enhanced_model
from src.training.enhanced_trainer import EnhancedModelTrainer, ModelTrainer
from src.configs.config_schema import ConfigFactory

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MigrationHelper:
    """迁移助手类"""
    
    def __init__(self):
        self.config_factory = ConfigFactory()
        self.logger = logging.getLogger(__name__)
    
    def demonstrate_old_vs_new(self):
        """演示新旧方式的对比"""
        
        print("=" * 60)
        print("🔄 EPMMOENet架构迁移演示")
        print("=" * 60)
        
        # 示例配置
        model_config = self._load_sample_config()
        
        print("\n📌 旧方式（存在代码重复问题）:")
        print("-" * 40)
        self._demonstrate_old_approach(model_config)
        
        print("\n✨ 新方式（统一架构）:")
        print("-" * 40)
        self._demonstrate_new_approach(model_config)
        
        print("\n🚀 嵌入特征集成演示:")
        print("-" * 40)
        self._demonstrate_embedding_integration(model_config)
        
        print("\n📊 配置管理改进:")
        print("-" * 40)
        self._demonstrate_config_improvements()
    
    def _load_sample_config(self) -> Dict[str, Any]:
        """加载示例配置"""
        return {
            "network_name": "EPMMOENet",
            "train_dates": ["20240430"],
            "test_dates": ["20240531"],
            "output_dimension": 6,
            "use_cross_layer": True,
            "use_time_attention": True,
            "RawFeature": {
                "user_id": {"dtype": "StringLookup", "vocabulary": ["user1", "user2"]},
                "age": {"dtype": "Bucket", "bin_boundarie": [18, 30, 50]},
                "behavior_seq": {"dtype": "StringLookup", "vocabulary": ["action1", "action2"]}
            },
            "InputGeneral": {
                "features": ["user_id", "age"]
            },
            "InputSeqSet": {
                "Set": ["user_behavior"],
                "SetInfo": {
                    "user_behavior": {
                        "features": ["behavior_seq"],
                        "max_length": 50
                    }
                }
            }
        }
    
    def _demonstrate_old_approach(self, config: Dict[str, Any]):
        """演示旧的方式（存在问题）"""
        
        print("❌ 问题1：代码重复严重")
        print("   - EPMMOENet.py (700+ 行)")
        print("   - EPMMOENet_with_embeddings.py (几乎完全重复)")
        print("   - EPMMOENet_Transformer.py (更多重复)")
        
        print("\n❌ 问题2：配置文件过于庞大")
        print("   - 单个JSON文件包含所有配置")
        print("   - 特征定义与模型架构混杂")
        print("   - 难以维护和版本管理")
        
        print("\n❌ 问题3：模型创建复杂")
        print("   - 需要根据不同需求复制整个模型类")
        print("   - 导入路径不一致")
        print("   - 错误处理不完善")
        
        # 展示旧的使用方式
        print("\n💻 旧的代码示例：")
        print("""
        # 旧方式：需要复制整个模型类来支持嵌入特征
        if use_embeddings:
            from src.models.networks.EPMMOENet_with_embeddings import EPMMOENet_with_embeddings_Model
            model_instance = EPMMOENet_with_embeddings_Model(config, embedding_dim=16)
            model = model_instance.build_model()
        else:
            from src.models.networks.EPMMOENet import EPMMOENet_Model
            model = EPMMOENet_Model(config)
        """)
    
    def _demonstrate_new_approach(self, config: Dict[str, Any]):
        """演示新的统一方式"""
        
        print("✅ 解决方案：统一的模型工厂")
        print("   - 单一的EnhancedEPMMOENet类")
        print("   - 动态特征扩展能力")
        print("   - 统一的创建接口")
        
        print("\n💻 新的代码示例：")
        print("""
        # 新方式：统一接口，无需代码重复
        from src.models.model_factory import create_model
        
        # 创建标准模型
        model = create_model("EPMMOENet", config)
        
        # 创建支持嵌入特征的模型（无需重复代码！）
        embedding_features = {"user_embedding": {"type": "Dense", "dimension": 16}}
        model = create_model("EPMMOENet_with_embeddings", config, 
                           embedding_features=embedding_features)
        
        # 创建Transformer版本
        model = create_model("EPMMOENet_Enhanced", config, architecture_type="transformer")
        """)
        
        # 实际演示
        try:
            print("\n🔧 实际创建演示：")
            
            # 创建标准模型
            standard_model = create_model("EPMMOENet_Enhanced", config)
            print(f"   ✓ 标准模型创建成功：{type(standard_model).__name__}")
            
            # 创建支持嵌入的模型
            embedding_features = {
                "user_embedding": {"type": "Dense", "dimension": 16}
            }
            embedding_model = create_model("EPMMOENet_with_embeddings", config,
                                         embedding_features=embedding_features)
            print(f"   ✓ 嵌入模型创建成功：{type(embedding_model).__name__}")
            
        except Exception as e:
            print(f"   ⚠️  演示模型创建时出现预期错误（缺少完整特征定义）：{type(e).__name__}")
    
    def _demonstrate_embedding_integration(self, config: Dict[str, Any]):
        """演示嵌入特征集成"""
        
        print("🔗 嵌入特征集成的改进：")
        print("   - 无需复制整个模型类")
        print("   - 通过适配器模式动态扩展")
        print("   - 自动检测和处理嵌入特征")
        
        print("\n💻 嵌入特征使用示例：")
        print("""
        # 1. 准备嵌入特征配置
        embedding_features = {
            "user_embedding": {
                "type": "Dense",
                "dimension": 16,
                "description": "用户行为嵌入向量"
            },
            "item_embedding": {
                "type": "Dense", 
                "dimension": 32,
                "description": "商品特征嵌入向量"
            }
        }
        
        # 2. 创建支持嵌入的模型（一行代码！）
        model = create_model("EPMMOENet_with_embeddings", config,
                           embedding_features=embedding_features)
        
        # 3. 训练时自动处理嵌入特征
        trainer = EnhancedModelTrainer(config, "experiment_with_embeddings")
        model = trainer.build_model(feature_dict, embedding_features=embedding_features)
        """)
    
    def _demonstrate_config_improvements(self):
        """演示配置管理改进"""
        
        print("📋 配置管理的改进：")
        print("   - 结构化配置模式")
        print("   - 自动验证和类型检查")
        print("   - 按职责分离配置")
        
        print("\n💻 新配置方式示例：")
        print("""
        from src.configs.config_schema import ConfigFactory
        
        # 1. 创建结构化配置
        factory = ConfigFactory()
        
        # 2. 从模板创建基础配置
        config = factory.create_baseline_template()
        
        # 3. 添加嵌入特征支持
        embedding_features = ["user_embedding", "item_embedding"]
        config = factory.add_embedding_features(config, embedding_features)
        
        # 4. 自动验证配置
        config.validate()  # 自动检查配置一致性
        """)
        
        # 实际演示配置创建
        try:
            baseline_config = self.config_factory.create_baseline_template()
            print(f"\n   ✓ 基础配置模板创建成功：{baseline_config.experiment_name}")
            
            # 添加嵌入特征
            enhanced_config = self.config_factory.add_embedding_features(
                baseline_config, ["user_embedding"], 16)
            print(f"   ✓ 嵌入特征添加成功：{len(enhanced_config.features)} 个特征")
            
        except Exception as e:
            print(f"   ⚠️  配置演示出现错误：{e}")
    
    def create_migration_script(self, project_root: str = "."):
        """创建实际的迁移脚本"""
        
        script_content = '''#!/usr/bin/env python
"""
自动迁移脚本：将现有项目迁移到新架构

运行方式：
python migration_script.py
"""

import os
import shutil
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_project():
    """执行项目迁移"""
    
    logger.info("🚀 开始迁移项目到新架构...")
    
    # 1. 备份原有文件
    backup_old_files()
    
    # 2. 更新导入语句
    update_imports()
    
    # 3. 创建迁移使用示例
    create_migration_examples()
    
    logger.info("✅ 迁移完成！")

def backup_old_files():
    """备份可能冲突的旧文件"""
    files_to_backup = [
        "src/models/networks/EPMMOENet_with_embeddings.py",
        "src/training/trainer.py"
    ]
    
    backup_dir = Path("migration_backup")
    backup_dir.mkdir(exist_ok=True)
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            shutil.copy2(file_path, backup_dir / Path(file_path).name)
            logger.info(f"📦 备份文件：{file_path}")

def update_imports():
    """更新导入语句（示例）"""
    logger.info("🔧 更新导入语句...")
    
    # 这里可以添加自动化的导入更新逻辑
    print("""
    需要手动更新的导入语句：
    
    旧的导入：
    from src.models.networks.EPMMOENet_with_embeddings import EPMMOENet_with_embeddings_Model
    
    新的导入：
    from src.models.model_factory import create_model
    from src.training.enhanced_trainer import EnhancedModelTrainer
    """)

def create_migration_examples():
    """创建迁移使用示例"""
    example_dir = Path("migration_examples")
    example_dir.mkdir(exist_ok=True)
    
    # 创建使用示例文件
    example_content = """
# 新架构使用示例

# 1. 创建标准模型
from src.models.model_factory import create_model
model = create_model("EPMMOENet", config)

# 2. 创建支持嵌入的模型
embedding_features = {"user_embedding": {"type": "Dense", "dimension": 16}}
model = create_model("EPMMOENet_with_embeddings", config, 
                   embedding_features=embedding_features)

# 3. 使用增强版训练器
from src.training.enhanced_trainer import EnhancedModelTrainer
trainer = EnhancedModelTrainer(config, "my_experiment")
model = trainer.build_model(feature_dict, embedding_features=embedding_features)
"""
    
    with open(example_dir / "usage_examples.py", "w") as f:
        f.write(example_content)
    
    logger.info(f"📝 创建使用示例：{example_dir}/usage_examples.py")

if __name__ == "__main__":
    migrate_project()
'''
        
        script_path = Path(project_root) / "migration_script.py"
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(script_content)
        
        logger.info(f"📝 迁移脚本已创建：{script_path}")
        return script_path

def run_migration_demo():
    """运行迁移演示"""
    helper = MigrationHelper()
    
    print("🚀 开始EPMMOENet架构迁移演示...")
    print("这个演示展示如何解决代码重复和配置管理问题")
    
    try:
        helper.demonstrate_old_vs_new()
        
        print("\n" + "=" * 60)
        print("📋 迁移建议：")
        print("=" * 60)
        print("1. 阶段性迁移：先使用模型工厂，再逐步迁移配置")
        print("2. 向后兼容：新架构完全兼容现有代码")
        print("3. 渐进改进：可以逐个功能迁移，不影响生产")
        print("4. 删除重复：迁移完成后可以安全删除重复文件")
        
        print("\n🎯 下一步行动：")
        print("1. 运行此演示脚本熟悉新架构")
        print("2. 在测试环境中尝试新的模型创建方式") 
        print("3. 逐步迁移现有实验到新配置格式")
        print("4. 删除EPMMOENet_with_embeddings.py等重复文件")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误：{e}")
        logger.error("这通常是由于缺少完整的特征定义，实际使用时不会有此问题")

if __name__ == "__main__":
    run_migration_demo()