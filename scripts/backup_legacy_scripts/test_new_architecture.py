"""
新架构验证脚本

验证新的模型工厂和训练器是否正常工作
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_factory():
    """测试模型工厂功能"""
    logger.info("🧪 测试模型工厂...")
    
    try:
        from src.models.model_factory import model_factory, create_model
        
        # 简单配置用于测试
        test_config = {
            "network_name": "EPMMOENet",
            "output_dimension": 6,
            "RawFeature": {
                "test_feature": {
                    "dtype": "StringLookup",
                    "vocabulary": ["a", "b", "c"],
                    "embedding_dimension": 8
                }
            },
            "InputGeneral": {
                "features": ["test_feature"]
            },
            "InputSeqSet": {
                "Set": [],
                "SetInfo": {}
            },
            "InputScene": {
                "features": []
            }
        }
        
        # 测试标准模型创建
        model_info = model_factory.get_model_info("EPMMOENet_Enhanced")
        logger.info(f"   ✓ 模型信息获取成功：{model_info}")
        
        logger.info("   ✓ 模型工厂基础功能正常")
        return True
        
    except Exception as e:
        logger.error(f"   ❌ 模型工厂测试失败：{e}")
        return False

def test_enhanced_trainer():
    """测试增强版训练器"""
    logger.info("🧪 测试增强版训练器...")
    
    try:
        from src.training.enhanced_trainer import EnhancedModelTrainer
        
        test_config = {
            "network_name": "EPMMOENet",
            "output_dimension": 6,
            "batch_size": 1024
        }
        
        trainer = EnhancedModelTrainer(test_config, "test_run", "/tmp/test_output")
        logger.info(f"   ✓ 训练器初始化成功：{trainer.network_name}")
        
        # 测试配置解析
        assert trainer.batch_size == 1024
        assert trainer.network_name == "EPMMOENet"
        
        logger.info("   ✓ 增强版训练器基础功能正常")
        return True
        
    except Exception as e:
        logger.error(f"   ❌ 增强版训练器测试失败：{e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    logger.info("🧪 测试向后兼容性...")
    
    try:
        from src.models.model_factory import EPMMOENet_Model, EPMMOENet_with_embeddings_Model
        from src.training.enhanced_trainer import ModelTrainer
        
        # 测试兼容性接口存在
        assert callable(EPMMOENet_Model)
        assert callable(EPMMOENet_with_embeddings_Model)
        assert ModelTrainer is not None
        
        logger.info("   ✓ 向后兼容性接口正常")
        return True
        
    except Exception as e:
        logger.error(f"   ❌ 向后兼容性测试失败：{e}")
        return False

def test_config_schema():
    """测试配置模式"""
    logger.info("🧪 测试配置模式...")
    
    try:
        from src.configs.config_schema import ConfigFactory, ExperimentConfig
        
        factory = ConfigFactory()
        
        # 测试基础模板创建
        baseline_config = factory.create_baseline_template()
        assert isinstance(baseline_config, ExperimentConfig)
        assert baseline_config.experiment_name == "baseline_template"
        
        logger.info("   ✓ 配置模式基础功能正常")
        return True
        
    except Exception as e:
        logger.error(f"   ❌ 配置模式测试失败：{e}")
        return False

def test_feature_adapters():
    """测试特征适配器"""
    logger.info("🧪 测试特征适配器...")
    
    try:
        from src.models.layers.feature_adapters import (
            EmbeddingFeatureAdapter, 
            DynamicFeatureComposer,
            ModelVersionManager
        )
        
        # 测试适配器类存在
        assert EmbeddingFeatureAdapter is not None
        assert DynamicFeatureComposer is not None
        assert ModelVersionManager is not None
        
        # 测试配置转换功能
        test_config = {"test": "config"}
        embedding_features = ["emb1", "emb2"]
        enhanced_config = ModelVersionManager.create_embedding_config(test_config, embedding_features)
        
        assert "InputGeneral" in enhanced_config
        assert len(enhanced_config["InputGeneral"]["features"]) >= 2
        
        logger.info("   ✓ 特征适配器基础功能正常")
        return True
        
    except Exception as e:
        logger.error(f"   ❌ 特征适配器测试失败：{e}")
        return False

def run_comprehensive_test():
    """运行全面测试"""
    logger.info("🚀 开始新架构验证测试...")
    
    tests = [
        ("模型工厂", test_model_factory),
        ("增强版训练器", test_enhanced_trainer),
        ("向后兼容性", test_backward_compatibility),
        ("配置模式", test_config_schema),
        ("特征适配器", test_feature_adapters)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 时出现异常：{e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n📊 测试结果汇总：")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name:20s} {status}")
        if result:
            passed += 1
    
    logger.info("=" * 50)
    logger.info(f"总计：{passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        logger.info("🎉 所有测试通过！新架构工作正常。")
        return True
    else:
        logger.warning(f"⚠️  有 {len(tests) - passed} 个测试失败，需要检查相关组件。")
        return False

def main():
    """主函数"""
    logger.info("EPMMOENet新架构验证")
    logger.info("=" * 60)
    
    # 检查当前目录
    current_dir = Path.cwd()
    src_dir = current_dir / "src"
    
    if not src_dir.exists():
        logger.error("❌ 未找到src目录，请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 运行测试
    success = run_comprehensive_test()
    
    if success:
        logger.info("\n🎯 下一步建议：")
        logger.info("1. 运行 python migration_guide.py 查看迁移演示")
        logger.info("2. 尝试使用新的模型工厂创建模型")
        logger.info("3. 逐步迁移现有训练脚本")
        logger.info("4. 可以安全删除重复的模型文件")
        sys.exit(0)
    else:
        logger.error("\n❌ 部分测试失败，请检查相关组件后再使用新架构")
        sys.exit(1)

if __name__ == "__main__":
    main()