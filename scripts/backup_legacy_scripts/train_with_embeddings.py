"""
训练带有嵌入特征的转化率预估模型
"""

import os
import sys
import argparse
import datetime
import json
import logging

# 将项目根目录添加到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = current_dir
sys.path.append(project_root)

from src.features.add_embeddings import load_embeddings, add_embeddings_to_data, update_model_config

def setup_logging(log_file=None):
    """设置日志"""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 控制台日志
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件日志
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练带有嵌入特征的转化率预估模型')
    
    # 数据参数
    parser.add_argument('--data_dir', type=str, default='data', help='数据目录')
    parser.add_argument('--embeddings_dir', type=str, default='logs/my_test_run_028', help='嵌入向量目录')
    parser.add_argument('--output_dir', type=str, default='src/evaluation', help='输出目录')
    
    # 模型参数
    parser.add_argument('--model_code', type=str, default='sample_20250311_v7-20250311', help='模型代码')
    parser.add_argument('--dataset_code', type=str, default='dataset_nio_new_car_v15', help='数据集代码')
    parser.add_argument('--evaluate_file', type=str, default='20240531_随机采样1%.parquet', help='评估文件')
    parser.add_argument('--run_name', type=str, default=None, help='运行名称')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=4096, help='批量大小')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    parser.add_argument('--use_cross_layer', type=bool, default=True, help='是否使用特征交叉层')
    parser.add_argument('--use_time_attention', type=bool, default=True, help='是否使用时间注意力')
    
    args = parser.parse_args()
    return args

def main():
    """主函数"""
    args = parse_args()
    
    # 创建输出目录
    run_name = args.run_name or datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output_dir, run_name)
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置日志
    log_file = os.path.join(output_dir, 'training.log')
    logger = setup_logging(log_file)
    logger.info(f"开始训练，输出目录: {output_dir}")
    
    # 准备嵌入向量路径
    train_embeddings_path = os.path.join(args.embeddings_dir, 'user_embeddings_20240430.pkl')
    test_embeddings_path = os.path.join(args.embeddings_dir, 'user_embeddings_20240531.pkl')
    
    # 准备模型配置路径
    model_config_path = os.path.join('src', 'configs', 'models', f'{args.model_code}.json')
    dataset_config_path = os.path.join('src', 'configs', 'datasets', f'{args.dataset_code}.json')
    
    # 临时数据处理目录
    temp_data_dir = os.path.join(output_dir, 'processed_data')
    os.makedirs(temp_data_dir, exist_ok=True)
    
    logger.info("准备数据和配置文件...")
    
    # 加载嵌入向量
    logger.info(f"加载嵌入向量: {train_embeddings_path}")
    train_embeddings, embedding_dim = load_embeddings(train_embeddings_path)
    logger.info(f"加载嵌入向量: {test_embeddings_path}")
    test_embeddings, _ = load_embeddings(test_embeddings_path)
    
    # 合并嵌入向量以提高匹配率
    all_embeddings = {**train_embeddings, **test_embeddings}
    logger.info(f"合并后的嵌入向量总数: {len(all_embeddings)}")
    
    # 更新模型配置
    logger.info(f"更新模型配置: {model_config_path}")
    embedding_columns = [f'embedding_{i}' for i in range(embedding_dim)]
    new_model_config_path = os.path.join(output_dir, f'{args.model_code}_with_embeddings.json')
    update_model_config(model_config_path, embedding_columns, new_model_config_path)
    
    # 复制数据集配置
    logger.info(f"复制数据集配置: {dataset_config_path}")
    with open(dataset_config_path, 'r', encoding='utf-8') as f:
        dataset_config = json.load(f)
    
    new_dataset_config_path = os.path.join(output_dir, f'{args.dataset_code}.json')
    with open(new_dataset_config_path, 'w', encoding='utf-8') as f:
        json.dump(dataset_config, f, indent=4, ensure_ascii=False)
    
    # 运行训练命令
    new_model_code = f'{args.model_code}_with_embeddings'
    train_cmd = [
        "python", "src/train.py",
        f"--model_code={new_model_code}",
        f"--dataset_code={args.dataset_code}",
        f"--evaluate_file={args.evaluate_file}",
        f"--data_dir={args.data_dir}",
        f"--epochs={args.epochs}",
        f"--batch_size={args.batch_size}",
        f"--patience={args.patience}",
        f"--run_name={run_name}",
        f"--use_cross_layer={'true' if args.use_cross_layer else 'false'}",
        f"--use_time_attention={'true' if args.use_time_attention else 'false'}",
        f"--embedding_model=EPMMOENet_with_embeddings",
        f"--embedding_dir={args.embeddings_dir}"
    ]
    
    train_cmd_str = " ".join(train_cmd)
    logger.info(f"运行训练命令: {train_cmd_str}")
    
    # 输出运行命令
    with open(os.path.join(output_dir, 'run_command.sh'), 'w') as f:
        f.write(f"#!/bin/bash\n{train_cmd_str}\n")
    
    # 实际运行
    import subprocess
    subprocess.run(train_cmd)
    
    logger.info("训练完成!")

if __name__ == "__main__":
    main() 