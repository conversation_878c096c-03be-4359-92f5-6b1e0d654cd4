#!/usr/bin/env python
"""
基线性能测试脚本

目标：
1. 测试当前GOLDEN_CONFIG的实际性能
2. 建立性能基线
3. 为后续优化提供对比基准
"""

import os
import sys
import logging
import time
import json
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np
import tensorflow as tf

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor
from src.features.builder import FeatureBuilder
from src.training.enhanced_trainer import EnhancedModelTrainer
from src.evaluation.evaluator import ModelEvaluator


def setup_logging():
    """设置日志"""
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f"baseline_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    
    return logging.getLogger(__name__)


def load_golden_config():
    """加载GOLDEN_CONFIG配置"""
    import yaml
    config_file = Path("src/configs/models/GOLDEN_CONFIG.yaml")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config


def run_baseline_test(logger, epochs=3, quick_mode=False):
    """运行基线测试"""
    logger.info("=" * 60)
    logger.info("开始基线性能测试")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    try:
        # 1. 加载配置
        config = load_golden_config()
        logger.info(f"配置加载成功: {config.get('_metadata', {}).get('version', 'unknown')}")
        
        # 2. 数据加载
        logger.info("开始数据加载...")
        data_loader = DataLoader("data/dataset_nio_new_car_v15")
        
        train_dates = config['model_config']['train_dates']
        test_dates = config['model_config']['test_dates']
        
        train_data = data_loader.load_dataset(train_dates)
        test_data = data_loader.load_dataset(test_dates)
        
        if quick_mode:
            # 快速模式：只使用部分数据
            train_data = train_data.sample(n=min(5000, len(train_data)), random_state=42)
            test_data = test_data.sample(n=min(1000, len(test_data)), random_state=42)
            logger.info(f"快速模式：使用训练数据{len(train_data)}条，测试数据{len(test_data)}条")
        
        logger.info(f"数据加载完成: 训练集{len(train_data)}条, 测试集{len(test_data)}条")
        
        # 3. 数据预处理
        logger.info("开始数据预处理...")
        preprocessor = DataPreprocessor()
        
        # 处理标签
        train_data = preprocessor.process_purchase_labels(train_data)
        test_data = preprocessor.process_purchase_labels(test_data)
        
        logger.info("数据预处理完成")
        
        # 4. 特征构建
        logger.info("开始特征构建...")
        feature_builder = FeatureBuilder()
        
        # 从配置中获取特征信息
        features_config = config.get('input_modules', {})
        
        # 构建简化的特征配置
        raw_features = {}
        
        # 添加通用特征
        if 'InputGeneral' in features_config:
            for feature in features_config['InputGeneral']['features']:
                if feature in train_data.columns:
                    # 根据数据类型推断特征类型
                    if train_data[feature].dtype in ['object', 'string']:
                        raw_features[feature] = {
                            'type': 'table',
                            'dtype': 'StringLookup'
                        }
                    else:
                        raw_features[feature] = {
                            'type': 'table',
                            'dtype': 'Bucket',
                            'bin_boundarie': [0, 1, 10, 100, 1000]  # 默认分桶
                        }
        
        # 添加场景特征
        if 'InputScene' in features_config:
            for feature in features_config['InputScene']['features']:
                if feature in train_data.columns:
                    raw_features[feature] = {
                        'type': 'table',
                        'dtype': 'StringLookup'
                    }
        
        # 添加序列特征
        if 'InputSeqSet' in features_config:
            seq_info = features_config['InputSeqSet'].get('SetInfo', {})
            for seq_name, seq_config in seq_info.items():
                for feature in seq_config.get('features', []):
                    if feature in train_data.columns:
                        raw_features[feature] = {
                            'type': 'VarLen',
                            'dtype': 'StringLookup',
                            'max_len': 50,
                            'padd_value': '0'
                        }
        
        logger.info(f"构建特征配置: {len(raw_features)}个特征")
        
        # 预处理模型特征
        train_processed = preprocessor.preprocess_model_features(train_data, raw_features)
        test_processed = preprocessor.preprocess_model_features(test_data, raw_features)
        
        # 分割训练数据为训练集和验证集
        train_size = int(0.8 * len(train_processed))
        train_split = train_processed.iloc[:train_size]
        val_split = train_processed.iloc[train_size:]

        # 生成训练数据集
        train_dataset = feature_builder.generate_dataset(
            train_split,
            raw_features,
            label='m_purchase_days_nio_new_car_consum',
            batch_size=config.get('training_config', {}).get('batch_size', 1024)
        )

        # 生成验证数据集
        val_dataset = feature_builder.generate_dataset(
            val_split,
            raw_features,
            label='m_purchase_days_nio_new_car_consum',
            batch_size=config.get('training_config', {}).get('batch_size', 1024)
        )

        # 生成测试特征
        test_features = feature_builder.generate_dataset(
            test_processed,
            raw_features,
            label=None
        )
        
        logger.info("特征构建完成")
        
        # 5. 模型训练
        logger.info("开始模型训练...")
        
        # 创建训练器
        trainer = EnhancedModelTrainer(config, "baseline_test")
        
        # 构建模型
        trainer.build_model(train_dataset.take(1))

        # 训练模型
        training_start = time.time()
        trainer.train(train_dataset, val_dataset, epochs=epochs, patience=10)
        training_time = time.time() - training_start

        # 现在可以安全地获取参数数量
        model_params = trainer.model.count_params()
        logger.info(f"模型训练完成，参数数量: {model_params:,}，耗时: {training_time:.2f}秒")
        
        logger.info(f"模型训练完成，耗时: {training_time:.2f}秒")
        
        # 6. 模型评估
        logger.info("开始模型评估...")
        
        # 预测
        predictions = trainer.model.predict(test_features)
        
        # 评估
        evaluator = ModelEvaluator()
        true_labels = test_processed['m_purchase_days_nio_new_car_consum'].tolist()
        
        # 计算评估指标
        metrics = evaluator.evaluate_predictions(true_labels, predictions)
        
        logger.info("模型评估完成")
        
        # 7. 结果汇总
        total_time = time.time() - start_time
        
        results = {
            'config': 'GOLDEN_CONFIG',
            'timestamp': datetime.now().isoformat(),
            'data_info': {
                'train_samples': len(train_data),
                'test_samples': len(test_data),
                'features_count': len(raw_features),
                'quick_mode': quick_mode
            },
            'model_info': {
                'parameters': model_params,
                'training_time': training_time,
                'epochs': epochs
            },
            'performance': metrics,
            'total_time': total_time
        }
        
        # 输出关键指标
        logger.info("=" * 60)
        logger.info("基线性能结果")
        logger.info("=" * 60)
        logger.info(f"数据规模: 训练{len(train_data)}条, 测试{len(test_data)}条")
        logger.info(f"特征数量: {len(raw_features)}个")
        logger.info(f"模型参数: {model_params:,}")
        logger.info(f"训练时间: {training_time:.2f}秒")
        logger.info(f"总耗时: {total_time:.2f}秒")
        
        if 'month_1_pr_auc' in metrics:
            logger.info(f"Month_1 PR-AUC: {metrics['month_1_pr_auc']:.4f}")
        if 'month_1_recall_840' in metrics:
            logger.info(f"Month_1 Recall@840: {metrics['month_1_recall_840']:.4f}")
        if 'month_1_roc_auc' in metrics:
            logger.info(f"Month_1 ROC-AUC: {metrics['month_1_roc_auc']:.4f}")
        
        # 保存结果
        results_dir = Path('logs/baseline_results')
        results_dir.mkdir(parents=True, exist_ok=True)
        
        results_file = results_dir / f"baseline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"结果已保存: {results_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"基线测试失败: {e}")
        raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="基线性能测试")
    parser.add_argument('--epochs', type=int, default=3, help='训练轮数')
    parser.add_argument('--quick', action='store_true', help='快速模式')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    try:
        results = run_baseline_test(logger, epochs=args.epochs, quick_mode=args.quick)
        
        logger.info("🎉 基线测试完成!")
        return 0
        
    except Exception as e:
        logger.error(f"❌ 基线测试失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
