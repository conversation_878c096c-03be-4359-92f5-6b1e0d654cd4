#!/usr/bin/env python
"""
Scripts目录清理工具

自动整理scripts目录，移除冗余脚本，保留核心功能。
"""

import os
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_scripts():
    """清理scripts目录"""
    script_dir = Path(__file__).parent
    
    # 要保留的脚本
    keep_scripts = {
        'simple_training_test.py',      # 简化训练测试
        'real_training_test.py',        # 真实训练测试  
        'convert_legacy_config.py',     # 配置转换工具
        'compare_models.py',            # 模型比较工具
        'unified_training_test.py',     # 新的统一测试工具
        'cleanup_scripts.py'            # 这个清理脚本本身
    }
    
    # 要删除的脚本（已被整合或不再需要）
    remove_scripts = {
        'run_training_test.py',         # 已被unified_training_test.py整合
        'train_with_embeddings.py',     # 已被unified_training_test.py整合
        'test_new_architecture.py',     # 新架构已验证完成，不再需要
        'migration_guide.py',           # 迁移已完成，不再需要
        'final_integration_test.py',    # 整合已完成，不再需要
        'enhanced_data_pipeline_demo.py', # 演示脚本，不再需要
        'create_test_config.py'         # 临时脚本，不再需要
    }
    
    # 创建备份目录
    backup_dir = script_dir / 'backup_legacy_scripts'
    backup_dir.mkdir(exist_ok=True)
    
    logger.info("🧹 开始清理scripts目录...")
    
    # 备份并删除不需要的脚本
    for script_name in remove_scripts:
        script_path = script_dir / script_name
        if script_path.exists():
            # 备份到backup目录
            backup_path = backup_dir / script_name
            shutil.copy2(script_path, backup_path)
            logger.info(f"   📦 备份: {script_name} -> backup_legacy_scripts/")
            
            # 删除原文件
            script_path.unlink()
            logger.info(f"   🗑️  删除: {script_name}")
    
    # 检查保留的脚本
    logger.info("\n📋 保留的脚本:")
    for script_name in keep_scripts:
        script_path = script_dir / script_name
        if script_path.exists():
            logger.info(f"   ✅ {script_name}")
        else:
            logger.warning(f"   ⚠️  {script_name} (文件不存在)")
    
    # 显示备份的脚本
    logger.info(f"\n📦 备份脚本数量: {len(list(backup_dir.glob('*.py')))}")
    for backup_file in backup_dir.glob('*.py'):
        logger.info(f"   📄 {backup_file.name}")
    
    logger.info("\n🎉 Scripts目录清理完成！")
    logger.info("\n💡 使用建议:")
    logger.info("   - 日常训练测试: python scripts/unified_training_test.py --mode simple")
    logger.info("   - 完整验证: python scripts/unified_training_test.py --mode real")
    logger.info("   - 配置转换: python scripts/convert_legacy_config.py")
    logger.info("   - 模型比较: python scripts/compare_models.py")

if __name__ == "__main__":
    cleanup_scripts()