import json

# 加载基准模型和不同损失函数模型的结果
with open('src/evaluation/baseline_run/evaluation_20250329_233437/sample_20250311_v7-20250311_metrics.json', 'r') as f:
    baseline = json.load(f)

with open('src/evaluation/weighted_loss_run/evaluation_20250330_074743/sample_20250311_v7-20250311_metrics.json', 'r') as f:
    weighted = json.load(f)

with open('src/evaluation/focal_loss_run/evaluation_20250330_080143/sample_20250311_v7-20250311_metrics.json', 'r') as f:
    focal = json.load(f)

# 打印测试集上的月度指标
print('======================= 测试集月度指标比较 =======================')
for month in ['Month_1', 'Month_2', 'Month_3', 'Month_4', 'Month_5', 'Month_6']:
    base_roc = baseline['metrics_month_test'][month]['ROC_AUC']
    weight_roc = weighted['metrics_month_test'][month]['ROC_AUC'] 
    focal_roc = focal['metrics_month_test'][month]['ROC_AUC']
    weight_roc_change = (weight_roc - base_roc) * 100
    focal_roc_change = (focal_roc - base_roc) * 100
    
    base_pr = baseline['metrics_month_test'][month]['PR_AUC']
    weight_pr = weighted['metrics_month_test'][month]['PR_AUC']
    focal_pr = focal['metrics_month_test'][month]['PR_AUC']
    weight_pr_change = (weight_pr - base_pr) * 100
    focal_pr_change = (focal_pr - base_pr) * 100
    
    base_recall = baseline['metrics_month_test'][month]['Recall@840']
    weight_recall = weighted['metrics_month_test'][month]['Recall@840']
    focal_recall = focal['metrics_month_test'][month]['Recall@840']
    weight_recall_change = (weight_recall - base_recall) * 100
    focal_recall_change = (focal_recall - base_recall) * 100
    
    print(f'{month}:')
    print(f'  ROC-AUC:  基准:{base_roc:.4f} vs 加权:{weight_roc:.4f} (变化:{weight_roc_change:.2f}%) vs 焦点:{focal_roc:.4f} (变化:{focal_roc_change:.2f}%)')
    print(f'  PR-AUC:   基准:{base_pr:.4f} vs 加权:{weight_pr:.4f} (变化:{weight_pr_change:.2f}%) vs 焦点:{focal_pr:.4f} (变化:{focal_pr_change:.2f}%)')
    print(f'  Recall:   基准:{base_recall:.4f} vs 加权:{weight_recall:.4f} (变化:{weight_recall_change:.2f}%) vs 焦点:{focal_recall:.4f} (变化:{focal_recall_change:.2f}%)')
    print()

# 打印验证集上的整体指标
print('======================= 验证集整体指标比较 =======================')
base_roc = baseline['metrics_evaluate']['ROC_AUC']
weight_roc = weighted['metrics_evaluate']['ROC_AUC']
focal_roc = focal['metrics_evaluate']['ROC_AUC']
weight_roc_change = (weight_roc - base_roc) * 100
focal_roc_change = (focal_roc - base_roc) * 100

base_pr = baseline['metrics_evaluate']['PR_AUC']
weight_pr = weighted['metrics_evaluate']['PR_AUC']
focal_pr = focal['metrics_evaluate']['PR_AUC']
weight_pr_change = (weight_pr - base_pr) * 100
focal_pr_change = (focal_pr - base_pr) * 100

base_recall = baseline['metrics_evaluate']['Recall@840']
weight_recall = weighted['metrics_evaluate']['Recall@840']
focal_recall = focal['metrics_evaluate']['Recall@840']
weight_recall_change = (weight_recall - base_recall) * 100
focal_recall_change = (focal_recall - base_recall) * 100

print(f'  ROC-AUC:  基准:{base_roc:.4f} vs 加权:{weight_roc:.4f} (变化:{weight_roc_change:.2f}%) vs 焦点:{focal_roc:.4f} (变化:{focal_roc_change:.2f}%)')
print(f'  PR-AUC:   基准:{base_pr:.4f} vs 加权:{weight_pr:.4f} (变化:{weight_pr_change:.2f}%) vs 焦点:{focal_pr:.4f} (变化:{focal_pr_change:.2f}%)')
print(f'  Recall:   基准:{base_recall:.4f} vs 加权:{weight_recall:.4f} (变化:{weight_recall_change:.2f}%) vs 焦点:{focal_recall:.4f} (变化:{focal_recall_change:.2f}%)')
print()

# 打印验证集上的月度指标
print('======================= 验证集月度指标比较 =======================')
for month in ['Month_1', 'Month_2', 'Month_3', 'Month_4', 'Month_5', 'Month_6']:
    base_roc = baseline['metrics_month_evaluate'][month]['ROC_AUC']
    weight_roc = weighted['metrics_month_evaluate'][month]['ROC_AUC'] 
    focal_roc = focal['metrics_month_evaluate'][month]['ROC_AUC']
    weight_roc_change = (weight_roc - base_roc) * 100
    focal_roc_change = (focal_roc - base_roc) * 100
    
    base_pr = baseline['metrics_month_evaluate'][month]['PR_AUC']
    weight_pr = weighted['metrics_month_evaluate'][month]['PR_AUC']
    focal_pr = focal['metrics_month_evaluate'][month]['PR_AUC']
    weight_pr_change = (weight_pr - base_pr) * 100
    focal_pr_change = (focal_pr - base_pr) * 100
    
    base_recall = baseline['metrics_month_evaluate'][month]['Recall@840']
    weight_recall = weighted['metrics_month_evaluate'][month]['Recall@840']
    focal_recall = focal['metrics_month_evaluate'][month]['Recall@840']
    weight_recall_change = (weight_recall - base_recall) * 100
    focal_recall_change = (focal_recall - base_recall) * 100
    
    print(f'{month}:')
    print(f'  ROC-AUC:  基准:{base_roc:.4f} vs 加权:{weight_roc:.4f} (变化:{weight_roc_change:.2f}%) vs 焦点:{focal_roc:.4f} (变化:{focal_roc_change:.2f}%)')
    print(f'  PR-AUC:   基准:{base_pr:.4f} vs 加权:{weight_pr:.4f} (变化:{weight_pr_change:.2f}%) vs 焦点:{focal_pr:.4f} (变化:{focal_pr_change:.2f}%)')
    print(f'  Recall:   基准:{base_recall:.4f} vs 加权:{weight_recall:.4f} (变化:{weight_recall_change:.2f}%) vs 焦点:{focal_recall:.4f} (变化:{focal_recall_change:.2f}%)')
    print() 