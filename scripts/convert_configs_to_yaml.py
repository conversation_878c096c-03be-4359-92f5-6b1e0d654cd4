#!/usr/bin/env python
"""
配置文件转换脚本 - 将JSON配置转换为标准化YAML格式

功能：
1. 转换models/*.json到experiments/*.yaml
2. 整理features/目录中的分块文件
3. 清理临时文件
4. 标准化目录结构
"""

import json
import yaml
import sys
from pathlib import Path
import logging
from datetime import datetime

# 添加项目根路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.configs.unified_config_manager import unified_config_manager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfigConverter:
    """配置转换器"""
    
    def __init__(self):
        self.config_root = Path("src/configs")
        self.backup_dir = self.config_root / "backup_legacy"
        self.backup_dir.mkdir(exist_ok=True)
        
    def convert_all_configs(self):
        """转换所有配置文件"""
        logger.info("🔄 开始配置文件转换和整理...")
        
        # 1. 转换JSON模型配置
        self._convert_json_model_configs()
        
        # 2. 整理特征文件
        self._consolidate_feature_files()
        
        # 3. 清理临时文件
        self._cleanup_temporary_files()
        
        # 4. 更新目录结构
        self._reorganize_directory_structure()
        
        logger.info("✅ 配置文件转换和整理完成！")
    
    def _convert_json_model_configs(self):
        """转换JSON模型配置到YAML"""
        logger.info("📄 转换JSON模型配置...")
        
        models_dir = self.config_root / "models"
        if not models_dir.exists():
            logger.warning("models目录不存在")
            return
        
        for json_file in models_dir.glob("*.json"):
            logger.info(f"转换 {json_file.name}...")
            
            try:
                # 读取JSON配置
                with open(json_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 使用统一配置管理器转换
                experiment_name = json_file.stem
                experiment_config = unified_config_manager._convert_from_legacy(
                    config_data, experiment_name
                )
                
                # 保存为YAML
                yaml_file = unified_config_manager.experiments_dir / f"{experiment_name}.yaml"
                unified_config_manager.save_experiment_config(
                    experiment_config, experiment_name
                )
                
                # 备份原JSON文件
                backup_path = self.backup_dir / json_file.name
                json_file.rename(backup_path)
                
                logger.info(f"✅ {json_file.name} -> {yaml_file.name}")
                
            except Exception as e:
                logger.error(f"❌ 转换失败 {json_file.name}: {e}")
    
    def _consolidate_feature_files(self):
        """整理特征文件"""
        logger.info("📋 整理特征文件...")
        
        features_dir = self.config_root / "features"
        
        # 合并分块的特征文件
        chunk_files = list(features_dir.glob("*chunk*.yaml"))
        if chunk_files:
            logger.info(f"发现 {len(chunk_files)} 个分块文件，正在合并...")
            
            all_features = {}
            for chunk_file in sorted(chunk_files):
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunk_data = yaml.safe_load(f)
                    if isinstance(chunk_data, dict):
                        all_features.update(chunk_data)
                
                # 备份分块文件
                backup_path = self.backup_dir / chunk_file.name
                chunk_file.rename(backup_path)
            
            # 保存合并后的特征文件
            consolidated_file = features_dir / "nio_v7_consolidated_features.yaml"
            with open(consolidated_file, 'w', encoding='utf-8') as f:
                yaml.dump(all_features, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"✅ 合并了 {len(chunk_files)} 个分块文件到 {consolidated_file.name}")
    
    def _cleanup_temporary_files(self):
        """清理临时文件"""
        logger.info("🧹 清理临时文件...")
        
        cleanup_patterns = [
            "*nio_v7_converted*",
            "*.md"  # 清理临时的使用指南
        ]
        
        cleaned_count = 0
        for pattern in cleanup_patterns:
            for temp_file in self.config_root.rglob(pattern):
                if temp_file.is_file() and "backup" not in str(temp_file):
                    # 备份而不是删除
                    backup_path = self.backup_dir / temp_file.name
                    temp_file.rename(backup_path)
                    cleaned_count += 1
        
        logger.info(f"✅ 清理了 {cleaned_count} 个临时文件")
    
    def _reorganize_directory_structure(self):
        """重组目录结构"""
        logger.info("📁 重组目录结构...")
        
        # 确保统一的目录结构存在
        standard_dirs = [
            "foundation",   # 基础配置层
            "business",     # 业务配置层 
            "experiments",  # 实验配置层
            "features",     # 特征定义
            "backup_legacy" # 备份目录
        ]
        
        for dir_name in standard_dirs:
            dir_path = self.config_root / dir_name
            dir_path.mkdir(exist_ok=True)
        
        # 如果datasets目录存在，将其内容移动到experiments
        datasets_dir = self.config_root / "datasets"
        if datasets_dir.exists():
            logger.info("合并datasets目录到experiments...")
            
            for dataset_file in datasets_dir.glob("*"):
                if dataset_file.is_file():
                    backup_path = self.backup_dir / dataset_file.name
                    dataset_file.rename(backup_path)
            
            # 删除空的datasets目录
            try:
                datasets_dir.rmdir()
                logger.info("✅ 删除了空的datasets目录")
            except OSError:
                logger.warning("⚠️  datasets目录不为空，保留")
    
    def generate_migration_report(self):
        """生成迁移报告"""
        logger.info("📊 生成迁移报告...")
        
        report = {
            "migration_time": datetime.now().isoformat(),
            "directory_structure": self._analyze_current_structure(),
            "file_summary": self._count_files_by_type(),
            "recommendations": self._generate_recommendations()
        }
        
        report_file = Path("logs") / f"config_migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            yaml.dump(report, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"📄 迁移报告已保存: {report_file}")
        return report_file
    
    def _analyze_current_structure(self):
        """分析当前目录结构"""
        structure = {}
        for item in self.config_root.iterdir():
            if item.is_dir():
                file_count = len(list(item.glob("*")))
                structure[item.name] = {
                    "type": "directory",
                    "file_count": file_count
                }
            else:
                structure[item.name] = {
                    "type": "file",
                    "size": item.stat().st_size
                }
        return structure
    
    def _count_files_by_type(self):
        """按类型统计文件数量"""
        counts = {
            "yaml_files": len(list(self.config_root.rglob("*.yaml"))),
            "json_files": len(list(self.config_root.rglob("*.json"))),
            "python_files": len(list(self.config_root.rglob("*.py"))),
            "backup_files": len(list(self.backup_dir.glob("*"))) if self.backup_dir.exists() else 0
        }
        return counts
    
    def _generate_recommendations(self):
        """生成改进建议"""
        return [
            "所有JSON配置已转换为YAML格式",
            "临时文件已清理并备份到backup_legacy目录",
            "目录结构已标准化为三层架构",
            "建议定期清理backup_legacy目录中的过期文件",
            "可以开始使用unified_config_manager.py管理配置"
        ]

def main():
    """主函数"""
    converter = ConfigConverter()
    
    try:
        converter.convert_all_configs()
        report_file = converter.generate_migration_report()
        
        print("🎉 配置迁移成功完成！")
        print(f"📄 详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ 配置迁移失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())