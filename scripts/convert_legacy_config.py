"""
配置转换工具 - 将4135行的巨大JSON配置自动转换为分层结构

使用方法：
python convert_legacy_config.py --input src/configs/models/sample_20250311_v7-20250311.json --output experiment_v7
"""

import json
import yaml
import argparse
from pathlib import Path
from typing import Dict, Any, List
import logging
from collections import defaultdict

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LegacyConfigConverter:
    """旧配置转换器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 特征分类规则
        self.feature_category_rules = {
            "behavioral": [
                "cnt", "visit", "view", "search", "buy", "checkin", "collect",
                "exp", "sign_up", "del", "save", "answer", "fl_"
            ],
            "demographic": [
                "age_group", "gender", "resident_city", "career_type", 
                "user_identity", "is_nio_employee"
            ],
            "contextual": [
                "intention", "decision_maker", "stage", "confirm", "test_drive"
            ],
            "sequential": [
                "_seq", "action_code", "action_day", "action_veh_model"
            ],
            "temporal": [
                "_DSLA", "first_reg", "unfirst_reg"
            ]
        }
    
    def convert_config(self, input_path: str, experiment_name: str, output_dir: str = "src/configs"):
        """
        转换配置文件
        
        Args:
            input_path: 输入的JSON配置文件路径
            experiment_name: 实验名称
            output_dir: 输出目录
        """
        logger.info(f"🔄 开始转换配置文件：{input_path}")
        
        # 加载原配置
        with open(input_path, 'r', encoding='utf-8') as f:
            legacy_config = json.load(f)
        
        logger.info(f"📊 原配置文件统计：")
        logger.info(f"   - 总特征数：{len(legacy_config.get('RawFeature', {}))}")
        logger.info(f"   - InputGeneral特征数：{len(legacy_config.get('InputGeneral', {}).get('features', []))}")
        logger.info(f"   - InputScene特征数：{len(legacy_config.get('InputScene', {}).get('features', []))}")
        logger.info(f"   - 序列集合数：{len(legacy_config.get('InputSeqSet', {}).get('Set', []))}")
        
        # 创建输出目录结构
        output_path = Path(output_dir)
        self._create_directory_structure(output_path)
        
        # 1. 提取并保存架构配置
        arch_config = self._extract_architecture_config(legacy_config, experiment_name)
        self._save_architecture_config(arch_config, experiment_name, output_path)
        
        # 2. 提取并保存数据配置
        data_config = self._extract_data_config(legacy_config, experiment_name)
        self._save_data_config(data_config, experiment_name, output_path)
        
        # 3. 提取并分组特征配置
        feature_groups, feature_definitions = self._extract_and_group_features(legacy_config)
        self._save_feature_configs(feature_groups, feature_definitions, experiment_name, output_path)
        
        # 4. 创建实验配置
        experiment_config = self._create_experiment_config(
            legacy_config, experiment_name, arch_config, data_config, feature_groups)
        self._save_experiment_config(experiment_config, experiment_name, output_path)
        
        # 5. 生成使用示例
        self._generate_usage_examples(experiment_name, feature_groups, output_path)
        
        logger.info(f"✅ 配置转换完成！新配置保存在：{output_path}")
        
        return {
            "experiment_name": experiment_name,
            "architecture_config": arch_config,
            "data_config": data_config,
            "feature_groups": feature_groups,
            "total_features": len(feature_definitions)
        }
    
    def _create_directory_structure(self, output_path: Path):
        """创建目录结构"""
        for subdir in ["architectures", "data", "features", "experiments"]:
            (output_path / subdir).mkdir(parents=True, exist_ok=True)
    
    def _extract_architecture_config(self, legacy_config: Dict[str, Any], experiment_name: str) -> Dict[str, Any]:
        """提取架构配置"""
        arch_config = {
            "network_name": legacy_config.get("network_name", "EPMMOENet"),
            "output_dimension": legacy_config.get("output_dimension", 6),
            "output_activation": legacy_config.get("output_activation", "sigmoid"),
            
            # 基础参数
            "default_embedding_dimension": 8,
            "default_gru_dimension": 32,
            "expert_num": 8,
            
            # 架构开关
            "use_cross_layer": True,
            "use_multitask": bool(legacy_config.get("mask_label")),
            "use_mixed_precision": True,
            "use_time_attention": True,
            "time_decay_factor": 0.05,
            
            # 训练参数
            "loss_type": "standard",
            "pos_weight": 10.0,
            "use_month_weights": False,
            "val_metric": "loss",
            
            # 元信息
            "description": f"从{experiment_name}转换的架构配置",
            "version": "converted_v1.0",
            "converted_from": "legacy_json",
            "conversion_date": "2025-01-13"
        }
        
        # 如果有mask_label，启用多任务学习
        if legacy_config.get("mask_label"):
            arch_config["mask_label"] = legacy_config["mask_label"]
            arch_config["use_multitask"] = True
        
        return arch_config
    
    def _extract_data_config(self, legacy_config: Dict[str, Any], experiment_name: str) -> Dict[str, Any]:
        """提取数据配置"""
        data_config = {
            "train_dates": legacy_config.get("train_dates", ["20240430"]),
            "test_dates": legacy_config.get("test_dates", ["20240531"]),
            "data_format": "parquet",
            "partition_column": "datetime",
            "data_root": f"data/dataset_nio_new_car_v15",
            
            # 预处理参数
            "preprocessing": {
                "missing_value_strategy": "fill_default",
                "categorical_missing_token": "<UNK>",
                "numerical_missing_value": 0,
                "remove_duplicates": True,
                "outlier_detection": True,
                "outlier_threshold": 3.0,
                "min_category_frequency": 10,
                "max_categories_per_feature": 10000,
                "numerical_scaling": "none",
                "sequence_padding": "post",
                "sequence_truncating": "post",
                "default_sequence_length": 50
            },
            
            # 标签配置
            "mask_label": legacy_config.get("mask_label"),
            "target_labels": list(legacy_config.get("RawLabel", {}).keys()),
            
            # 数据分割
            "validation_split": 0.2,
            "random_seed": 42,
            "batch_loading": True,
            "num_parallel_reads": 4,
            
            # 元信息
            "description": f"从{experiment_name}转换的数据配置",
            "version": "converted_v1.0",
            "conversion_date": "2025-01-13"
        }
        
        return data_config
    
    def _extract_and_group_features(self, legacy_config: Dict[str, Any]) -> tuple:
        """提取并分组特征"""
        raw_features = legacy_config.get("RawFeature", {})
        input_general = legacy_config.get("InputGeneral", {}).get("features", [])
        input_scene = legacy_config.get("InputScene", {}).get("features", [])
        input_seqset_info = legacy_config.get("InputSeqSet", {}).get("SetInfo", {})
        
        # 按类别分组特征
        feature_groups = defaultdict(list)
        
        # 分类InputGeneral中的特征
        for feature in input_general:
            category = self._classify_feature(feature)
            feature_groups[f"general_{category}"].append(feature)
        
        # 分类InputScene中的特征
        for feature in input_scene:
            category = self._classify_feature(feature)
            feature_groups[f"scene_{category}"].append(feature)
        
        # 处理序列特征
        for seq_name, seq_info in input_seqset_info.items():
            seq_features = seq_info.get("features", [])
            feature_groups[f"sequence_{seq_name}"] = seq_features
        
        # 创建特征组配置
        feature_group_config = {}
        for group_name, features in feature_groups.items():
            if features:  # 只包含非空的组
                # 推断分类
                if "sequence" in group_name:
                    category = "sequential"
                elif "scene" in group_name:
                    category = "contextual"
                else:
                    # 从组名中提取类别
                    category = group_name.split("_")[-1] if "_" in group_name else "behavioral"
                
                feature_group_config[group_name] = {
                    "description": f"从legacy配置提取的{group_name}特征组",
                    "category": category,
                    "features": features
                }
        
        # 保留完整的特征定义
        feature_definitions = raw_features
        
        logger.info(f"📝 特征分组结果：")
        for group_name, group_info in feature_group_config.items():
            logger.info(f"   - {group_name}: {len(group_info['features'])} 个特征 ({group_info['category']})")
        
        return feature_group_config, feature_definitions
    
    def _classify_feature(self, feature_name: str) -> str:
        """根据特征名称分类特征"""
        feature_lower = feature_name.lower()
        
        for category, keywords in self.feature_category_rules.items():
            if any(keyword in feature_lower for keyword in keywords):
                return category
        
        return "behavioral"  # 默认分类
    
    def _save_architecture_config(self, arch_config: Dict[str, Any], 
                                experiment_name: str, output_path: Path):
        """保存架构配置"""
        arch_file = output_path / "architectures" / f"{experiment_name}_arch.yaml"
        with open(arch_file, 'w', encoding='utf-8') as f:
            yaml.dump(arch_config, f, default_flow_style=False, allow_unicode=True)
        logger.info(f"💾 架构配置已保存：{arch_file}")
    
    def _save_data_config(self, data_config: Dict[str, Any], 
                         experiment_name: str, output_path: Path):
        """保存数据配置"""
        data_file = output_path / "data" / f"{experiment_name}_data.yaml"
        with open(data_file, 'w', encoding='utf-8') as f:
            yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)
        logger.info(f"💾 数据配置已保存：{data_file}")
    
    def _save_feature_configs(self, feature_groups: Dict[str, Any], 
                            feature_definitions: Dict[str, Any],
                            experiment_name: str, output_path: Path):
        """保存特征配置"""
        # 保存特征组定义
        groups_file = output_path / "features" / f"{experiment_name}_feature_groups.yaml"
        with open(groups_file, 'w', encoding='utf-8') as f:
            yaml.dump(feature_groups, f, default_flow_style=False, allow_unicode=True)
        logger.info(f"💾 特征组配置已保存：{groups_file}")
        
        # 保存特征定义（分块保存，避免单个文件过大）
        chunk_size = 100
        feature_items = list(feature_definitions.items())
        
        for i in range(0, len(feature_items), chunk_size):
            chunk = dict(feature_items[i:i + chunk_size])
            chunk_file = output_path / "features" / f"{experiment_name}_features_chunk_{i//chunk_size + 1}.yaml"
            with open(chunk_file, 'w', encoding='utf-8') as f:
                yaml.dump(chunk, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"💾 特征定义已分块保存：{len(feature_items)} 个特征，{len(feature_items)//chunk_size + 1} 个文件")
    
    def _create_experiment_config(self, legacy_config: Dict[str, Any],
                                experiment_name: str, arch_config: Dict[str, Any],
                                data_config: Dict[str, Any], 
                                feature_groups: Dict[str, Any]) -> Dict[str, Any]:
        """创建实验配置"""
        experiment_config = {
            "experiment_name": experiment_name,
            "description": f"从legacy JSON转换的实验配置：{experiment_name}",
            "version": "converted_v1.0",
            "conversion_date": "2025-01-13",
            
            # 引用其他配置文件
            "architecture_config": f"{experiment_name}_arch.yaml",
            "data_config": f"{experiment_name}_data.yaml",
            "feature_groups_config": f"{experiment_name}_feature_groups.yaml",
            
            # 实验特定配置
            "feature_groups_to_use": list(feature_groups.keys()),
            "training_params": {
                "epochs": 50,
                "batch_size": 4096,
                "patience": 10,
                "learning_rate": 0.0005
            },
            
            # 原配置的关键信息
            "legacy_info": {
                "original_file": "sample_20250311_v7-20250311.json",
                "total_features": len(legacy_config.get("RawFeature", {})),
                "network_name": legacy_config.get("network_name"),
                "train_dates": legacy_config.get("train_dates"),
                "test_dates": legacy_config.get("test_dates")
            }
        }
        
        return experiment_config
    
    def _save_experiment_config(self, experiment_config: Dict[str, Any],
                              experiment_name: str, output_path: Path):
        """保存实验配置"""
        exp_file = output_path / "experiments" / f"{experiment_name}.yaml"
        with open(exp_file, 'w', encoding='utf-8') as f:
            yaml.dump(experiment_config, f, default_flow_style=False, allow_unicode=True)
        logger.info(f"💾 实验配置已保存：{exp_file}")
    
    def _generate_usage_examples(self, experiment_name: str, 
                               feature_groups: Dict[str, Any], output_path: Path):
        """生成使用示例"""
        usage_examples = f'''# {experiment_name} 使用示例

## 1. 使用分层配置创建模型

```python
from src.configs.config_manager_enhanced import config_manager

# 创建分层配置
config = config_manager.create_layered_config(
    architecture="{experiment_name}_arch",
    feature_groups={list(feature_groups.keys())[:3]},  # 使用前3个特征组作为示例
    data_config="{experiment_name}_data",
    experiment_name="{experiment_name}_experiment"
)

# 使用新的模型工厂创建模型
from src.models.model_factory import create_model
model = create_model("EPMMOENet_Enhanced", config.model_config)
```

## 2. 使用增强版训练器

```python
from src.training.enhanced_trainer import EnhancedModelTrainer

trainer = EnhancedModelTrainer(config.model_config, "{experiment_name}_run")
model = trainer.build_model(feature_dict)
trainer.train(train_dataset, val_dataset, epochs=50)
```

## 3. 可用的特征组

{chr(10).join([f"- {name}: {info['description']} ({len(info['features'])} 个特征)" for name, info in feature_groups.items()])}

## 4. 配置文件结构

```
src/configs/
├── architectures/{experiment_name}_arch.yaml     # 架构配置
├── data/{experiment_name}_data.yaml              # 数据配置  
├── features/{experiment_name}_feature_groups.yaml # 特征组配置
├── features/{experiment_name}_features_chunk_*.yaml # 特征定义（分块）
└── experiments/{experiment_name}.yaml             # 实验配置
```

## 5. 迁移对比

### 原方式（问题）：
- 单个4135行的JSON文件
- 架构、特征、数据配置混杂
- 难以维护和版本管理
- 团队协作冲突频繁

### 新方式（解决）：
- 按职责分离的多个小文件
- 清晰的配置层次结构
- 易于维护和扩展
- 支持配置继承和组合
'''
        
        usage_file = output_path / f"{experiment_name}_usage_guide.md"
        with open(usage_file, 'w', encoding='utf-8') as f:
            f.write(usage_examples)
        logger.info(f"📚 使用指南已生成：{usage_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="转换legacy配置文件到分层结构")
    parser.add_argument("--input", "-i", required=True, 
                       help="输入的JSON配置文件路径")
    parser.add_argument("--output", "-o", required=True,
                       help="实验名称（用作输出文件前缀）")
    parser.add_argument("--config-dir", "-c", default="src/configs",
                       help="配置输出目录")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.input).exists():
        logger.error(f"❌ 输入文件不存在：{args.input}")
        return 1
    
    try:
        # 执行转换
        converter = LegacyConfigConverter()
        result = converter.convert_config(args.input, args.output, args.config_dir)
        
        # 输出转换结果
        logger.info("🎉 转换成功完成！")
        logger.info("📊 转换结果统计：")
        logger.info(f"   - 实验名称：{result['experiment_name']}")
        logger.info(f"   - 特征组数：{len(result['feature_groups'])}")
        logger.info(f"   - 总特征数：{result['total_features']}")
        logger.info(f"   - 架构类型：{result['architecture_config']['network_name']}")
        
        logger.info(f"\\n🎯 下一步操作：")
        logger.info(f"1. 查看生成的使用指南：{args.config_dir}/{args.output}_usage_guide.md")
        logger.info(f"2. 检查分层配置文件：{args.config_dir}/")
        logger.info(f"3. 使用新配置进行实验")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 转换失败：{e}")
        return 1

if __name__ == "__main__":
    exit(main())