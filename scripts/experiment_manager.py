#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实验管理工具 - 维护最优配置和管理实验流程
"""
import json
import yaml
import os
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
import argparse


class ExperimentManager:
    """实验管理器 - 负责最优配置维护和实验流程管理"""
    
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.config_dir = self.base_dir / "src/configs/models"
        self.golden_config_path = self.config_dir / "GOLDEN_CONFIG.yaml"
        self.experiments_dir = self.config_dir / "experiments"
        self.archived_dir = self.config_dir / "archived"
        
        # 确保目录存在
        self.experiments_dir.mkdir(exist_ok=True)
        self.archived_dir.mkdir(exist_ok=True)
    
    def get_golden_config(self):
        """获取当前最优配置"""
        if not self.golden_config_path.exists():
            raise FileNotFoundError("Golden config not found!")
        
        with open(self.golden_config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def create_experiment(self, experiment_name, description=""):
        """创建新实验配置（基于golden config的副本）"""
        golden_config = self.get_golden_config()
        
        # 移除metadata用于实验
        experiment_config = {k: v for k, v in golden_config.items() if k != '_metadata'}
        
        # 添加实验metadata
        experiment_config['_experiment_metadata'] = {
            "experiment_name": experiment_name,
            "description": description,
            "created_from": golden_config.get('_metadata', {}).get('version', 'unknown'),
            "created_at": datetime.now().isoformat(),
            "status": "CREATED"
        }
        
        experiment_path = self.experiments_dir / f"{experiment_name}.yaml"
        with open(experiment_path, 'w', encoding='utf-8') as f:
            yaml.dump(experiment_config, f, allow_unicode=True, default_flow_style=False, indent=2)
        
        print(f"✅ 实验配置已创建: {experiment_path}")
        print(f"📝 描述: {description}")
        return experiment_path
    
    def run_experiment(self, experiment_name, epochs=3, dry_run=False):
        """运行实验"""
        experiment_path = self.experiments_dir / f"{experiment_name}.yaml"
        if not experiment_path.exists():
            raise FileNotFoundError(f"实验配置不存在: {experiment_path}")
        
        print(f"🧪 开始运行实验: {experiment_name}")
        
        if dry_run:
            print("🔍 DRY RUN模式 - 不执行实际训练")
            return None
        
        # 构建训练命令
        cmd = [
            "python", "src/train.py",
            f"--model_code={experiment_name}",
            f"--epochs={epochs}",
            "--batch_size=1024",
            "--data_dir=data",
            f"--run_name=exp_{experiment_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        ]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        # 执行训练
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.base_dir)
            if result.returncode == 0:
                print("✅ 实验训练完成")
                return self._extract_metrics_from_output(result.stdout)
            else:
                print(f"❌ 实验训练失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"❌ 实验执行异常: {e}")
            return None
    
    def compare_with_golden(self, experiment_results):
        """对比实验结果与golden config"""
        if not experiment_results:
            return False, "无实验结果"
        
        golden_config = self.get_golden_config()
        golden_performance = golden_config.get('_metadata', {}).get('performance', {})
        
        current_pr_auc = golden_performance.get('month_1_pr_auc', 0)
        experiment_pr_auc = experiment_results.get('month_1_pr_auc', 0)
        
        improvement = experiment_pr_auc - current_pr_auc
        
        print(f"📊 性能对比:")
        print(f"   Golden PR_AUC: {current_pr_auc:.4f}")
        print(f"   实验 PR_AUC:   {experiment_pr_auc:.4f}")
        print(f"   改进幅度:      {improvement:+.4f}")
        
        is_better = improvement > 0.001  # 至少改进0.1%
        return is_better, f"改进 {improvement:+.4f}" if is_better else f"下降 {improvement:.4f}"
    
    def promote_experiment(self, experiment_name, experiment_results):
        """将成功的实验提升为golden config"""
        experiment_path = self.experiments_dir / f"{experiment_name}.yaml"
        
        # 备份当前golden config
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        golden_config = self.get_golden_config()
        backup_path = self.archived_dir / f"golden_backup_{timestamp}.yaml"
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            yaml.dump(golden_config, f, allow_unicode=True, default_flow_style=False, indent=2)
        
        # 读取实验配置
        with open(experiment_path, 'r', encoding='utf-8') as f:
            experiment_config = yaml.safe_load(f)
        
        # 移除实验metadata，添加golden metadata
        if '_experiment_metadata' in experiment_config:
            del experiment_config['_experiment_metadata']
        
        experiment_config['_metadata'] = {
            "version": f"v1.{len(list(self.archived_dir.glob('golden_backup_*.yaml')))}_{experiment_name}",
            "creation_date": datetime.now().strftime("%Y-%m-%d"),
            "performance": experiment_results,
            "source_config": f"{experiment_name}.yaml",
            "description": f"Promoted from experiment {experiment_name}",
            "last_updated": datetime.now().isoformat(),
            "validation_status": "VALIDATED"
        }
        
        # 更新golden config
        with open(self.golden_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(experiment_config, f, allow_unicode=True, default_flow_style=False, indent=2)
        
        print(f"🏆 实验 {experiment_name} 已提升为Golden Config!")
        print(f"📦 原Golden Config已备份至: {backup_path}")
        
        # 归档实验配置
        archived_exp_path = self.archived_dir / f"experiment_{experiment_name}_{timestamp}.yaml"
        shutil.move(str(experiment_path), str(archived_exp_path))
        print(f"📁 实验配置已归档至: {archived_exp_path}")
    
    def cleanup_failed_experiments(self):
        """清理失败的实验配置"""
        experiments = list(self.experiments_dir.glob("*.yaml"))
        archived_count = 0
        
        for exp_path in experiments:
            # 将失败实验移至归档
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archived_path = self.archived_dir / f"failed_{exp_path.stem}_{timestamp}.yaml"
            shutil.move(str(exp_path), str(archived_path))
            archived_count += 1
        
        print(f"🧹 已清理 {archived_count} 个实验配置至归档目录")
    
    def _extract_metrics_from_output(self, output):
        """从训练输出中提取指标（简化版本）"""
        # 这里应该解析实际的输出，暂时返回模拟数据
        # 实际实现需要解析evaluation结果
        try:
            # 寻找关键指标
            lines = output.split('\n')
            for line in lines:
                if "Month_1: ROC-AUC" in line and "PR-AUC" in line:
                    # 解析 "Month_1: ROC-AUC = 0.8691, PR-AUC = 0.2426" 格式
                    parts = line.split("PR-AUC = ")
                    if len(parts) > 1:
                        pr_auc = float(parts[1].strip())
                        return {
                            "month_1_pr_auc": pr_auc,
                            "extraction_method": "log_parsing"
                        }
        except:
            pass
        
        return {"month_1_pr_auc": 0.0, "extraction_method": "failed"}
    
    def list_experiments(self):
        """列出所有实验"""
        print("\n📋 当前实验状态:")
        
        # Golden config
        golden = self.get_golden_config()
        golden_meta = golden.get('_metadata', {})
        print(f"🏆 Golden Config: {golden_meta.get('version', 'unknown')}")
        print(f"   性能: PR_AUC = {golden_meta.get('performance', {}).get('month_1_pr_auc', 'N/A')}")
        
        # 进行中的实验
        experiments = list(self.experiments_dir.glob("*.yaml"))
        if experiments:
            print(f"\n🧪 进行中的实验 ({len(experiments)}):")
            for exp_path in experiments:
                print(f"   - {exp_path.stem}")
        
        # 归档的实验
        archived = list(self.archived_dir.glob("*.yaml"))
        if archived:
            print(f"\n📁 归档配置 ({len(archived)}):")
            for arch_path in sorted(archived)[-5:]:  # 显示最近5个
                print(f"   - {arch_path.stem}")


def main():
    parser = argparse.ArgumentParser(description="实验管理工具")
    parser.add_argument('action', choices=['create', 'run', 'compare', 'promote', 'cleanup', 'list'])
    parser.add_argument('--name', help='实验名称')
    parser.add_argument('--description', default='', help='实验描述')
    parser.add_argument('--epochs', type=int, default=3, help='训练轮数')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式')
    
    args = parser.parse_args()
    
    em = ExperimentManager()
    
    if args.action == 'create':
        if not args.name:
            print("❌ 请提供实验名称: --name")
            return
        em.create_experiment(args.name, args.description)
    
    elif args.action == 'run':
        if not args.name:
            print("❌ 请提供实验名称: --name")
            return
        results = em.run_experiment(args.name, args.epochs, args.dry_run)
        if results:
            is_better, message = em.compare_with_golden(results)
            print(f"📈 结果: {message}")
            
            if is_better:
                confirm = input("🤔 是否提升为Golden Config? (y/N): ")
                if confirm.lower() == 'y':
                    em.promote_experiment(args.name, results)
    
    elif args.action == 'cleanup':
        em.cleanup_failed_experiments()
    
    elif args.action == 'list':
        em.list_experiments()


if __name__ == "__main__":
    main()