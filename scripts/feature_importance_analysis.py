#!/usr/bin/env python3
"""
特征重要性分析脚本
验证特征选择 vs 特征工程的效果差异
"""

import pandas as pd
import numpy as np
import json
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureImportanceAnalyzer:
    """特征重要性分析器"""
    
    def __init__(self):
        self.feature_scores = {}
        self.label_encoders = {}
        
    def load_and_preprocess_data(self, config_path, dataset_code="dataset_nio_new_car_v15"):
        """加载和预处理数据"""
        logger.info(f"Loading configuration: {config_path}")
        
        # 加载配置
        with open(config_path, 'r') as f:
            config = json.load(f)
            
        # 加载数据
        data_loader = DataLoader(dataset_code)
        df_raw = data_loader.load_dataset(
            dates=config['train_dates'] + config['test_dates'],
            data_dir="data"
        )
        
        # 数据预处理
        preprocessor = DataPreprocessor()
        df_processed = preprocessor.preprocess_features(df_raw, config)
        df_processed = preprocessor.process_labels(
            df_processed, 
            'purchase_days_nio_new_car_total', 
            'm_purchase_days_nio_new_car'
        )
        
        # 提取特征和标签
        feature_cols = []
        for module, info in config.items():
            if isinstance(info, dict) and 'features' in info:
                feature_cols.extend(info['features'])
        
        # 移除不存在的特征列
        available_features = [col for col in feature_cols if col in df_processed.columns]
        logger.info(f"Available features: {len(available_features)}/{len(feature_cols)}")
        
        X = df_processed[available_features]
        y = df_processed['m_purchase_days_nio_new_car'].iloc[:, 0]  # Month_1标签
        
        return X, y, available_features
    
    def encode_categorical_features(self, X):
        """编码分类特征"""
        X_encoded = X.copy()
        
        for col in X_encoded.columns:
            if X_encoded[col].dtype == 'object':
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    X_encoded[col] = self.label_encoders[col].fit_transform(X_encoded[col].astype(str))
                else:
                    X_encoded[col] = self.label_encoders[col].transform(X_encoded[col].astype(str))
                    
        return X_encoded
    
    def calculate_statistical_importance(self, X, y):
        """计算统计特征重要性"""
        logger.info("Calculating statistical feature importance...")
        
        X_encoded = self.encode_categorical_features(X)
        
        # F-statistics
        f_scores, f_pvalues = f_classif(X_encoded, y)
        
        # Mutual Information
        mi_scores = mutual_info_classif(X_encoded, y, random_state=42)
        
        # 存储结果
        self.feature_scores['f_statistics'] = dict(zip(X.columns, f_scores))
        self.feature_scores['f_pvalues'] = dict(zip(X.columns, f_pvalues))
        self.feature_scores['mutual_info'] = dict(zip(X.columns, mi_scores))
        
        return self.feature_scores
    
    def calculate_tree_importance(self, X, y):
        """计算基于树模型的特征重要性"""
        logger.info("Calculating tree-based feature importance...")
        
        X_encoded = self.encode_categorical_features(X)
        
        # Random Forest特征重要性
        rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X_encoded, y)
        
        self.feature_scores['rf_importance'] = dict(zip(X.columns, rf.feature_importances_))
        
        return self.feature_scores
    
    def get_top_features(self, method='rf_importance', k=50):
        """获取top-k重要特征"""
        if method not in self.feature_scores:
            raise ValueError(f"Method {method} not calculated yet")
            
        scores = self.feature_scores[method]
        sorted_features = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        return [feat for feat, score in sorted_features[:k]]
    
    def save_feature_analysis(self, output_path):
        """保存特征分析结果"""
        analysis_results = {
            'feature_scores': self.feature_scores,
            'top_features': {
                'rf_top_30': self.get_top_features('rf_importance', 30),
                'rf_top_50': self.get_top_features('rf_importance', 50),
                'mi_top_30': self.get_top_features('mutual_info', 30),
                'mi_top_50': self.get_top_features('mutual_info', 50),
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Feature analysis saved to: {output_path}")
        
    def create_optimized_config(self, base_config_path, output_config_path, method='rf_importance', k=50):
        """基于特征重要性创建优化的配置文件"""
        # 加载基础配置
        with open(base_config_path, 'r') as f:
            config = json.load(f)
            
        # 获取top-k特征
        top_features = self.get_top_features(method, k)
        
        # 重新分配特征到不同模块
        config['InputGeneral']['features'] = [f for f in top_features if not f.startswith('intention_')]
        config['InputScene']['features'] = [f for f in top_features if f.startswith('intention_')] + \
                                         ['user_create_days', 'user_register_days']
        
        # 如果Scene特征太少，从General中补充一些
        if len(config['InputScene']['features']) < 3:
            config['InputScene']['features'].extend([
                f for f in top_features[:10] 
                if f not in config['InputScene']['features']
            ][:3])
            
        # 更新特征配置
        config['InputSeqSet'] = {"Set": [], "SetInfo": {}}
        
        # 保存优化配置
        with open(output_config_path, 'w') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Optimized config saved to: {output_config_path}")
        logger.info(f"Selected {len(top_features)} features using {method}")


def main():
    """主函数"""
    analyzer = FeatureImportanceAnalyzer()
    
    # 分析100特征版本的重要性
    config_path = "src/configs/models/enhanced_100_features.json"
    
    try:
        # 加载数据
        X, y, features = analyzer.load_and_preprocess_data(config_path)
        logger.info(f"Data loaded: {X.shape[0]} samples, {X.shape[1]} features")
        
        # 计算特征重要性
        analyzer.calculate_statistical_importance(X, y)
        analyzer.calculate_tree_importance(X, y)
        
        # 保存分析结果
        analyzer.save_feature_analysis("data/analysis/feature_importance_analysis.json")
        
        # 创建优化的配置文件
        analyzer.create_optimized_config(
            config_path,
            "src/configs/models/enhanced_optimized_features.json",
            method='rf_importance',
            k=50
        )
        
        # 打印top特征
        print("\n=== TOP 20 FEATURES (Random Forest) ===")
        top_rf_features = analyzer.get_top_features('rf_importance', 20)
        for i, feat in enumerate(top_rf_features, 1):
            score = analyzer.feature_scores['rf_importance'][feat]
            print(f"{i:2d}. {feat:<50} {score:.4f}")
            
        print("\n=== TOP 20 FEATURES (Mutual Information) ===")
        top_mi_features = analyzer.get_top_features('mutual_info', 20)
        for i, feat in enumerate(top_mi_features, 1):
            score = analyzer.feature_scores['mutual_info'][feat]
            print(f"{i:2d}. {feat:<50} {score:.4f}")
            
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()