#!/usr/bin/env python
"""
NIO转化率预测 - 最终统一训练脚本

集成所有最佳实践：
- 自适应模型架构
- 完整评估指标
- 现代化损失函数
- 专业训练流程
"""

import os
import sys
import logging
import time
import argparse
from pathlib import Path
from datetime import datetime
import json
import tensorflow as tf
import numpy as np
import pandas as pd

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.models.adaptive_model_factory import adaptive_factory
from src.data.loader import DataLoader
from src.features.builder import FeatureBuilder
from src.configs.unified_config_manager import unified_config_manager

# 设置日志
logs_dir = project_root / "logs"
logs_dir.mkdir(exist_ok=True)

def setup_logging():
    """设置日志"""
    log_file = logs_dir / f"final_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class FinalTrainer:
    """最终统一训练器"""
    
    def __init__(self, epochs: int = 10, batch_size: int = 512, learning_rate: float = 0.001):
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.logger = setup_logging()
        self.start_time = datetime.now()
        self.results = {}
    
    def train(self):
        """执行完整训练流程"""
        self.logger.info("🚀 ========NIO转化率预测 - 最终训练开始========")
        
        try:
            # 1. 环境检查
            self._check_environment()
            
            # 2. 数据加载
            train_data, test_data = self._load_data()
            
            # 3. 特征构建
            feature_dict, train_dataset, test_dataset = self._build_features(train_data, test_data)
            
            # 4. 模型构建
            model = self._build_model(train_data)
            
            # 5. 模型训练
            history = self._train_model(model, train_data, test_data)
            
            # 6. 结果评估
            self._evaluate_results(history, model)
            
            # 7. 保存结果
            self._save_results()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 训练失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _check_environment(self):
        """环境检查"""
        self.logger.info("1️⃣ 环境检查")
        self.logger.info(f"   TensorFlow版本: {tf.__version__}")
        
        # GPU设置
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            self.logger.info(f"   GPU设备: {len(gpus)} 个")
        else:
            self.logger.info("   使用CPU训练")
        
        self.results['environment'] = {
            'tensorflow_version': tf.__version__,
            'gpu_available': len(gpus) > 0,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate
        }
    
    def _load_data(self):
        """数据加载"""
        self.logger.info("2️⃣ 数据加载")
        
        # 加载配置
        experiment_config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")
        
        # 数据加载
        dataset_code = "data/dataset_nio_new_car_v15"
        data_loader = DataLoader(dataset_code, "src/configs/datasets/dataset_nio_new_car_v15.json")
        
        # 加载训练数据
        train_dates = ['20240430']
        self.logger.info(f"   加载训练数据: {train_dates}")
        df_raw = data_loader.load_dataset(dates=train_dates)
        
        # 采样以控制训练时间
        sample_size = min(3000, len(df_raw))  # 增加到3000个样本
        df_processed = df_raw.sample(n=sample_size, random_state=42)
        
        # 处理标签
        if 'm_purchase_days_nio_new_car' in df_processed.columns:
            def parse_label_string(label_str):
                try:
                    if isinstance(label_str, str):
                        import ast
                        return ast.literal_eval(label_str)
                    else:
                        return label_str
                except:
                    return [0, 0, 0, 0, 0, 0]
            
            df_processed['m_purchase_days_nio_new_car'] = df_processed['m_purchase_days_nio_new_car'].apply(parse_label_string)
        
        # 分割数据
        split_idx = int(len(df_processed) * 0.8)
        train_data = df_processed.iloc[:split_idx]
        test_data = df_processed.iloc[split_idx:]
        
        self.logger.info(f"   训练数据: {train_data.shape}")
        self.logger.info(f"   测试数据: {test_data.shape}")
        
        # 分析标签分布
        train_labels = np.array([row for row in train_data['m_purchase_days_nio_new_car']])
        positive_rate = (train_labels.sum(axis=1) > 0).mean()
        self.logger.info(f"   正样本率: {positive_rate:.4f}")
        
        self.results['data'] = {
            'train_samples': len(train_data),
            'test_samples': len(test_data),
            'features': train_data.shape[1],
            'positive_rate': float(positive_rate)
        }
        
        return train_data, test_data
    
    def _build_features(self, train_data, test_data):
        """特征构建"""
        self.logger.info("3️⃣ 特征构建")
        
        try:
            # 加载特征配置
            experiment_config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")
            raw_features = {name: {
                'type': feat.type,
                'dimension': feat.dimension,
                'boundaries': feat.boundaries,
                'vocabulary_size': feat.vocabulary_size,
                'max_length': feat.max_length
            } for name, feat in experiment_config.features.items()}
            
            feature_builder = FeatureBuilder()
            
            # 构建特征字典
            feature_dict = feature_builder.generate_dataset(train_data, raw_features, label=None)
            
            # 构建训练和测试数据集
            train_dataset = feature_builder.generate_dataset(
                train_data, raw_features, 
                label="m_purchase_days_nio_new_car", 
                batch_size=self.batch_size
            )
            
            test_dataset = feature_builder.generate_dataset(
                test_data, raw_features,
                label="m_purchase_days_nio_new_car",
                batch_size=self.batch_size
            )
            
            self.logger.info(f"   特征字典大小: {len(feature_dict)}")
            
            return feature_dict, train_dataset, test_dataset
            
        except Exception as e:
            self.logger.error(f"❌ 特征构建失败: {e}")
            raise
    
    def _build_model(self, train_data):
        """构建模型"""
        self.logger.info("4️⃣ 模型构建")
        
        # 使用自适应模型工厂
        model, preprocessing_info = adaptive_factory.create_model_from_data(
            train_data=train_data,
            target_column="m_purchase_days_nio_new_car",
            model_config={'output_dimension': 6}
        )
        
        self.logger.info(f"   模型参数: {model.count_params():,}")
        
        # 重新编译模型，使用完整指标
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=self.learning_rate),
            loss='binary_crossentropy',
            metrics=[
                'accuracy',
                tf.keras.metrics.Precision(name='precision'),
                tf.keras.metrics.Recall(name='recall'),
                tf.keras.metrics.AUC(name='auc'),
                tf.keras.metrics.AUC(curve='PR', name='pr_auc'),
                tf.keras.metrics.F1Score(name='f1_score')
            ]
        )
        
        self.results['model'] = {
            'parameters': int(model.count_params()),
            'architecture': 'adaptive_factory'
        }
        
        return model
    
    def _prepare_training_data(self, train_data, test_data, preprocessing_info):
        """准备训练数据"""
        feature_specs = preprocessing_info['feature_specs']
        
        def prepare_data(data):
            X_dict = {}
            for feature_name, spec in feature_specs.items():
                if feature_name in data.columns:
                    if spec.feature_type.value == 'numerical':
                        X_dict[feature_name] = data[feature_name].fillna(0).astype(np.float32).values
                    else:
                        series_data = data[feature_name].fillna('unknown')
                        if hasattr(series_data, 'cat'):
                            string_values = series_data.astype(str).values
                        else:
                            string_values = series_data.astype(str).values
                        X_dict[feature_name] = string_values
                else:
                    batch_size = len(data)
                    if spec.feature_type.value == 'numerical':
                        X_dict[feature_name] = np.zeros(batch_size, dtype=np.float32)
                    else:
                        X_dict[feature_name] = np.full(batch_size, 'unknown', dtype=str)
            
            y = np.array([row if isinstance(row, list) else [0,0,0,0,0,0] 
                         for row in data['m_purchase_days_nio_new_car']], dtype=np.float32)
            
            return X_dict, y
        
        return prepare_data(train_data), prepare_data(test_data)
    
    def _train_model(self, model, train_data, test_data):
        """训练模型"""
        self.logger.info("5️⃣ 模型训练")
        
        # 获取预处理信息
        _, preprocessing_info = adaptive_factory.create_model_from_data(
            train_data=train_data.head(100),  # 只用少量数据获取preprocessing_info
            target_column="m_purchase_days_nio_new_car",
            model_config={'output_dimension': 6}
        )
        
        # 准备训练数据
        (X_train, y_train), (X_test, y_test) = self._prepare_training_data(
            train_data, test_data, preprocessing_info
        )
        
        # 训练
        self.logger.info(f"🔥 开始训练（{self.epochs}个epoch）...")
        
        # 回调函数
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_pr_auc',
                patience=5,
                mode='max',
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=3,
                min_lr=1e-6,
                verbose=1
            )
        ]
        
        start_time = time.time()
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=self.epochs,
            batch_size=self.batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - start_time
        self.results['training_time'] = training_time
        
        self.logger.info(f"✅ 训练完成！耗时: {training_time:.2f}秒")
        
        return history
    
    def _evaluate_results(self, history, model):
        """评估结果"""
        self.logger.info("6️⃣ 结果评估")
        
        # 获取最终epoch的指标
        final_epoch = len(history.history['loss']) - 1
        
        # 训练集指标
        metrics = {}
        for metric_name in ['loss', 'accuracy', 'precision', 'recall', 'auc', 'pr_auc', 'f1_score']:
            metrics[f'train_{metric_name}'] = float(history.history[metric_name][final_epoch])
            metrics[f'val_{metric_name}'] = float(history.history[f'val_{metric_name}'][final_epoch])
        
        # 记录结果
        self.results['metrics'] = metrics
        self.results['epochs_completed'] = final_epoch + 1
        
        # 输出关键指标
        self.logger.info("📊 最终训练结果:")
        self.logger.info(f"   训练集 - Loss: {metrics['train_loss']:.4f} | AUC: {metrics['train_auc']:.4f} | PR-AUC: {metrics['train_pr_auc']:.4f}")
        self.logger.info(f"   验证集 - Loss: {metrics['val_loss']:.4f} | AUC: {metrics['val_auc']:.4f} | PR-AUC: {metrics['val_pr_auc']:.4f}")
        self.logger.info(f"   验证集 - Precision: {metrics['val_precision']:.4f} | Recall: {metrics['val_recall']:.4f} | F1: {metrics['val_f1_score']:.4f}")
        
        # 性能评估
        val_pr_auc = metrics['val_pr_auc']
        if val_pr_auc > 0.15:
            self.logger.info("🎉 模型性能：优秀")
        elif val_pr_auc > 0.10:
            self.logger.info("✅ 模型性能：良好")
        elif val_pr_auc > 0.05:
            self.logger.info("⚠️ 模型性能：一般")
        else:
            self.logger.info("❌ 模型性能：需要改进")
    
    def _save_results(self):
        """保存结果"""
        total_time = (datetime.now() - self.start_time).total_seconds()
        
        final_report = {
            'experiment_info': {
                'name': 'NIO转化率预测最终训练',
                'start_time': self.start_time.isoformat(),
                'total_duration': total_time,
                'config': {
                    'epochs': self.epochs,
                    'batch_size': self.batch_size,
                    'learning_rate': self.learning_rate
                }
            },
            'results': self.results,
            'summary': {
                'success': True,
                'key_metrics': {
                    'val_pr_auc': self.results['metrics']['val_pr_auc'],
                    'val_auc': self.results['metrics']['val_auc'],
                    'val_f1_score': self.results['metrics']['val_f1_score'],
                    'model_params': self.results['model']['parameters'],
                    'training_time': self.results['training_time']
                }
            }
        }
        
        # 保存报告
        report_file = logs_dir / f"final_training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"📄 最终报告已保存: {report_file}")
        self.logger.info(f"📋 训练总结 - 总耗时: {total_time:.2f}秒")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='NIO转化率预测 - 最终统一训练')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=512, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    
    args = parser.parse_args()
    
    trainer = FinalTrainer(
        epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate
    )
    
    success = trainer.train()
    
    if success:
        print("🎉 训练成功完成！")
        return 0
    else:
        print("❌ 训练失败")
        return 1


if __name__ == "__main__":
    exit(main())