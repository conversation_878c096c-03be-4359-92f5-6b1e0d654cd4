# ML模型优化实验

本目录包含了完整的ML模型优化实验流程和脚本。

## 📁 目录结构

```
optimization_experiments/
├── README.md                           # 本文档
├── simple_baseline_test.py             # 基线测试脚本
├── optimization_v1_test.py             # 优化版本1：激进特征扩展
├── optimization_v2_test.py             # 优化版本2：保守优化策略
├── optimization_v3_test.py             # 优化版本3：Focal Loss优化
├── optimization_v4_test.py             # 优化版本4：加权损失函数
├── optimization_v5_test.py             # 优化版本5：简单有效优化 ⭐
├── optimization_summary.py             # 优化总结报告生成器
├── baseline_performance_test.py        # 基线性能测试
├── data_driven_optimization_test.py    # 数据驱动优化测试
├── simple_optimization_test.py         # 简单优化测试
└── unified_optimization_test.py        # 统一优化测试
```

## 🎯 优化历程总结

### 基线测试
- **脚本**: `simple_baseline_test.py`
- **配置**: 简化基线配置，9个核心特征
- **结果**: Month_1 PR-AUC = 0.5040
- **状态**: ✅ 成功建立基线

### 优化V1：激进特征扩展
- **脚本**: `optimization_v1_test.py`
- **策略**: 大幅增加特征数量（21个）
- **结果**: Month_1 PR-AUC = 0.0050
- **状态**: ❌ 性能严重下降，过拟合

### 优化V2：保守优化策略
- **脚本**: `optimization_v2_test.py`
- **策略**: 在基线基础上增加3个高质量特征
- **结果**: Month_1 PR-AUC = 0.5047 (+0.1%)
- **状态**: ✅ 小幅提升成功

### 优化V3：Focal Loss优化
- **脚本**: `optimization_v3_test.py`
- **策略**: 使用Focal Loss处理类别不平衡
- **结果**: 训练失败
- **状态**: ❌ 损失函数形状不匹配

### 优化V4：加权损失函数
- **脚本**: `optimization_v4_test.py`
- **策略**: 使用加权二元交叉熵
- **结果**: 训练失败
- **状态**: ❌ 数据类型不匹配

### 优化V5：简单有效优化 ⭐
- **脚本**: `optimization_v5_test.py`
- **策略**: 基于V2的渐进式改进
- **结果**: Month_1 PR-AUC = 0.5053 (+0.3%)
- **状态**: 🎉 最佳性能

## 🚀 快速开始

### 运行基线测试
```bash
cd /path/to/nio-eatv
python scripts/optimization_experiments/simple_baseline_test.py --epochs 3 --sample-size 3000
```

### 运行最佳优化版本
```bash
python scripts/optimization_experiments/optimization_v5_test.py --epochs 8 --sample-size 20000
```

### 生成优化总结报告
```bash
python scripts/optimization_experiments/optimization_summary.py
```

## 📊 性能对比

| 版本 | 策略 | 特征数 | 训练样本 | Month_1 PR-AUC | 状态 |
|------|------|--------|----------|----------------|------|
| 基线 | 简化配置 | 9 | 3,000 | 0.5040 | ✅ |
| V1 | 激进优化 | 21 | 8,000 | 0.0050 | ❌ |
| V2 | 保守优化 | 12 | 5,000 | 0.5047 | ✅ |
| V3 | Focal Loss | 15 | 12,000 | N/A | ❌ |
| V4 | 加权损失 | 15 | 15,000 | N/A | ❌ |
| V5 | 简单有效 | 14 | 20,000 | 0.5053 | 🎉 |

## 💡 关键经验

1. **渐进式优化比激进式优化更可靠**
   - V2的保守策略成功，V1的激进策略失败
   - 在稳定基础上小步迭代是最佳实践

2. **特征工程需要精心设计**
   - 更多特征不等于更好性能
   - 高质量特征比特征数量更重要

3. **数据量的重要性**
   - V5使用20,000样本比V2的5,000样本效果更好
   - 充足的训练数据有助于模型泛化

4. **简单方法的可靠性**
   - 标准损失函数比自定义损失函数更可靠
   - V3和V4的复杂损失函数都失败了

5. **训练策略优化的价值**
   - 降低学习率(0.0005)比默认学习率(0.001)更稳定
   - 适当的回调函数有助于防止过拟合

## 🎯 下一步优化建议

1. **特征工程优化**
   - 分析特征重要性，移除冗余特征
   - 尝试特征交互和组合
   - 探索时间序列特征的时间窗口优化

2. **模型架构优化**
   - 尝试不同的expert_num配置
   - 探索use_time_attention的效果
   - 考虑use_cross_layer的不同配置

3. **训练策略优化**
   - 尝试不同的学习率调度策略
   - 探索不同的批次大小
   - 考虑数据增强技术

4. **数据质量提升**
   - 增加更多历史数据
   - 改进数据清洗流程
   - 处理缺失值和异常值

5. **模型集成**
   - 训练多个模型进行集成
   - 尝试不同的集成策略
   - 考虑时间序列交叉验证

## 📄 相关文档

- [优化路线图V2](../../docs/OPTIMIZATION_ROADMAP_V2.md)
- [优化总结V2](../../docs/OPTIMIZATION_SUMMARY_V2.md)
- [项目主README](../../README.md)

## 🔗 结果文件

优化实验的详细结果保存在：
- `logs/optimization_results/optimization_v*_*.json`
- `logs/optimization_results/optimization_summary_*.json`

## 📞 联系方式

如有问题或建议，请联系项目维护者。
