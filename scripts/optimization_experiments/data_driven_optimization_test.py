#!/usr/bin/env python
"""
数据驱动优化测试脚本

这个脚本将：
1. 加载原始数据
2. 应用特征优化策略
3. 训练优化后的模型
4. 对比优化前后的性能
5. 生成详细的优化报告
"""

import os
import sys
import logging
import time
import json
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np
import tensorflow as tf

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor
from src.features.builder import FeatureBuilder
from src.features.optimizers import FeatureOptimizer
from src.training.enhanced_trainer import EnhancedModelTrainer
from src.evaluation.evaluator import ModelEvaluator
from src.configs.unified_config_manager import UnifiedConfigManager


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'logs/data_driven_optimization_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger(__name__)


def load_baseline_config():
    """加载基线配置"""
    config_manager = UnifiedConfigManager()
    return config_manager.load_model_config("GOLDEN_CONFIG")


def load_optimization_config():
    """加载优化配置"""
    config_manager = UnifiedConfigManager()
    return config_manager.load_model_config("OPTIMIZATION_V8_DATA_DRIVEN")


def run_baseline_experiment(logger):
    """运行基线实验"""
    logger.info("=" * 60)
    logger.info("开始基线实验 (GOLDEN_CONFIG)")
    logger.info("=" * 60)
    
    try:
        # 加载基线配置
        config = load_baseline_config()
        
        # 数据加载
        data_loader = DataLoader("data/dataset_nio_new_car_v15")
        train_data = data_loader.load_data(config['model_config']['train_dates'])
        test_data = data_loader.load_data(config['model_config']['test_dates'])
        
        logger.info(f"基线数据加载完成: 训练集{len(train_data)}条, 测试集{len(test_data)}条")
        
        # 数据预处理
        preprocessor = DataPreprocessor()
        train_processed = preprocessor.preprocess_features(train_data)
        test_processed = preprocessor.preprocess_features(test_data)
        
        # 特征构建
        feature_builder = FeatureBuilder()
        train_dataset = feature_builder.generate_dataset(
            train_processed, 
            config.get('features', {}),
            label='m_purchase_days_nio_new_car_consum',
            batch_size=config['training_config']['batch_size']
        )
        
        # 模型训练
        trainer = EnhancedModelTrainer(config, "baseline_experiment")
        
        start_time = time.time()
        trainer.build_model(train_dataset.take(1))
        trainer.train(train_dataset, epochs=3, patience=10)
        training_time = time.time() - start_time
        
        # 模型评估
        evaluator = ModelEvaluator()
        test_features = feature_builder.generate_dataset(
            test_processed,
            config.get('features', {}),
            label=None
        )
        
        predictions = trainer.model.predict(test_features)
        metrics = evaluator.evaluate_predictions(
            test_processed['m_purchase_days_nio_new_car_consum'].tolist(),
            predictions
        )
        
        baseline_results = {
            'config': 'GOLDEN_CONFIG',
            'training_time': training_time,
            'model_params': trainer.model.count_params(),
            'feature_count': len(config.get('features', {})),
            'metrics': metrics,
            'data_shape': {
                'train': train_data.shape,
                'test': test_data.shape
            }
        }
        
        logger.info(f"基线实验完成:")
        logger.info(f"  训练时间: {training_time:.2f}秒")
        logger.info(f"  模型参数: {trainer.model.count_params():,}")
        logger.info(f"  Month_1 PR-AUC: {metrics.get('month_1_pr_auc', 'N/A')}")
        
        return baseline_results
        
    except Exception as e:
        logger.error(f"基线实验失败: {e}")
        return None


def run_optimization_experiment(logger):
    """运行优化实验"""
    logger.info("=" * 60)
    logger.info("开始优化实验 (OPTIMIZATION_V8_DATA_DRIVEN)")
    logger.info("=" * 60)
    
    try:
        # 加载优化配置
        config = load_optimization_config()
        
        # 数据加载
        data_loader = DataLoader("data/dataset_nio_new_car_v15")
        train_data = data_loader.load_data(config['model_config']['train_dates'])
        test_data = data_loader.load_data(config['model_config']['test_dates'])
        
        logger.info(f"优化数据加载完成: 训练集{len(train_data)}条, 测试集{len(test_data)}条")
        
        # 特征优化
        feature_optimizer = FeatureOptimizer(config.get('feature_optimization', {}))
        
        logger.info("开始特征优化...")
        train_optimized, optimization_report = feature_optimizer.optimize_features(
            train_data, 
            'm_purchase_days_nio_new_car_consum',
            config.get('features', {})
        )
        
        # 对测试数据应用相同的优化（但不重新计算策略）
        test_optimized = test_data[train_optimized.columns]
        
        logger.info(f"特征优化完成:")
        logger.info(f"  原始特征数: {optimization_report['original_features']}")
        logger.info(f"  优化后特征数: {optimization_report['final_features']}")
        logger.info(f"  特征减少率: {optimization_report['reduction_rate']:.1%}")
        
        # 数据预处理
        preprocessor = DataPreprocessor()
        train_processed = preprocessor.preprocess_features(train_optimized)
        test_processed = preprocessor.preprocess_features(test_optimized)
        
        # 特征构建
        feature_builder = FeatureBuilder()
        train_dataset = feature_builder.generate_dataset(
            train_processed,
            config.get('features', {}),
            label='m_purchase_days_nio_new_car_consum',
            batch_size=config['training_config']['batch_size']
        )
        
        # 模型训练
        trainer = EnhancedModelTrainer(config, "optimization_experiment")
        
        start_time = time.time()
        trainer.build_model(train_dataset.take(1))
        trainer.train(train_dataset, epochs=3, patience=10)
        training_time = time.time() - start_time
        
        # 模型评估
        evaluator = ModelEvaluator()
        test_features = feature_builder.generate_dataset(
            test_processed,
            config.get('features', {}),
            label=None
        )
        
        predictions = trainer.model.predict(test_features)
        metrics = evaluator.evaluate_predictions(
            test_processed['m_purchase_days_nio_new_car_consum'].tolist(),
            predictions
        )
        
        optimization_results = {
            'config': 'OPTIMIZATION_V8_DATA_DRIVEN',
            'training_time': training_time,
            'model_params': trainer.model.count_params(),
            'feature_count': optimization_report['final_features'],
            'metrics': metrics,
            'optimization_report': optimization_report,
            'data_shape': {
                'train': train_optimized.shape,
                'test': test_optimized.shape
            }
        }
        
        logger.info(f"优化实验完成:")
        logger.info(f"  训练时间: {training_time:.2f}秒")
        logger.info(f"  模型参数: {trainer.model.count_params():,}")
        logger.info(f"  Month_1 PR-AUC: {metrics.get('month_1_pr_auc', 'N/A')}")
        
        return optimization_results
        
    except Exception as e:
        logger.error(f"优化实验失败: {e}")
        return None


def compare_results(baseline_results, optimization_results, logger):
    """对比实验结果"""
    logger.info("=" * 60)
    logger.info("实验结果对比")
    logger.info("=" * 60)
    
    if not baseline_results or not optimization_results:
        logger.error("缺少实验结果，无法进行对比")
        return None
    
    # 性能对比
    baseline_pr_auc = baseline_results['metrics'].get('month_1_pr_auc', 0)
    optimization_pr_auc = optimization_results['metrics'].get('month_1_pr_auc', 0)
    pr_auc_improvement = optimization_pr_auc - baseline_pr_auc
    
    # 效率对比
    training_time_reduction = (baseline_results['training_time'] - optimization_results['training_time']) / baseline_results['training_time']
    feature_reduction = (baseline_results['feature_count'] - optimization_results['feature_count']) / baseline_results['feature_count']
    
    comparison = {
        'performance': {
            'baseline_pr_auc': baseline_pr_auc,
            'optimization_pr_auc': optimization_pr_auc,
            'pr_auc_improvement': pr_auc_improvement,
            'improvement_percentage': pr_auc_improvement / baseline_pr_auc * 100 if baseline_pr_auc > 0 else 0
        },
        'efficiency': {
            'baseline_training_time': baseline_results['training_time'],
            'optimization_training_time': optimization_results['training_time'],
            'training_time_reduction': training_time_reduction,
            'baseline_feature_count': baseline_results['feature_count'],
            'optimization_feature_count': optimization_results['feature_count'],
            'feature_reduction': feature_reduction
        },
        'model_complexity': {
            'baseline_params': baseline_results['model_params'],
            'optimization_params': optimization_results['model_params'],
            'params_change': optimization_results['model_params'] - baseline_results['model_params']
        }
    }
    
    logger.info("性能对比:")
    logger.info(f"  基线 Month_1 PR-AUC: {baseline_pr_auc:.4f}")
    logger.info(f"  优化 Month_1 PR-AUC: {optimization_pr_auc:.4f}")
    logger.info(f"  性能提升: {pr_auc_improvement:+.4f} ({comparison['performance']['improvement_percentage']:+.1f}%)")
    
    logger.info("效率对比:")
    logger.info(f"  训练时间: {baseline_results['training_time']:.2f}s → {optimization_results['training_time']:.2f}s ({training_time_reduction:+.1%})")
    logger.info(f"  特征数量: {baseline_results['feature_count']} → {optimization_results['feature_count']} ({feature_reduction:+.1%})")
    logger.info(f"  模型参数: {baseline_results['model_params']:,} → {optimization_results['model_params']:,}")
    
    return comparison


def save_experiment_report(baseline_results, optimization_results, comparison, logger):
    """保存实验报告"""
    report = {
        'experiment_info': {
            'timestamp': datetime.now().isoformat(),
            'description': '数据驱动的特征优化实验',
            'baseline_config': 'GOLDEN_CONFIG',
            'optimization_config': 'OPTIMIZATION_V8_DATA_DRIVEN'
        },
        'baseline_results': baseline_results,
        'optimization_results': optimization_results,
        'comparison': comparison
    }
    
    # 保存到文件
    report_dir = Path('logs/experiments')
    report_dir.mkdir(parents=True, exist_ok=True)
    
    report_file = report_dir / f"data_driven_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    logger.info(f"实验报告已保存: {report_file}")
    return report_file


def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始数据驱动优化实验")
    logger.info(f"TensorFlow版本: {tf.__version__}")
    
    try:
        # 运行基线实验
        baseline_results = run_baseline_experiment(logger)
        
        # 运行优化实验
        optimization_results = run_optimization_experiment(logger)
        
        # 对比结果
        comparison = compare_results(baseline_results, optimization_results, logger)
        
        # 保存报告
        if baseline_results and optimization_results and comparison:
            report_file = save_experiment_report(baseline_results, optimization_results, comparison, logger)
            logger.info(f"实验完成，报告保存至: {report_file}")
        else:
            logger.error("实验未完全成功，无法生成完整报告")
            
    except Exception as e:
        logger.error(f"实验执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
