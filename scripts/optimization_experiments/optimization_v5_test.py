#!/usr/bin/env python
"""
优化版本5测试脚本 - 简单有效的优化

基于V2的成功，采用简单但有效的优化策略：
1. 保持V2的特征配置
2. 增加数据量
3. 优化训练参数
4. 使用标准损失函数但调整学习率

目标：Month_1 PR-AUC > 0.52 (比基线0.5040提升3%+)
"""

import sys
import logging
import time
import json
from pathlib import Path
from datetime import datetime
import numpy as np
import tensorflow as tf

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor
from src.features.builder import FeatureBuilder
from src.models.model_factory import ModelFactory
from src.evaluation.evaluator import ModelEvaluator


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_optimized_features_config():
    """创建优化的特征配置 - 基于V2成功配置"""
    return {
        # V2的核心特征
        'user_create_days': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 30, 90, 180, 365, 1000]},
        'intention_intention_fail_days': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 7, 30, 90, 180, 365]},
        'app_search_intention_DSLA': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 7, 30, 90, 180, 365]},
        'user_core_nio_value': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 10, 50, 100, 500, 1000]},
        'intention_opportunity_create_days': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 1, 7, 30, 90, 180, 365]},
        'user_core_first_reg_leads_nio_DSLA': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 1, 7, 30, 90, 180, 365]},
        'user_core_unfirst_reg_leads_nio_DSLA': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 1, 7, 30, 90, 180, 365]},
        
        # V2的类别特征
        'user_core_user_gender': {'type': 'table', 'dtype': 'StringLookup'},
        'user_core_user_age_group': {'type': 'table', 'dtype': 'StringLookup'},
        'user_core_nio_user_identity': {'type': 'table', 'dtype': 'StringLookup'},
        'intention_stage': {'type': 'table', 'dtype': 'StringLookup'},
        'intention_status': {'type': 'table', 'dtype': 'StringLookup'},
        
        # 新增少量高质量特征
        'user_register_days': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 30, 90, 180, 365, 730, 1095]},
        'intention_create_time_days': {'type': 'table', 'dtype': 'Bucket', 'bin_boundarie': [0, 1, 7, 30, 90, 180, 365]},
    }


def create_optimized_model_config():
    """创建优化的模型配置 - 基于V2但稍微增强"""
    features_config = create_optimized_features_config()
    
    return {
        'network_name': 'EPMMOENet',
        'default_embedding_dimension': 14,  # 比V2(12)稍大
        'default_gru_dimension': 28,        # 比V2(24)稍大
        'expert_num': 7,                    # 比V2(6)稍大
        'use_cross_layer': True,
        'use_multitask': False,
        'use_mixed_precision': False,
        'use_time_attention': False,
        'output_dimension': 6,
        'output_activation': 'sigmoid',
        
        'RawFeature': features_config,
        'InputGeneral': {
            'features': [
                'user_create_days',
                'intention_intention_fail_days', 
                'app_search_intention_DSLA',
                'user_core_nio_value',
                'intention_opportunity_create_days',
                'user_core_first_reg_leads_nio_DSLA',
                'user_core_unfirst_reg_leads_nio_DSLA',
                'user_core_user_gender',
                'user_core_user_age_group',
                'user_core_nio_user_identity',
                'user_register_days',
                'intention_create_time_days',
            ]
        },
        'InputScene': {
            'features': [
                'intention_stage',
                'intention_status'
            ]
        },
        'InputSeqSet': {
            'Set': [],
            'SetInfo': {}
        }
    }


def run_optimization_v5_test(logger, epochs=8, sample_size=25000):
    """运行优化版本5测试"""
    logger.info("=" * 60)
    logger.info("开始优化版本5测试 - 简单有效的优化")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    try:
        # 1. 数据加载
        logger.info("1. 数据加载...")
        data_loader = DataLoader("data/dataset_nio_new_car_v15")
        
        train_data = data_loader.load_dataset(["20240430"])
        test_data = data_loader.load_dataset(["20240531"])
        
        # 使用更多数据进行训练
        train_data = train_data.sample(n=min(sample_size, len(train_data)), random_state=42)
        test_data = test_data.sample(n=min(3000, len(test_data)), random_state=42)
        
        logger.info(f"数据加载完成: 训练集{len(train_data)}条, 测试集{len(test_data)}条")
        
        # 2. 数据预处理
        logger.info("2. 数据预处理...")
        preprocessor = DataPreprocessor()
        
        # 处理标签
        train_data = preprocessor.process_purchase_labels(train_data)
        test_data = preprocessor.process_purchase_labels(test_data)
        
        # 检查标签分布
        train_labels = train_data['m_purchase_days_nio_new_car_consum'].values
        
        # 处理标签格式（可能是列表）
        if len(train_labels) > 0 and isinstance(train_labels[0], (list, np.ndarray)):
            train_labels = np.array([label[0] if isinstance(label, (list, np.ndarray)) and len(label) > 0 else 0 
                                   for label in train_labels])
        
        positive_rate = np.mean(train_labels > 0)
        logger.info(f"训练数据正样本率: {positive_rate:.3f}")
        
        # 3. 特征构建
        logger.info("3. 特征构建...")
        features_config = create_optimized_features_config()
        
        # 过滤存在的特征
        available_features = {}
        for feature_name, feature_config in features_config.items():
            if feature_name in train_data.columns:
                available_features[feature_name] = feature_config
            else:
                logger.warning(f"特征 {feature_name} 不存在，跳过")
        
        logger.info(f"可用特征: {len(available_features)}个")
        
        # 预处理特征
        train_processed = preprocessor.preprocess_model_features(train_data, available_features)
        test_processed = preprocessor.preprocess_model_features(test_data, available_features)
        
        # 分割训练和验证数据
        train_size = int(0.8 * len(train_processed))
        train_split = train_processed.iloc[:train_size]
        val_split = train_processed.iloc[train_size:]
        
        # 构建数据集
        feature_builder = FeatureBuilder()
        
        train_dataset = feature_builder.generate_dataset(
            train_split, available_features, 
            label='m_purchase_days_nio_new_car_consum', 
            batch_size=512
        )
        
        val_dataset = feature_builder.generate_dataset(
            val_split, available_features,
            label='m_purchase_days_nio_new_car_consum',
            batch_size=512
        )
        
        test_features = feature_builder.generate_dataset(
            test_processed, available_features, label=None
        )
        
        logger.info("特征构建完成")
        
        # 4. 模型构建
        logger.info("4. 模型构建...")
        model_config = create_optimized_model_config()
        
        model_factory = ModelFactory()
        model = model_factory.create_model(
            model_config['network_name'],
            model_config,
            embedding_features=available_features
        )
        
        # 编译模型 - 使用标准配置但优化学习率
        model.compile(
            optimizer=tf.keras.optimizers.Adam(
                learning_rate=0.0005,  # 降低学习率
                beta_1=0.9,
                beta_2=0.999,
                epsilon=1e-7
            ),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        # 构建模型（通过第一个batch）
        for batch in train_dataset.take(1):
            _ = model(batch[0])
            break
        
        model_params = model.count_params()
        logger.info(f"模型构建完成，参数数量: {model_params:,}")
        
        # 5. 模型训练
        logger.info("5. 模型训练...")
        
        # 设置回调
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=4,
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=2,
                min_lr=1e-6,
                verbose=1
            )
        ]
        
        training_start = time.time()
        history = model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=epochs,
            callbacks=callbacks,
            verbose=1
        )
        training_time = time.time() - training_start
        
        logger.info(f"模型训练完成，耗时: {training_time:.2f}秒")
        
        # 6. 模型评估
        logger.info("6. 模型评估...")
        
        # 预测
        predictions = model.predict(test_features)
        
        # 评估
        evaluator = ModelEvaluator()
        
        # 准备评估数据
        test_eval = test_processed.copy()
        
        # 处理预测结果
        if predictions.shape[1] == 6:
            test_eval['result'] = predictions.tolist()
        else:
            pred_expanded = np.tile(predictions.flatten().reshape(-1, 1), (1, 6))
            test_eval['result'] = pred_expanded.tolist()
        
        # 确保标签也是正确的格式
        labels = test_eval['m_purchase_days_nio_new_car_consum']
        if isinstance(labels.iloc[0], (list, np.ndarray)):
            pass
        else:
            label_expanded = np.zeros((len(labels), 6))
            label_expanded[:, 0] = labels.values
            test_eval['m_purchase_days_nio_new_car_consum'] = label_expanded.tolist()
        
        # 计算评估指标
        metrics = evaluator.evaluate_model(
            df_test_m=test_eval,
            df_evaluate=test_eval,
            label_column='m_purchase_days_nio_new_car_consum',
            pred_column='result'
        )
        
        # 7. 结果汇总
        total_time = time.time() - start_time
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'config': 'optimization_v5',
            'optimization_type': 'simple_effective',
            'data_info': {
                'train_samples': len(train_data),
                'test_samples': len(test_data),
                'features_count': len(available_features),
                'positive_rate': float(positive_rate)
            },
            'model_info': {
                'parameters': int(model_params),
                'training_time': float(training_time),
                'epochs_trained': len(history.history['loss'])
            },
            'performance': metrics,
            'total_time': float(total_time)
        }
        
        # 输出关键指标
        logger.info("=" * 60)
        logger.info("优化版本5测试结果")
        logger.info("=" * 60)
        logger.info(f"数据规模: 训练{len(train_data)}条, 测试{len(test_data)}条")
        logger.info(f"特征数量: {len(available_features)}个")
        logger.info(f"模型参数: {model_params:,}")
        logger.info(f"训练时间: {training_time:.2f}秒")
        logger.info(f"训练轮数: {len(history.history['loss'])}")
        logger.info(f"总耗时: {total_time:.2f}秒")
        
        # 输出性能指标
        month_1_metrics = metrics.get('metrics_month_test', {}).get('Month_1', {})
        if month_1_metrics:
            logger.info(f"Month_1 PR-AUC: {month_1_metrics.get('PR_AUC', 0):.4f}")
            logger.info(f"Month_1 ROC-AUC: {month_1_metrics.get('ROC_AUC', 0):.4f}")
            logger.info(f"Month_1 Recall@840: {month_1_metrics.get('Recall@840', 0):.4f}")
        
        # 输出训练历史
        final_loss = history.history['loss'][-1]
        final_val_loss = history.history['val_loss'][-1]
        logger.info(f"最终训练损失: {final_loss:.4f}")
        logger.info(f"最终验证损失: {final_val_loss:.4f}")
        
        # 与之前版本对比
        baseline_pr_auc = 0.5040  # 基线
        v2_pr_auc = 0.5047       # V2
        current_pr_auc = month_1_metrics.get('PR_AUC', 0)
        
        baseline_improvement = current_pr_auc - baseline_pr_auc
        baseline_improvement_pct = (baseline_improvement / baseline_pr_auc * 100) if baseline_pr_auc > 0 else 0
        
        v2_improvement = current_pr_auc - v2_pr_auc
        v2_improvement_pct = (v2_improvement / v2_pr_auc * 100) if v2_pr_auc > 0 else 0
        
        logger.info("=" * 60)
        logger.info("性能对比")
        logger.info("=" * 60)
        logger.info(f"基线 Month_1 PR-AUC: {baseline_pr_auc:.4f}")
        logger.info(f"V2    Month_1 PR-AUC: {v2_pr_auc:.4f}")
        logger.info(f"V5    Month_1 PR-AUC: {current_pr_auc:.4f}")
        logger.info(f"相比基线改进: {baseline_improvement:+.4f} ({baseline_improvement_pct:+.1f}%)")
        logger.info(f"相比V2改进: {v2_improvement:+.4f} ({v2_improvement_pct:+.1f}%)")
        
        if current_pr_auc > baseline_pr_auc:
            if baseline_improvement_pct >= 3.0:
                logger.info("🎉 V5达到目标！性能提升超过3%!")
            elif baseline_improvement_pct >= 1.0:
                logger.info("✅ V5性能提升良好，超过1%!")
            else:
                logger.info("✅ V5性能提升成功，但幅度较小")
        else:
            logger.info("⚠️ V5性能未超过基线，需要进一步优化")
        
        # 保存结果
        results_dir = Path('logs/optimization_results')
        results_dir.mkdir(parents=True, exist_ok=True)
        
        results_file = results_dir / f"optimization_v5_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"结果已保存: {results_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"优化版本5测试失败: {e}")
        raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="优化版本5测试")
    parser.add_argument('--epochs', type=int, default=8, help='训练轮数')
    parser.add_argument('--sample-size', type=int, default=25000, help='训练样本数量')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    try:
        results = run_optimization_v5_test(
            logger, 
            epochs=args.epochs, 
            sample_size=args.sample_size
        )
        
        logger.info("🎉 优化版本5测试完成!")
        return 0
        
    except Exception as e:
        logger.error(f"❌ 优化版本5测试失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
