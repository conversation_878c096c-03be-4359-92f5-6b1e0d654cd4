#!/usr/bin/env python
"""
简化版优化测试脚本

这个脚本用于验证基本的优化功能，避免复杂的依赖问题
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def test_feature_optimizer():
    """测试特征优化器"""
    logger = logging.getLogger(__name__)
    logger.info("测试特征优化器")
    
    try:
        from src.features.optimizers import FeatureOptimizer
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 1000
        
        test_data = pd.DataFrame({
            'feature1': np.random.randn(n_samples),
            'feature2': np.random.randn(n_samples) * 0.1 + np.random.randn(n_samples),  # 与feature1相关
            'feature3': np.random.randn(n_samples),
            'feature4': np.random.choice(['A', 'B', 'C', 'D'], n_samples),
            'feature5': np.random.choice(['X', 'Y'], n_samples, p=[0.9, 0.1]),  # 不平衡类别
            'target': np.random.choice([0, 1], n_samples, p=[0.95, 0.05])  # 不平衡目标
        })
        
        # 添加一些缺失值
        test_data.loc[np.random.choice(n_samples, 100, replace=False), 'feature1'] = np.nan
        test_data.loc[np.random.choice(n_samples, 200, replace=False), 'feature4'] = np.nan
        
        logger.info(f"创建测试数据: {test_data.shape}")
        logger.info(f"缺失值统计: {test_data.isnull().sum().to_dict()}")
        
        # 初始化优化器
        optimizer = FeatureOptimizer()
        
        # 执行优化
        optimized_data, report = optimizer.optimize_features(
            test_data, 
            'target',
            {}
        )
        
        logger.info("特征优化完成")
        logger.info(f"原始特征数: {report['original_features']}")
        logger.info(f"优化后特征数: {report['final_features']}")
        logger.info(f"减少率: {report['reduction_rate']:.1%}")
        
        # 检查优化步骤
        for step_name, step_result in report['steps']:
            logger.info(f"步骤 {step_name}: {step_result}")
        
        logger.info("✅ 特征优化器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 特征优化器测试失败: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    logger = logging.getLogger(__name__)
    logger.info("测试配置加载")
    
    try:
        import yaml
        
        # 测试加载GOLDEN_CONFIG
        config_file = Path("src/configs/models/GOLDEN_CONFIG.yaml")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ GOLDEN_CONFIG加载成功: {config.get('_metadata', {}).get('version', 'unknown')}")
        else:
            logger.warning(f"⚠️ GOLDEN_CONFIG文件不存在: {config_file}")
        
        # 测试加载优化配置
        opt_config_file = Path("src/configs/models/OPTIMIZATION_V8_DATA_DRIVEN.yaml")
        if opt_config_file.exists():
            with open(opt_config_file, 'r', encoding='utf-8') as f:
                opt_config = yaml.safe_load(f)
            logger.info(f"✅ 优化配置加载成功: {opt_config.get('_metadata', {}).get('version', 'unknown')}")
        else:
            logger.warning(f"⚠️ 优化配置文件不存在: {opt_config_file}")
        
        logger.info("✅ 配置加载测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置加载测试失败: {e}")
        return False


def test_data_loading():
    """测试数据加载"""
    logger = logging.getLogger(__name__)
    logger.info("测试数据加载")
    
    try:
        from src.data.loader import DataLoader
        
        # 检查数据目录
        data_dir = Path("data/dataset_nio_new_car_v15")
        if not data_dir.exists():
            logger.warning(f"⚠️ 数据目录不存在: {data_dir}")
            return False
        
        # 检查数据文件
        train_dir = data_dir / "datetime=20240430"
        test_dir = data_dir / "datetime=20240531"
        
        if not train_dir.exists():
            logger.warning(f"⚠️ 训练数据目录不存在: {train_dir}")
            return False
        
        if not test_dir.exists():
            logger.warning(f"⚠️ 测试数据目录不存在: {test_dir}")
            return False
        
        # 尝试加载少量数据
        data_loader = DataLoader("data/dataset_nio_new_car_v15")
        
        # 只加载前100行进行测试
        train_data = data_loader.load_dataset(["20240430"])
        sample_data = train_data.head(100)
        
        logger.info(f"✅ 数据加载成功: {sample_data.shape}")
        logger.info(f"列数: {len(sample_data.columns)}")
        logger.info(f"数据类型: {sample_data.dtypes.value_counts().to_dict()}")
        
        # 检查是否有问题列
        problematic_columns = []
        for col in sample_data.columns:
            try:
                str(col)  # 尝试转换为字符串
            except:
                problematic_columns.append(col)
        
        if problematic_columns:
            logger.warning(f"⚠️ 发现问题列: {problematic_columns}")
        else:
            logger.info("✅ 所有列名都正常")
        
        logger.info("✅ 数据加载测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据加载测试失败: {e}")
        return False


def test_enhanced_preprocessor():
    """测试增强版预处理器"""
    logger = logging.getLogger(__name__)
    logger.info("测试增强版预处理器")
    
    try:
        from src.data.enhanced_preprocessor import EnhancedDataPreprocessor
        import pandas as pd
        import numpy as np
        
        # 创建简单测试数据
        test_data = pd.DataFrame({
            'numeric_feature': [1, 2, 3, np.nan, 5],
            'categorical_feature': ['A', 'B', 'A', 'C', 'B'],
            'target': [0, 1, 0, 1, 0]
        })
        
        logger.info(f"创建测试数据: {test_data.shape}")
        
        # 初始化预处理器
        preprocessor = EnhancedDataPreprocessor()
        
        # 执行预处理
        processed_data, report = preprocessor.enhanced_preprocess(
            test_data,
            target_column='target',
            apply_optimization=False  # 先不启用优化
        )
        
        logger.info("增强版预处理完成")
        logger.info(f"处理后数据: {processed_data.shape}")
        logger.info(f"处理步骤: {len(report['steps'])}")
        
        for step_name, step_result in report['steps']:
            logger.info(f"步骤 {step_name}: {step_result}")
        
        logger.info("✅ 增强版预处理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强版预处理器测试失败: {e}")
        return False


def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始简化版优化测试")
    logger.info("=" * 60)
    
    tests = [
        ("配置加载", test_config_loading),
        ("特征优化器", test_feature_optimizer),
        ("数据加载", test_data_loading),
        ("增强版预处理器", test_enhanced_preprocessor),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"运行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
        
        logger.info("-" * 40)
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("测试结果汇总")
    logger.info("=" * 60)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！优化功能基本正常")
        return 0
    else:
        logger.error(f"⚠️ {total_tests - passed_tests} 个测试失败，需要修复")
        return 1


if __name__ == "__main__":
    exit(main())
