#!/usr/bin/env python3
"""
基于购车流程逻辑的特征分析和优化
意向→搜索→试驾→购车→后续行为流程分析
"""

import json
import pandas as pd
import numpy as np
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PurchaseWorkflowAnalyzer:
    """购车流程特征分析器"""
    
    def __init__(self):
        self.workflow_stages = {}
        self.feature_importance = {}
        
    def define_purchase_workflow(self):
        """定义购车决策流程的特征分类"""
        self.workflow_stages = {
            '1_意向产生阶段': {
                'description': '用户产生购车意向，开始关注NIO',
                'business_value': 'CRITICAL - 购车漏斗顶端',
                'features': [
                    'fellow_follow_decision_maker',
                    'fellow_follow_intention_nio_confirm', 
                    'fellow_follow_intention_test_drive',
                    'user_core_answer_sales_pc_nio',  # 回答销售问题
                    'user_core_fl_f2f_interview_nio',  # Fellow面对面访谈
                    'user_core_fl_offline_im_nio',     # Fellow线下消息
                ],
                'time_windows': ['1d', '7d', '30d', 'DSLA'],
                'key_indicators': ['明确决策意向', 'Fellow深度互动', '销售咨询频次']
            },
            
            '2_信息搜索阶段': {
                'description': '主动搜索产品信息，了解NIO生态',
                'business_value': 'HIGH - 信息获取行为',
                'features': [
                    'app_search_intention',
                    'user_core_search_nioapp',
                    'user_core_view_cm_hp_nioapp',     # 查看商城主页
                    'user_core_view_cm_dp_nioapp',     # 查看商城详情页
                    'user_core_view_service_intro',    # 查看服务介绍
                ],
                'time_windows': ['1d', '7d', '30d', '180d'],
                'key_indicators': ['搜索频次', '页面浏览深度', '服务关注度']
            },
            
            '3_配置决策阶段': {
                'description': '选择车型配置，对比不同方案',
                'business_value': 'CRITICAL - 具体配置决策',
                'features': [
                    'user_core_save_veh_cgf_nio',      # 保存车辆配置
                    'user_core_del_veh_cgf_nio',       # 删除车辆配置
                    'user_core_view_used_veh',         # 查看二手车(对比)
                ],
                'time_windows': ['1d', '7d', '30d', '60d'],
                'key_indicators': ['配置保存次数', '配置修改频次', '对比行为']
            },
            
            '4_试驾体验阶段': {
                'description': '预约试驾，实际体验产品',
                'business_value': 'CRITICAL - 关键转化节点',
                'features': [
                    'user_core_book_td_nio',           # 预约试驾
                    'user_core_exp_td_nio',            # 试驾体验
                    'user_core_visit_nh',              # 访问NIO House
                    'user_core_exp_charging_nio',      # 充电体验
                ],
                'time_windows': ['1d', '7d', '30d', 'DSLA'],
                'key_indicators': ['试驾完成率', 'NIO House访问', '充电体验']
            },
            
            '5_购车决策阶段': {
                'description': '确定购买，完成交易流程',
                'business_value': 'CRITICAL - 最终转化',
                'features': [
                    'user_core_lock_ncar_nio',         # 锁定新车
                    'user_core_pay_ncar_dp_nio',       # 支付新车定金
                    'user_core_first_reg_leads_nio',   # 首次注册线索
                    'user_core_unfirst_reg_leads_nio', # 非首次注册线索
                ],
                'time_windows': ['1d', '7d', '30d', 'DSLA'],
                'key_indicators': ['锁车行为', '定金支付', '线索转化']
            },
            
            '6_后续行为阶段': {
                'description': '购买后的服务体验和社区参与',
                'business_value': 'MEDIUM - 服务体验和复购',
                'features': [
                    'user_core_exp_maintenance_nio',   # 维保体验
                    'user_core_checkin_nioapp',        # APP签到
                    'user_core_sign_up_comm_act',      # 社区活动报名
                    'user_core_collect_checkin_prize', # 收集签到奖励
                    'user_core_buy_cm_nioapp',         # 商城购买
                    'user_core_buy_nl_nioapp',         # 牛粒购买
                ],
                'time_windows': ['1d', '7d', '30d', '60d'],
                'key_indicators': ['服务体验', '社区活跃度', '生态参与度']
            }
        }
        
    def analyze_historical_vs_current(self):
        """分析历史高性能版本vs当前版本的流程覆盖度"""
        print("=== 购车流程特征覆盖度分析 ===")
        
        # 加载历史版本配置
        with open('src/evaluation/20250317_v1/sample_20250311_v7-20250311_feature_column.json', 'r') as f:
            hist_config = json.load(f)
        
        # 加载当前版本配置
        with open('src/configs/models/enhanced_60_features_curated.json', 'r') as f:
            curr_config = json.load(f)
        
        hist_features = set(hist_config['InputGeneral']['features'] + hist_config['InputScene']['features'])
        curr_features = set(curr_config['InputGeneral']['features'] + curr_config['InputScene']['features'])
        
        for stage_name, stage_info in self.workflow_stages.items():
            print(f"\n{stage_name}: {stage_info['description']}")
            print(f"业务价值: {stage_info['business_value']}")
            
            # 统计每个阶段的特征覆盖度
            stage_hist_count = 0
            stage_curr_count = 0
            missing_features = []
            
            for base_feature in stage_info['features']:
                for time_window in stage_info['time_windows']:
                    feature_name = f"{base_feature}_{time_window}_cnt" if time_window != 'DSLA' else f"{base_feature}_{time_window}"
                    
                    if feature_name in hist_features:
                        stage_hist_count += 1
                    if feature_name in curr_features:
                        stage_curr_count += 1
                    else:
                        # 检查是否存在这类特征
                        if any(base_feature in f for f in hist_features):
                            missing_features.append(feature_name)
            
            print(f"  历史版本覆盖: {stage_hist_count} 个特征")
            print(f"  当前版本覆盖: {stage_curr_count} 个特征")
            print(f"  覆盖率: {stage_curr_count/max(stage_hist_count,1)*100:.1f}%")
            
            if missing_features[:3]:  # 显示前3个缺失特征
                print(f"  关键缺失: {', '.join(missing_features[:3])}")
    
    def generate_workflow_optimized_config(self):
        """生成基于购车流程优化的特征配置"""
        print(f"\n=== 购车流程优化特征选择策略 ===")
        
        # 加载历史版本以获取可用特征
        with open('src/evaluation/20250317_v1/sample_20250311_v7-20250311_feature_column.json', 'r') as f:
            hist_config = json.load(f)
        
        available_features = set(hist_config['InputGeneral']['features'] + hist_config['InputScene']['features'])
        
        # 按流程优先级选择特征
        selected_features = {
            'InputGeneral': [],
            'InputScene': []
        }
        
        # 基础必选特征(用户画像)
        base_features = [
            'user_core_user_gender',
            'user_core_user_age_group', 
            'user_core_resident_city',
            'user_core_is_nio_employee',
            'user_core_pred_career_type',
            'user_core_nio_user_identity'
        ]
        
        for feature in base_features:
            if feature in available_features:
                selected_features['InputGeneral'].append(feature)
        
        # 按购车流程优先级选择特征
        priority_order = ['1_意向产生阶段', '4_试驾体验阶段', '5_购车决策阶段', '3_配置决策阶段', '2_信息搜索阶段', '6_后续行为阶段']
        
        feature_budget = 80  # 总预算80个特征
        current_count = len(selected_features['InputGeneral'])
        
        for stage_name in priority_order:
            stage_info = self.workflow_stages[stage_name]
            stage_budget = min(15, feature_budget - current_count)  # 每个阶段最多15个特征
            
            print(f"\n{stage_name} - 分配预算: {stage_budget}个特征")
            
            stage_features = []
            for base_feature in stage_info['features']:
                if current_count >= feature_budget:
                    break
                    
                # 为每个基础特征选择最重要的时间窗口
                time_windows = stage_info['time_windows']
                selected_windows = []
                
                if stage_info['business_value'] == 'CRITICAL':
                    # 关键阶段保留更多时间窗口
                    selected_windows = ['1d', '30d', 'DSLA'] if 'DSLA' in time_windows else ['1d', '30d']
                else:
                    # 非关键阶段保留核心时间窗口
                    selected_windows = ['1d', '30d']
                
                for time_window in selected_windows:
                    if current_count >= feature_budget:
                        break
                        
                    feature_name = f"{base_feature}_{time_window}_cnt" if time_window != 'DSLA' else f"{base_feature}_{time_window}"
                    
                    if feature_name in available_features:
                        stage_features.append(feature_name)
                        current_count += 1
                        
                        if len(stage_features) >= stage_budget:
                            break
            
            selected_features['InputGeneral'].extend(stage_features)
            print(f"  实际选择: {len(stage_features)}个特征")
            
            if current_count >= feature_budget:
                break
        
        # Scene特征保持原有配置
        scene_features = [
            'user_core_nio_user_identity',
            'intention_stage',
            'intention_status', 
            'intention_create_time_days',
            'user_create_days',
            'user_register_days'
        ]
        
        for feature in scene_features:
            if feature in available_features:
                selected_features['InputScene'].append(feature)
        
        total_features = len(selected_features['InputGeneral']) + len(selected_features['InputScene'])
        print(f"\n=== 优化后配置统计 ===")
        print(f"InputGeneral: {len(selected_features['InputGeneral'])} 个特征")
        print(f"InputScene: {len(selected_features['InputScene'])} 个特征")
        print(f"总计: {total_features} 个特征")
        
        return selected_features
    
    def save_workflow_config(self, selected_features, output_path):
        """保存购车流程优化的配置文件"""
        # 基于历史版本的配置结构
        with open('src/evaluation/20250317_v1/sample_20250311_v7-20250311_feature_column.json', 'r') as f:
            base_config = json.load(f)
        
        # 创建新配置
        new_config = {
            "network_name": "EPMMOENet",
            "train_dates": ["20240430"],
            "test_dates": ["20240531"],
            "mask_label": "mask_label",  # 关键：启用多任务学习
            "use_multitask": True,       # 关键：启用多任务学习
            "InputGeneral": {
                "features": selected_features['InputGeneral']
            },
            "InputScene": {
                "features": selected_features['InputScene']
            },
            "InputSeqSet": {
                "Set": ["main_seq", "car_seq"],
                "SetInfo": {
                    "main_seq": ["user_core_action_code_seq", "user_core_action_day_seq"],
                    "car_seq": ["user_car_core_action_code_seq", "user_car_core_action_day_seq", "user_car_core_action_veh_model_seq"]
                }
            },
            "RawLabel": {
                "m_purchase_days_nio_new_car": {},
                "d_purchase_days_nio_new_car": {},
                "pos_flag": {},
                "mask_label": {}  # 关键：多任务学习标签
            }
        }
        
        # 只包含实际存在的特征的RawFeature配置
        new_config["RawFeature"] = {}
        for feature in selected_features['InputGeneral'] + selected_features['InputScene']:
            if feature in base_config.get('RawFeature', {}):
                new_config["RawFeature"][feature] = base_config["RawFeature"][feature]
        
        # 保存配置
        with open(output_path, 'w') as f:
            json.dump(new_config, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 购车流程优化配置已保存到: {output_path}")
        return new_config

def main():
    """主函数"""
    print("NIO转化率预测模型 - 购车流程特征优化分析")
    print("=" * 60)
    
    analyzer = PurchaseWorkflowAnalyzer()
    
    # 定义购车流程
    analyzer.define_purchase_workflow()
    
    # 分析历史vs当前版本的覆盖度
    analyzer.analyze_historical_vs_current()
    
    # 生成流程优化的特征配置
    selected_features = analyzer.generate_workflow_optimized_config()
    
    # 保存优化配置
    output_path = "src/configs/models/enhanced_workflow_optimized.json"
    analyzer.save_workflow_config(selected_features, output_path)
    
    print(f"\n=== 核心优化点总结 ===")
    print(f"✅ 启用多任务学习 (mask_label)")
    print(f"✅ 覆盖完整购车流程 (意向→搜索→配置→试驾→购车→后续)")
    print(f"✅ 突出关键转化节点特征")
    print(f"✅ 平衡特征数量与业务价值")
    print(f"✅ 保留历史高性能版本的核心架构")

if __name__ == "__main__":
    main()