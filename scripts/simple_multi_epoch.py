#!/usr/bin/env python
"""
简化多epoch训练脚本 - 基于成功的统一训练测试逻辑
"""

import os
import sys
import logging
import time
import argparse
from pathlib import Path
from datetime import datetime
import json
import tensorflow as tf
import numpy as np
import pandas as pd

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.configs.unified_config_manager import unified_config_manager
from src.models.adaptive_model_factory import adaptive_factory
from src.data.loader import DataLoader

# 设置日志到logs目录
logs_dir = project_root / "logs"
logs_dir.mkdir(exist_ok=True)

def setup_logging():
    """设置日志"""
    log_file = logs_dir / f"simple_multi_epoch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_real_data():
    """加载真实数据"""
    logger = logging.getLogger(__name__)
    
    try:
        # 加载配置
        config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")
        
        # 加载数据
        data_loader = DataLoader(
            dataset_code="data/dataset_nio_new_car_v15",
            dataset_config_path="src/configs/datasets/dataset_nio_new_car_v15.json"
        )
        
        logger.info("   加载训练数据: ['20240430']")
        df_raw = data_loader.load_dataset(['20240430'])
        
        # 处理标签列格式
        logger.info("   处理标签列格式...")
        if 'm_purchase_days_nio_new_car' in df_raw.columns:
            df_raw['m_purchase_days_nio_new_car'] = df_raw['m_purchase_days_nio_new_car'].apply(
                lambda x: json.loads(x) if isinstance(x, str) else x
            )
        
        logger.info("   ✅ 真实数据加载成功")
        
        # 采样数据以加快训练
        np.random.seed(42)
        sample_size = min(2000, len(df_raw))  # 最多2000样本用于多epoch训练
        
        if len(df_raw) > sample_size:
            sampled_indices = np.random.choice(len(df_raw), sample_size, replace=False)
            df_sample = df_raw.iloc[sampled_indices].copy().reset_index(drop=True)
        else:
            df_sample = df_raw.copy()
        
        logger.info(f"   采样数据: {df_sample.shape}")
        
        # 分割为训练和测试
        train_size = int(0.8 * len(df_sample))
        df_train = df_sample.iloc[:train_size].copy()
        df_test = df_sample.iloc[train_size:].copy()
        
        logger.info(f"   训练数据: {df_train.shape}")
        logger.info(f"   测试数据: {df_test.shape}")
        
        return df_train, df_test
        
    except Exception as e:
        logger.error(f"❌ 数据加载失败: {e}")
        return None, None

def prepare_adaptive_training_data(data, preprocessing_info):
    """为自适应模型准备训练数据"""
    if data is None:
        return None, None
        
    feature_specs = preprocessing_info['feature_specs']
    
    # 为每个特征准备对应的数据
    X_dict = {}
    for feature_name, spec in feature_specs.items():
        if feature_name in data.columns:
            if spec.feature_type.value == 'numerical':
                # 数值特征：填充NaN并转换类型
                X_dict[feature_name] = data[feature_name].fillna(0).astype(np.float32).values
            elif spec.feature_type.value in ['categorical', 'text', 'sequence']:
                # 字符串特征：处理Categorical类型的fillna问题
                if hasattr(data[feature_name], 'cat'):  # pandas Categorical
                    # 如果是Categorical类型，先转换为字符串再处理缺失值
                    string_values = data[feature_name].astype(str).replace('nan', 'unknown').values
                else:
                    # 普通字符串列
                    string_values = data[feature_name].fillna('unknown').astype(str).values
                X_dict[feature_name] = string_values
            else:
                # 其他类型特征，同样处理Categorical
                if hasattr(data[feature_name], 'cat'):  # pandas Categorical
                    # 先转换为字符串再处理缺失值
                    string_values = data[feature_name].astype(str).replace('nan', 'unknown').values
                else:
                    string_values = data[feature_name].fillna('unknown').astype(str).values
                X_dict[feature_name] = string_values
        else:
            # 如果特征在数据中不存在，使用默认值
            batch_size = len(data)
            if spec.feature_type.value == 'numerical':
                X_dict[feature_name] = np.zeros(batch_size, dtype=np.float32)
            else:
                X_dict[feature_name] = np.full(batch_size, 'unknown', dtype=str)
    
    # 标签处理
    y = np.array([row if isinstance(row, list) else [0,0,0,0,0,0] 
                 for row in data['m_purchase_days_nio_new_car']], dtype=np.float32)
    
    return X_dict, y

def train_multi_epoch_model(df_train, df_test, epochs=10):
    """多epoch训练模型"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"🧠 使用自适应模型工厂构建模型...")
        
        # 构建自适应模型
        model, preprocessing_info = adaptive_factory.create_model_from_data(
            train_data=df_train,
            target_column="m_purchase_days_nio_new_car"
        )
        
        logger.info(f"✅ 自适应模型构建成功，参数数量: {model.count_params():,}")
        
        # 准备训练数据
        logger.info("   准备训练数据...")
        X_train, y_train = prepare_adaptive_training_data(df_train, preprocessing_info)
        X_test, y_test = prepare_adaptive_training_data(df_test, preprocessing_info)
        
        if X_train is None:
            logger.error("❌ 训练数据准备失败")
            return None, None, 0
        
        # 编译模型 - 多标签分类问题
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',  # 多标签分类使用binary_crossentropy
            metrics=['accuracy']
        )
        
        # 设置回调
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=5,
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=3,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        # 开始训练
        logger.info(f"🔥 开始训练 {epochs} 个epoch...")
        start_time = time.time()
        
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=epochs,
            batch_size=256,
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - start_time
        logger.info(f"✅ 训练完成！总耗时: {training_time:.2f}秒")
        
        return model, history, training_time
        
    except Exception as e:
        logger.error(f"❌ 模型训练失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None, None, 0

def analyze_training_results(history, training_time):
    """分析训练结果"""
    logger = logging.getLogger(__name__)
    
    logger.info("📊 训练结果深度分析:")
    
    if not history or not history.history:
        logger.error("❌ 没有训练历史记录")
        return {}
    
    # 基本指标
    epochs_completed = len(history.history['loss'])
    final_train_loss = history.history['loss'][-1]
    final_train_acc = history.history['accuracy'][-1]
    final_val_loss = history.history['val_loss'][-1]
    final_val_acc = history.history['val_accuracy'][-1]
    
    # 最佳性能
    best_val_loss = min(history.history['val_loss'])
    best_val_loss_epoch = history.history['val_loss'].index(best_val_loss) + 1
    best_val_acc = max(history.history['val_accuracy'])
    best_val_acc_epoch = history.history['val_accuracy'].index(best_val_acc) + 1
    
    # 学习率衰减检测
    has_lr_reduction = any(['lr' in str(key).lower() for key in history.history.keys()])
    
    # 早停检测
    early_stopped = epochs_completed < 15  # 假设最大epoch为15
    
    # 过拟合检测
    train_val_loss_gap = final_train_loss - final_val_loss
    if train_val_loss_gap > 1.0:
        overfitting_status = "severe_overfitting"
    elif train_val_loss_gap > 0.5:
        overfitting_status = "mild_overfitting"
    elif train_val_loss_gap < -0.5:
        overfitting_status = "underfitting"
    else:
        overfitting_status = "balanced"
    
    # 收敛分析
    if len(history.history['val_loss']) >= 3:
        recent_losses = history.history['val_loss'][-3:]
        loss_variance = np.var(recent_losses)
        converged = loss_variance < 0.01
    else:
        converged = False
    
    # 输出分析结果
    logger.info(f"   📈 训练轮数: {epochs_completed}")
    logger.info(f"   📉 最终训练损失: {final_train_loss:.4f}")
    logger.info(f"   📈 最终训练准确率: {final_train_acc:.4f}")
    logger.info(f"   📉 最终验证损失: {final_val_loss:.4f}")
    logger.info(f"   📈 最终验证准确率: {final_val_acc:.4f}")
    logger.info(f"   ⭐ 最佳验证损失: {best_val_loss:.4f} (第{best_val_loss_epoch}轮)")
    logger.info(f"   ⭐ 最佳验证准确率: {best_val_acc:.4f} (第{best_val_acc_epoch}轮)")
    logger.info(f"   ⏱️ 训练时间: {training_time:.2f}秒")
    logger.info(f"   🔄 早停触发: {'是' if early_stopped else '否'}")
    logger.info(f"   🎯 过拟合状态: {overfitting_status}")
    logger.info(f"   📊 收敛状态: {'已收敛' if converged else '未收敛'}")
    
    # 性能诊断
    if final_val_acc < 0.15:
        logger.info("   ⚠️ 验证准确率很低，可能需要:")
        logger.info("      - 检查特征工程质量")
        logger.info("      - 调整模型架构")
        logger.info("      - 增加训练数据")
    elif final_val_acc < 0.25:
        logger.info("   📈 验证准确率中等，可以尝试:")
        logger.info("      - 特征选择优化")
        logger.info("      - 超参数调优")
        logger.info("      - 增加模型复杂度")
    else:
        logger.info("   🎯 验证准确率良好")
    
    if overfitting_status == "severe_overfitting":
        logger.info("   ⚠️ 严重过拟合，建议:")
        logger.info("      - 增加正则化 (dropout, L1/L2)")
        logger.info("      - 减少模型复杂度")
        logger.info("      - 增加训练数据")
    elif overfitting_status == "mild_overfitting":
        logger.info("   📊 轻微过拟合，可考虑:")
        logger.info("      - 调整学习率")
        logger.info("      - 增加dropout")
        logger.info("      - 早停更严格")
    elif overfitting_status == "underfitting":
        logger.info("   📈 可能欠拟合，可尝试:")
        logger.info("      - 增加模型复杂度")
        logger.info("      - 降低正则化")
        logger.info("      - 训练更多epoch")
    
    # 基于数据特征的分析
    logger.info("   🔍 基于数据特征的模型行为分析:")
    logger.info("      - 模型需要学习355个特征的复杂模式")
    logger.info("      - 正样本率4%的不平衡数据挑战")
    logger.info("      - 6个月时间窗口的多分类任务")
    
    return {
        'epochs_completed': epochs_completed,
        'final_train_loss': final_train_loss,
        'final_train_acc': final_train_acc,
        'final_val_loss': final_val_loss,
        'final_val_acc': final_val_acc,
        'best_val_loss': best_val_loss,
        'best_val_loss_epoch': best_val_loss_epoch,
        'best_val_acc': best_val_acc,
        'best_val_acc_epoch': best_val_acc_epoch,
        'training_time': training_time,
        'early_stopped': early_stopped,
        'overfitting_status': overfitting_status,
        'converged': converged
    }

def save_training_report(results, history):
    """保存训练报告"""
    logger = logging.getLogger(__name__)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = logs_dir / f"simple_multi_epoch_report_{timestamp}.json"
    
    # 准备报告数据，确保所有值都可以JSON序列化
    serializable_results = {}
    for k, v in results.items():
        if isinstance(v, (bool, np.bool_)):
            serializable_results[k] = bool(v)
        elif isinstance(v, (int, np.integer)):
            serializable_results[k] = int(v)
        elif isinstance(v, (float, np.floating)):
            serializable_results[k] = float(v)
        else:
            serializable_results[k] = str(v)
    
    report = {
        "training_info": {
            "test_name": "简化多Epoch训练测试",
            "start_time": datetime.now().isoformat(),
            "epochs_requested": results.get('epochs_completed', 0)
        },
        "results": serializable_results,
        "training_history": {
            k: [float(v) for v in values] for k, values in history.history.items()
        } if history and history.history else {}
    }
    
    # 保存报告
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info(f"📄 训练报告已保存: {report_file}")
    return report_file

def main():
    parser = argparse.ArgumentParser(description='简化多epoch训练脚本')
    parser.add_argument('--epochs', type=int, default=15, help='训练轮数 (默认: 15)')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    logger.info(f"🚀 ========简化多Epoch训练开始 - {args.epochs}个epoch========")
    
    try:
        # 1. 加载数据
        logger.info("1️⃣ 数据加载")
        df_train, df_test = load_real_data()
        if df_train is None:
            logger.error("❌ 数据加载失败，退出")
            return False
        
        # 2. 训练模型
        logger.info("2️⃣ 模型训练")
        model, history, training_time = train_multi_epoch_model(df_train, df_test, epochs=args.epochs)
        if model is None:
            logger.error("❌ 模型训练失败，退出")
            return False
        
        # 3. 分析结果
        logger.info("3️⃣ 结果分析")
        results = analyze_training_results(history, training_time)
        
        # 4. 保存报告
        logger.info("4️⃣ 保存报告")
        report_file = save_training_report(results, history)
        
        logger.info("✅ 简化多Epoch训练完成!")
        logger.info(f"🎯 最终验证准确率: {results.get('final_val_acc', 0):.4f}")
        logger.info(f"📊 最佳验证准确率: {results.get('best_val_acc', 0):.4f} (第{results.get('best_val_acc_epoch', 0)}轮)")
        logger.info(f"🔄 训练状态: {results.get('overfitting_status', 'unknown')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 训练过程失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()