#!/usr/bin/env python
"""
NIO转化率预测模型 - 统一训练脚本

整合了现代化特征工程管道和模型训练功能
支持多种训练模式：快速测试、完整训练、基准对比
"""

import os
import sys
import logging
import time
import argparse
from pathlib import Path
from datetime import datetime
import json
import numpy as np
import pandas as pd
import tensorflow as tf

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.data.loader import DataLoader
from src.features.modern_pipeline import ModernFeaturePipeline
from src.models.modern_model_builder import ModernModelBuilder, ModelConfig, create_dataset_from_pipeline

# 设置日志到logs目录
logs_dir = project_root / "logs"
logs_dir.mkdir(exist_ok=True)


def setup_logging(verbose: bool = False):
    """设置日志"""
    log_file = logs_dir / f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def load_data():
    """加载训练和测试数据"""
    logger = logging.getLogger(__name__)
    
    try:
        data_loader = DataLoader(
            dataset_code="data/dataset_nio_new_car_v15",
            dataset_config_path="src/configs/datasets/dataset_nio_new_car_v15.json"
        )
        
        logger.info("加载训练数据...")
        df_train = data_loader.load_dataset(['20240430'])
        
        logger.info("加载测试数据...")
        df_test = data_loader.load_dataset(['20240531'])
        
        # 处理标签列
        def process_labels(df):
            if 'm_purchase_days_nio_new_car' in df.columns:
                df['m_purchase_days_nio_new_car'] = df['m_purchase_days_nio_new_car'].apply(
                    lambda x: json.loads(x) if isinstance(x, str) else x
                )
            return df
        
        df_train = process_labels(df_train)
        df_test = process_labels(df_test)
        
        logger.info(f"训练数据: {df_train.shape}")
        logger.info(f"测试数据: {df_test.shape}")
        
        return df_train, df_test
        
    except Exception as e:
        logger.error(f"❌ 数据加载失败: {e}")
        return None, None


def prepare_labels(df_train, df_test):
    """准备标签数据"""
    logger = logging.getLogger(__name__)
    
    # 提取标签
    train_labels = np.array(df_train['m_purchase_days_nio_new_car'].tolist())
    test_labels = np.array(df_test['m_purchase_days_nio_new_car'].tolist())
    
    # 计算转化率统计
    train_conversion_rates = train_labels.mean(axis=0)
    test_conversion_rates = test_labels.mean(axis=0)
    
    logger.info("各月转化率分析:")
    for i, (train_rate, test_rate) in enumerate(zip(train_conversion_rates, test_conversion_rates)):
        logger.info(f"  月份{i+1}: 训练集={train_rate:.3%}, 测试集={test_rate:.3%}")
    
    train_any_conversion = (train_labels.sum(axis=1) > 0).mean()
    test_any_conversion = (test_labels.sum(axis=1) > 0).mean()
    logger.info(f"总体转化率: 训练集={train_any_conversion:.3%}, 测试集={test_any_conversion:.3%}")
    
    return train_labels, test_labels


def setup_feature_pipeline(df_train):
    """设置特征工程管道"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔧 初始化现代化特征工程管道...")
    
    try:
        # 创建并配置管道
        pipeline = ModernFeaturePipeline()
        pipeline.load_config_from_yaml("sample_20250311_v7-20250311")
        
        # 拟合管道
        start_time = time.time()
        pipeline.fit(df_train)
        fit_time = time.time() - start_time
        
        logger.info(f"✅ 管道拟合完成，耗时: {fit_time:.2f}秒")
        
        # 获取管道摘要
        summary = pipeline.get_summary()
        logger.info("管道摘要:")
        logger.info(f"  总特征数: {summary['total_features']}")
        logger.info(f"  特征类型分布: {summary['feature_types']}")
        
        return pipeline, summary
        
    except Exception as e:
        logger.error(f"❌ 特征管道设置失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None, None


def create_model(pipeline, model_type: str = "modern"):
    """创建模型"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"🧠 创建{model_type}模型...")
    
    try:
        if model_type == "modern":
            # 现代化模型配置
            config = ModelConfig(
                output_dim=6,
                hidden_units=[512, 256, 128],
                use_cross_net=True,
                cross_layers=3,
                use_attention=True,
                use_focal_loss=True,
                dropout_rate=0.3,
                learning_rate=0.001
            )
        elif model_type == "simple":
            # 简化模型配置  
            config = ModelConfig(
                output_dim=6,
                hidden_units=[256, 128],
                use_cross_net=False,
                use_attention=False,
                use_focal_loss=True,
                dropout_rate=0.2,
                learning_rate=0.001
            )
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 创建模型构建器
        builder = ModernModelBuilder(config)
        
        # 构建模型
        start_time = time.time()
        model = builder.build_model(pipeline)
        build_time = time.time() - start_time
        
        logger.info(f"✅ 模型构建完成，耗时: {build_time:.2f}秒")
        logger.info(f"  模型参数数量: {model.count_params():,}")
        
        return model, builder
        
    except Exception as e:
        logger.error(f"❌ 模型创建失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None, None


def train_model(model, builder, train_features, test_features, train_labels, test_labels, 
                epochs: int = 10, model_name: str = "model"):
    """训练模型"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"🔥 开始模型训练（{epochs}个epoch）...")
    
    try:
        # 创建数据集
        train_dataset = create_dataset_from_pipeline(
            train_features, 
            tf.constant(train_labels, dtype=tf.float32),
            batch_size=1024,
            shuffle=True
        )
        
        test_dataset = create_dataset_from_pipeline(
            test_features,
            tf.constant(test_labels, dtype=tf.float32),
            batch_size=1024,
            shuffle=False
        )
        
        # 获取回调
        model_save_path = logs_dir / f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.h5"
        callbacks = builder.get_callbacks(str(model_save_path))
        
        # 开始训练
        start_time = time.time()
        
        history = model.fit(
            train_dataset,
            validation_data=test_dataset,
            epochs=epochs,
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - start_time
        logger.info(f"✅ 模型训练完成！总耗时: {training_time:.2f}秒")
        
        return history, training_time
        
    except Exception as e:
        logger.error(f"❌ 模型训练失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None, 0


def analyze_results(history, training_time, model, model_name: str):
    """分析训练结果"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"📊 {model_name}训练结果分析:")
    
    if not history or not history.history:
        logger.error("❌ 没有训练历史记录")
        return {}
    
    # 基本指标
    epochs_completed = len(history.history['loss'])
    final_train_loss = history.history['loss'][-1]
    final_val_loss = history.history['val_loss'][-1]
    
    # PR-AUC (更适合不平衡数据)
    final_train_pr_auc = history.history.get('pr_auc', [0])[-1]
    final_val_pr_auc = history.history.get('val_pr_auc', [0])[-1]
    
    # 最佳性能
    best_val_pr_auc = max(history.history.get('val_pr_auc', [0]))
    best_val_pr_auc_epoch = history.history.get('val_pr_auc', [0]).index(best_val_pr_auc) + 1
    
    # 精确率和召回率
    final_precision = history.history.get('val_precision', [0])[-1]
    final_recall = history.history.get('val_recall', [0])[-1]
    
    # 输出分析结果
    logger.info(f"   📈 训练轮数: {epochs_completed}")
    logger.info(f"   📉 最终训练损失: {final_train_loss:.4f}")
    logger.info(f"   📉 最终验证损失: {final_val_loss:.4f}")
    logger.info(f"   📊 最终验证PR-AUC: {final_val_pr_auc:.4f}")
    logger.info(f"   ⭐ 最佳验证PR-AUC: {best_val_pr_auc:.4f} (第{best_val_pr_auc_epoch}轮)")
    logger.info(f"   🎯 最终精确率: {final_precision:.4f}")
    logger.info(f"   🎯 最终召回率: {final_recall:.4f}")
    logger.info(f"   ⏱️ 训练时间: {training_time:.2f}秒")
    logger.info(f"   🏗️ 模型参数: {model.count_params():,}")
    
    return {
        'model_name': model_name,
        'epochs_completed': epochs_completed,
        'final_train_loss': float(final_train_loss),
        'final_val_loss': float(final_val_loss),
        'final_val_pr_auc': float(final_val_pr_auc),
        'best_val_pr_auc': float(best_val_pr_auc),
        'best_val_pr_auc_epoch': int(best_val_pr_auc_epoch),
        'final_precision': float(final_precision),
        'final_recall': float(final_recall),
        'training_time': float(training_time),
        'model_params': int(model.count_params())
    }


def save_training_report(results_list, pipeline_summary):
    """保存训练报告"""
    logger = logging.getLogger(__name__)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = logs_dir / f"training_report_{timestamp}.json"
    
    report = {
        "experiment_info": {
            "timestamp": datetime.now().isoformat(),
            "framework": "现代化特征工程管道 v2.0",
            "total_models": len(results_list)
        },
        "feature_pipeline": {
            "total_features": pipeline_summary.get('total_features', 0),
            "feature_types": pipeline_summary.get('feature_types', {}),
        },
        "model_results": results_list,
        "summary": {
            "best_model": max(results_list, key=lambda x: x.get('best_val_pr_auc', 0)) if results_list else None
        }
    }
    
    # 保存报告
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info(f"📄 训练报告已保存: {report_file}")
    return report_file


def main():
    parser = argparse.ArgumentParser(description='NIO转化率预测模型统一训练脚本')
    parser.add_argument('--mode', type=str, default='modern', 
                       choices=['test', 'modern', 'simple', 'compare'],
                       help='训练模式: test(快速测试), modern(现代化模型), simple(简化模型), compare(对比模式)')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--verbose', action='store_true', help='详细日志输出')
    
    args = parser.parse_args()
    
    logger = setup_logging(args.verbose)
    
    logger.info(f"🚀 ========NIO转化率预测模型训练开始 - {args.mode}模式========")
    
    try:
        # 1. 加载数据
        logger.info("1️⃣ 加载数据")
        df_train, df_test = load_data()
        if df_train is None:
            return False
        
        # 2. 准备标签
        logger.info("2️⃣ 准备标签数据")
        train_labels, test_labels = prepare_labels(df_train, df_test)
        
        # 3. 设置特征管道
        logger.info("3️⃣ 设置特征工程管道")
        pipeline, pipeline_summary = setup_feature_pipeline(df_train)
        if pipeline is None:
            return False
        
        # 4. 转换数据
        logger.info("4️⃣ 转换特征数据")
        train_features = pipeline.transform(df_train)
        test_features = pipeline.transform(df_test)
        
        results_list = []
        
        if args.mode == 'test':
            # 快速测试模式
            logger.info("5️⃣ 快速测试模式")
            return True
            
        elif args.mode == 'compare':
            # 对比模式 - 训练多个模型
            logger.info("5️⃣ 对比训练模式")
            
            for model_type in ['simple', 'modern']:
                logger.info(f"训练{model_type}模型...")
                
                model, builder = create_model(pipeline, model_type)
                if model is None:
                    continue
                
                history, training_time = train_model(
                    model, builder, train_features, test_features,
                    train_labels, test_labels, epochs=args.epochs, 
                    model_name=model_type
                )
                
                if history is not None:
                    results = analyze_results(history, training_time, model, model_type)
                    results_list.append(results)
        
        else:
            # 单模型训练
            logger.info(f"5️⃣ {args.mode}模型训练")
            
            model, builder = create_model(pipeline, args.mode)
            if model is None:
                return False
            
            history, training_time = train_model(
                model, builder, train_features, test_features,
                train_labels, test_labels, epochs=args.epochs,
                model_name=args.mode
            )
            
            if history is not None:
                results = analyze_results(history, training_time, model, args.mode)
                results_list.append(results)
        
        # 6. 保存报告
        if results_list:
            logger.info("6️⃣ 保存训练报告")
            save_training_report(results_list, pipeline_summary)
            
            # 输出最终总结
            logger.info("📋 训练总结:")
            for result in results_list:
                logger.info(f"  {result['model_name']}: PR-AUC={result['best_val_pr_auc']:.4f}, "
                          f"参数={result['model_params']:,}, 时间={result['training_time']:.1f}s")
        
        logger.info("✅ 训练完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 训练过程失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)