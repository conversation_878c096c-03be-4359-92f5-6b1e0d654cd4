#!/usr/bin/env python
"""
统一优化测试脚本

这个脚本整合了所有优化功能：
1. 数据驱动的特征优化
2. 模型架构优化
3. 训练策略优化
4. 自动化实验管理
5. 性能对比和报告生成

使用方式：
python scripts/unified_optimization_test.py --mode [quick|full|batch]
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime

# Add project root to Python path
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.utils.experiment_manager import ExperimentManager


def setup_logging(log_level=logging.INFO):
    """设置日志系统"""
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f"unified_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger


def run_quick_test(logger):
    """
    快速测试模式 - 验证优化功能是否正常工作
    """
    logger.info("=" * 60)
    logger.info("开始快速测试模式")
    logger.info("=" * 60)
    
    try:
        # 初始化实验管理器
        experiment_manager = ExperimentManager(
            base_config_name="GOLDEN_CONFIG",
            experiment_dir="logs/experiments/quick_test"
        )
        
        # 运行数据驱动优化实验（快速模式）
        logger.info("运行数据驱动优化实验（快速模式）")
        result = experiment_manager.run_optimization_experiment(
            experiment_config_name="OPTIMIZATION_V8_DATA_DRIVEN",
            epochs=1,  # 快速测试只用1个epoch
            quick_test=True  # 启用快速测试模式
        )
        
        # 输出关键结果
        metrics = result.get('metrics', {})
        logger.info("快速测试结果:")
        logger.info(f"  Month_1 PR-AUC: {metrics.get('month_1_pr_auc', 'N/A')}")
        logger.info(f"  训练时间: {result.get('total_duration', 'N/A'):.2f}秒")
        logger.info(f"  模型参数: {result.get('stages', {}).get('training', {}).get('model_params', 'N/A'):,}")
        
        # 检查优化是否生效
        preprocessing_stage = result.get('stages', {}).get('preprocessing', {})
        optimization_applied = preprocessing_stage.get('optimization_applied', False)
        
        if optimization_applied:
            logger.info("✅ 特征优化已成功应用")
            train_report = preprocessing_stage.get('train_report', {})
            for step_name, step_result in train_report.get('steps', []):
                if step_name == '特征优化':
                    original_features = step_result.get('original_features', 0)
                    final_features = step_result.get('final_features', 0)
                    reduction_rate = step_result.get('reduction_rate', 0)
                    logger.info(f"  特征数量: {original_features} → {final_features} (减少{reduction_rate:.1%})")
        else:
            logger.warning("⚠️ 特征优化未应用")
        
        # 与基线对比
        baseline_comparison = result.get('baseline_comparison', {})
        if baseline_comparison and 'improvements' in baseline_comparison:
            logger.info("与基线对比:")
            for metric, improvement in baseline_comparison['improvements'].items():
                logger.info(f"  {metric}: {improvement['percentage']:+.1f}%")
        
        logger.info("快速测试完成 ✅")
        return True
        
    except Exception as e:
        logger.error(f"快速测试失败: {e}")
        return False


def run_full_optimization(logger):
    """
    完整优化模式 - 运行所有优化实验
    """
    logger.info("=" * 60)
    logger.info("开始完整优化模式")
    logger.info("=" * 60)
    
    try:
        # 初始化实验管理器
        experiment_manager = ExperimentManager(
            base_config_name="GOLDEN_CONFIG",
            experiment_dir="logs/experiments/full_optimization"
        )
        
        # 定义要运行的优化实验
        optimization_experiments = [
            "OPTIMIZATION_V8_DATA_DRIVEN",  # 数据驱动优化
            # 可以添加更多优化配置
        ]
        
        results = {}
        
        for experiment_name in optimization_experiments:
            logger.info(f"运行优化实验: {experiment_name}")
            
            try:
                result = experiment_manager.run_optimization_experiment(
                    experiment_config_name=experiment_name,
                    epochs=3,  # 完整模式使用3个epoch
                    quick_test=False
                )
                
                results[experiment_name] = result
                
                # 输出实验结果
                metrics = result.get('metrics', {})
                logger.info(f"实验 {experiment_name} 完成:")
                logger.info(f"  Month_1 PR-AUC: {metrics.get('month_1_pr_auc', 'N/A'):.4f}")
                logger.info(f"  Month_1 Recall@840: {metrics.get('month_1_recall_840', 'N/A'):.4f}")
                logger.info(f"  训练时间: {result.get('total_duration', 'N/A'):.2f}秒")
                
                # 与基线对比
                baseline_comparison = result.get('baseline_comparison', {})
                if baseline_comparison and 'improvements' in baseline_comparison:
                    logger.info("  与基线对比:")
                    for metric, improvement in baseline_comparison['improvements'].items():
                        logger.info(f"    {metric}: {improvement['percentage']:+.1f}%")
                
            except Exception as e:
                logger.error(f"实验 {experiment_name} 失败: {e}")
                results[experiment_name] = {'error': str(e)}
        
        # 生成优化摘要
        logger.info("=" * 60)
        logger.info("优化摘要")
        logger.info("=" * 60)
        
        successful_experiments = [name for name, result in results.items() if 'error' not in result]
        failed_experiments = [name for name, result in results.items() if 'error' in result]
        
        logger.info(f"成功实验: {len(successful_experiments)}/{len(optimization_experiments)}")
        logger.info(f"失败实验: {len(failed_experiments)}")
        
        if successful_experiments:
            # 找到最优实验
            best_experiment = None
            best_pr_auc = 0
            
            for exp_name in successful_experiments:
                pr_auc = results[exp_name].get('metrics', {}).get('month_1_pr_auc', 0)
                if pr_auc > best_pr_auc:
                    best_pr_auc = pr_auc
                    best_experiment = exp_name
            
            if best_experiment:
                logger.info(f"最优实验: {best_experiment}")
                logger.info(f"最优 PR-AUC: {best_pr_auc:.4f}")
                
                # 检查是否达到目标
                target_pr_auc = 0.27
                if best_pr_auc >= target_pr_auc:
                    logger.info(f"🎉 达到目标性能! PR-AUC {best_pr_auc:.4f} >= {target_pr_auc}")
                else:
                    logger.info(f"⚠️ 未达到目标性能，还需提升 {target_pr_auc - best_pr_auc:.4f}")
        
        # 获取实验摘要
        summary = experiment_manager.get_experiment_summary()
        logger.info(f"实验历史: {summary['total_experiments']}个实验")
        logger.info(f"成功率: {summary['success_rate']:.1%}")
        
        logger.info("完整优化完成 ✅")
        return True
        
    except Exception as e:
        logger.error(f"完整优化失败: {e}")
        return False


def run_batch_experiments(logger):
    """
    批量实验模式 - 运行多个配置的对比实验
    """
    logger.info("=" * 60)
    logger.info("开始批量实验模式")
    logger.info("=" * 60)
    
    try:
        # 初始化实验管理器
        experiment_manager = ExperimentManager(
            base_config_name="GOLDEN_CONFIG",
            experiment_dir="logs/experiments/batch"
        )
        
        # 定义批量实验配置
        batch_configs = [
            "GOLDEN_CONFIG",  # 基线
            "OPTIMIZATION_V8_DATA_DRIVEN",  # 数据驱动优化
            # 可以添加更多配置进行对比
        ]
        
        logger.info(f"批量运行 {len(batch_configs)} 个配置")
        
        # 运行批量实验
        batch_results = experiment_manager.run_batch_experiments(
            experiment_configs=batch_configs,
            epochs=3
        )
        
        # 分析批量结果
        logger.info("批量实验结果分析:")
        
        config_performances = {}
        for config_name, result in batch_results['results'].items():
            if 'error' not in result:
                pr_auc = result.get('metrics', {}).get('month_1_pr_auc', 0)
                training_time = result.get('total_duration', 0)
                config_performances[config_name] = {
                    'pr_auc': pr_auc,
                    'training_time': training_time
                }
                logger.info(f"  {config_name}: PR-AUC={pr_auc:.4f}, 时间={training_time:.1f}s")
            else:
                logger.error(f"  {config_name}: 失败 - {result['error']}")
        
        # 排序并显示最优配置
        if config_performances:
            sorted_configs = sorted(
                config_performances.items(),
                key=lambda x: x[1]['pr_auc'],
                reverse=True
            )
            
            logger.info("配置排名 (按PR-AUC):")
            for i, (config_name, perf) in enumerate(sorted_configs, 1):
                logger.info(f"  {i}. {config_name}: {perf['pr_auc']:.4f}")
        
        logger.info("批量实验完成 ✅")
        return True
        
    except Exception as e:
        logger.error(f"批量实验失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="统一优化测试脚本")
    parser.add_argument(
        '--mode',
        choices=['quick', 'full', 'batch'],
        default='quick',
        help='运行模式: quick(快速测试), full(完整优化), batch(批量实验)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细日志输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logging(log_level)
    
    logger.info(f"开始统一优化测试 - 模式: {args.mode}")
    
    try:
        success = False
        
        if args.mode == 'quick':
            success = run_quick_test(logger)
        elif args.mode == 'full':
            success = run_full_optimization(logger)
        elif args.mode == 'batch':
            success = run_batch_experiments(logger)
        
        if success:
            logger.info("🎉 优化测试成功完成!")
            return 0
        else:
            logger.error("❌ 优化测试失败!")
            return 1
            
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        return 1
    except Exception as e:
        logger.error(f"未预期的错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
