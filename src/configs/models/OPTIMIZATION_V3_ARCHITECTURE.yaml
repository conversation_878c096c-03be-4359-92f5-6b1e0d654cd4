# 优化实验V3: 架构优化
# 基于GOLDEN_CONFIG，优化模型架构参数
# 目标: 提升Month_1 PR-AUC > 0.27

_metadata:
  version: "optimization_v3_architecture"
  creation_date: "2025-06-16"
  base_config: "GOLDEN_CONFIG"
  hypothesis: "增加模型容量和专家数量可以提升性能"
  target_improvement: "Month_1 PR-AUC > 0.27"

# 模型基础配置 - 优化架构参数
model_config:
  network_name: "EPMMOENet"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
  mask_label: null
  use_multitask: false
  
  # 架构优化参数
  default_embedding_dimension: 16    # 从8增加到16，增强特征表示能力
  default_gru_dimension: 64          # 从32增加到64，增强序列建模能力
  expert_num: 12                     # 从8增加到12，增强专家网络容量
  use_cross_layer: true              # 保持特征交叉
  use_time_attention: true           # 保持时间注意力
  time_decay_factor: 0.01            # 从0.02降低到0.01，增强长期记忆
  
# 训练参数 - 保持最优损失函数配置
training_config:
  batch_size: 512                    # 从1024降低到512，更稳定的梯度
  loss_type: "focal"
  pos_weight: 25.0                   # 保持V2的优化结果
  use_month_weights: true
  focal_alpha: 0.3                   # 保持V2的优化结果
  focal_gamma: 2.5                   # 保持V2的优化结果
  learning_rate: 0.0003              # 从0.0005降低到0.0003，更稳定训练

# 输入模块配置 - 保持GOLDEN_CONFIG的35个特征
input_modules:
  InputGeneral:
    features:
      # 核心时间特征 (8个)
      - user_create_days
      - intention_intention_fail_days
      - app_search_intention_DSLA
      - intention_opportunity_create_days
      - user_core_unfirst_reg_leads_nio_DSLA
      - user_core_first_reg_leads_nio_DSLA
      - intention_create_time_days
      - user_register_days
      
      # 用户活跃度特征 (7个)
      - user_core_visit_nioapp_login_180d_cnt
      - user_core_visit_nioapp_login_90d_cnt
      - user_core_visit_nioapp_login_30d_cnt
      - user_core_visit_nioapp_login_60d_cnt
      - user_core_visit_nioapp_login_7d_cnt
      - user_core_visit_nioapp_login_1d_cnt
      - user_core_nio_value
      
      # 线索质量特征 (7个)
      - user_core_unfirst_reg_leads_nio_180d_cnt
      - user_core_unfirst_reg_leads_nio_90d_cnt
      - user_core_unfirst_reg_leads_nio_60d_cnt
      - user_core_unfirst_reg_leads_nio_30d_cnt
      - user_core_unfirst_reg_leads_nio_7d_cnt
      - user_core_user_curr_credit_amount
      - user_core_pred_has_other_vehicle
      
      # 应用使用特征 (3个)
      - user_core_nio_has_inviter
      - user_core_checkin_nioapp_60d_cnt
      - user_core_checkin_nioapp_90d_cnt
      
      # 同伴跟进特征 (3个)
      - fellow_follow_decision_maker
      - fellow_follow_intention_nio_confirm
      - fellow_follow_intention_test_drive
      
      # 用户画像特征 (4个)
      - user_core_user_gender
      - user_core_user_age_group
      - user_core_resident_city
      - user_core_pred_career_type
      - user_core_nio_user_identity
      
      # 意向状态特征 (2个)
      - intention_stage
      - intention_status

  InputScene:
    features: []

  InputSeqSet:
    Set: []
    SetInfo: {}

# 标签配置
labels:
  m_purchase_days_nio_new_car: {}
  d_purchase_days_nio_new_car: {}
  pos_flag: {}

# 特征处理配置 - 继承GOLDEN_CONFIG (简化版本，只列出关键特征)
features:
  # 数值特征 - 桶化处理
  user_create_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5]

  intention_intention_fail_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]

  app_search_intention_DSLA:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]
    missing_rate: 0.732

  intention_opportunity_create_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]

  user_core_unfirst_reg_leads_nio_DSLA:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]

  user_core_first_reg_leads_nio_DSLA:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]

  user_core_nio_value:
    dtype: "Bucket"
    bin_boundarie: [0.5, 100.5, 200.5, 300.5, 400.5, 500.5, 600.5, 700.5, 800.5, 900.5]

  user_core_user_curr_credit_amount:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1000.5, 5000.5, 10000.5, 20000.5, 50000.5, 100000.5]

  intention_create_time_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5]

  user_register_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5, 4018.5, 4383.5, 4749.5]

  user_core_visit_nioapp_login_180d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 100.5, 200.5]

  user_core_visit_nioapp_login_90d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 50.5, 100.5]

  user_core_unfirst_reg_leads_nio_180d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5, 3.5, 5.5, 10.5]

  user_core_visit_nioapp_login_30d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]

  user_core_visit_nioapp_login_60d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 50.5]

  user_core_unfirst_reg_leads_nio_90d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5, 3.5, 5.5]

  user_core_visit_nioapp_login_7d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5]

  user_core_unfirst_reg_leads_nio_60d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5, 3.5]

  user_core_unfirst_reg_leads_nio_30d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5]

  user_core_visit_nioapp_login_1d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5]

  user_core_unfirst_reg_leads_nio_7d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5]

  user_core_checkin_nioapp_60d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5]

  user_core_checkin_nioapp_90d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]

  # 分类特征 - 字符串查找
  user_core_pred_has_other_vehicle:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "None"]

  user_core_nio_has_inviter:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "None"]

  fellow_follow_decision_maker:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]

  fellow_follow_intention_nio_confirm:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "LowFreq", "None"]

  fellow_follow_intention_test_drive:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "LowFreq", "None"]

  user_core_user_gender:
    dtype: "StringLookup"
    vocabulary: ["M", "F", "Unknown", "None"]

  user_core_user_age_group:
    dtype: "StringLookup"
    vocabulary: ["18-25", "26-30", "31-35", "36-40", "41-45", "46-50", "51+", "Unknown", "None"]

  user_core_resident_city:
    dtype: "StringLookup"
    vocabulary: ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Chengdu", "Nanjing", "Wuhan", "Xian", "Suzhou", "Other", "None"]

  user_core_pred_career_type:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "None"]

  user_core_nio_user_identity:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "None"]

  intention_stage:
    dtype: "StringLookup"
    vocabulary: ["A", "I", "D", "O", "None"]

  intention_status:
    dtype: "StringLookup"
    vocabulary: ["Active", "Inactive", "Closed", "None"]
