# 优化实验V5: 时间衰减建模优化
# 基于数据特性分析，专门针对时间衰减效应进行建模优化
# 目标: 提升Month_1 PR-AUC > 0.28

_metadata:
  version: "optimization_v5_time_decay"
  creation_date: "2025-06-16"
  base_config: "OPTIMIZATION_V2_LOSS_TUNING"
  hypothesis: "基于首月效应和时间衰减模式，优化月份权重和时间特征处理"
  target_improvement: "Month_1 PR-AUC > 0.28"
  data_insights: |
    - 首月转化率1.29% vs 第6月0.38%，衰减明显
    - 95.7%用户不购买，1.27%用户首月购买
    - 时间类特征占Top10重要性的70%

# 模型基础配置 (保持不变)
model_config:
  network_name: "EPMMOENet"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
  mask_label: null
  use_multitask: false
  
# 训练参数 - 基于时间衰减优化
training_config:
  batch_size: 1024
  loss_type: "focal"
  pos_weight: 30.0                   # 从25.0增加到30.0，更强调稀有正样本
  use_month_weights: true
  focal_alpha: 0.25                  # 从0.3降低到0.25，减少对困难样本的过度关注
  focal_gamma: 3.0                   # 从2.5增加到3.0，增强对困难样本的区分
  
  # 优化的月份权重 - 基于实际转化率设计
  month_weights: [10.0, 4.0, 3.0, 3.0, 2.0, 1.0]  # 更强调首月效应

# 输入模块配置 - 优化时间特征组合
input_modules:
  InputGeneral:
    features:
      # 核心时间特征组 (Top重要性，保留全部)
      - user_create_days                    # 重要性#1: 12.5%
      - intention_intention_fail_days       # 重要性#2: 11.0%
      - app_search_intention_DSLA          # 重要性#3: 9.2%
      - intention_opportunity_create_days   # 重要性#4: 8.5%
      - user_core_unfirst_reg_leads_nio_DSLA # 重要性#5: 6.2%
      - user_core_first_reg_leads_nio_DSLA  # 重要性#6: 6.0%
      - intention_create_time_days          # 重要性#7: 5.4%
      - user_register_days                  # 重要性#8: 5.2%
      
      # 优化的行为频次特征 (去除冗余，保留关键时间窗口)
      - user_core_visit_nioapp_login_180d_cnt  # 重要性#9: 5.0% (长期)
      - user_core_visit_nioapp_login_30d_cnt   # 重要性#13: 2.9% (短期)
      - user_core_unfirst_reg_leads_nio_180d_cnt # 重要性#12: 3.0% (长期)
      - user_core_unfirst_reg_leads_nio_60d_cnt  # 重要性#18: 1.4% (中期)
      - user_core_unfirst_reg_leads_nio_30d_cnt  # 重要性#21: 1.0% (短期)
      
      # 用户价值特征
      - user_core_nio_value                 # 重要性#10: 4.6%
      - user_core_user_curr_credit_amount   # 重要性#15: 2.1%
      
      # 用户属性特征 (保留高区分度特征)
      - user_core_pred_has_other_vehicle    # 重要性#19: 1.0%
      - user_core_nio_has_inviter          # 重要性#23: 0.6%
      
      # 应用使用特征 (保留核心)
      - user_core_checkin_nioapp_60d_cnt    # 重要性#24: 0.5%
      
      # 同伴跟进特征 (业务关键)
      - fellow_follow_decision_maker
      - fellow_follow_intention_nio_confirm
      - fellow_follow_intention_test_drive
      
      # 用户画像特征 (保留核心分类)
      - user_core_user_gender
      - user_core_user_age_group
      - user_core_resident_city
      - user_core_pred_career_type
      - user_core_nio_user_identity
      
      # 意向状态特征 (业务关键)
      - intention_stage
      - intention_status

  InputScene:
    features: []

  InputSeqSet:
    Set: []
    SetInfo: {}

# 标签配置
labels:
  m_purchase_days_nio_new_car: {}
  d_purchase_days_nio_new_car: {}
  pos_flag: {}

# 特征处理配置 - 基于数据分析优化分桶策略
features:
  # 核心时间特征 - 基于业务逻辑优化分桶
  user_create_days:
    dtype: "Bucket"
    # 基于购买行为时间模式优化分桶: 重点区分30天内的新用户
    bin_boundarie: [0.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5]

  intention_intention_fail_days:
    dtype: "Bucket"
    # 意向失败时间: 重点区分7天内的热意向
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]

  app_search_intention_DSLA:
    dtype: "Bucket"
    # 搜索活跃度: 重点区分近期活跃用户
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]
    missing_rate: 0.732
    # 缺失值处理: 73.2%缺失表示无搜索行为，设置特殊编码
    missing_strategy: "business_logic"  # 缺失=无搜索行为

  intention_opportunity_create_days:
    dtype: "Bucket"
    # 商机创建时间: 重点区分新商机
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]

  user_core_unfirst_reg_leads_nio_DSLA:
    dtype: "Bucket"
    # 非首次注册活跃度: 重点区分活跃用户
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]
    missing_strategy: "business_logic"  # 缺失=无此类行为

  user_core_first_reg_leads_nio_DSLA:
    dtype: "Bucket"
    # 首次注册活跃度
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]
    missing_strategy: "business_logic"

  user_core_nio_value:
    dtype: "Bucket"
    # 用户价值评分: 基于分布优化分桶
    bin_boundarie: [0.5, 100.5, 200.5, 300.5, 400.5, 500.5, 600.5, 700.5, 800.5, 900.5]

  user_core_user_curr_credit_amount:
    dtype: "Bucket"
    # 信用额度: 重点区分高价值用户
    bin_boundarie: [0.5, 1000.5, 5000.5, 10000.5, 20000.5, 50000.5, 100000.5]

  intention_create_time_days:
    dtype: "Bucket"
    # 意向创建时间: 重点区分新意向
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5]

  user_register_days:
    dtype: "Bucket"
    # 用户注册时间: 全时间范围分桶
    bin_boundarie: [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5, 4018.5, 4383.5, 4749.5]

  # 优化的行为频次特征 - 保留关键时间窗口
  user_core_visit_nioapp_login_180d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 100.5, 200.5]

  user_core_visit_nioapp_login_30d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]

  user_core_unfirst_reg_leads_nio_180d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5, 3.5, 5.5, 10.5]

  user_core_unfirst_reg_leads_nio_60d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5, 3.5]

  user_core_unfirst_reg_leads_nio_30d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5]

  user_core_checkin_nioapp_60d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5]

  # 分类特征 - 保持原有配置
  user_core_pred_has_other_vehicle:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "None"]

  user_core_nio_has_inviter:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "None"]

  fellow_follow_decision_maker:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]

  fellow_follow_intention_nio_confirm:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "LowFreq", "None"]

  fellow_follow_intention_test_drive:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "LowFreq", "None"]

  user_core_user_gender:
    dtype: "StringLookup"
    vocabulary: ["M", "F", "Unknown", "None"]

  user_core_user_age_group:
    dtype: "StringLookup"
    vocabulary: ["18-25", "26-30", "31-35", "36-40", "41-45", "46-50", "51+", "Unknown", "None"]

  user_core_resident_city:
    dtype: "StringLookup"
    vocabulary: ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Chengdu", "Nanjing", "Wuhan", "Xian", "Suzhou", "Other", "None"]

  user_core_pred_career_type:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "None"]

  user_core_nio_user_identity:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "None"]

  intention_stage:
    dtype: "StringLookup"
    vocabulary: ["A", "I", "D", "O", "None"]

  intention_status:
    dtype: "StringLookup"
    vocabulary: ["Active", "Inactive", "Closed", "None"]
