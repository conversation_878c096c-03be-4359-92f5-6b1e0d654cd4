# 优化实验V7: 时间窗口优化
# 基于数据分析，优化时间窗口特征组合，去除冗余，保留关键
# 目标: 通过精简时间窗口特征提升模型效率和泛化能力

_metadata:
  version: "optimization_v7_time_window"
  creation_date: "2025-06-16"
  base_config: "OPTIMIZATION_V2_LOSS_TUNING"
  hypothesis: "基于特征重要性分析，优化时间窗口组合可以提升性能"
  target_improvement: "评估集 Month_1 PR-AUC > 0.08"
  data_insights: |
    - 时间窗口特征高度相关(60d vs 90d相关性0.93)
    - 长期窗口(180d)重要性更高
    - 短期窗口(7d, 1d)对首月预测更重要

# 模型基础配置 (保持不变)
model_config:
  network_name: "EPMMOENet"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
  mask_label: null
  use_multitask: false
  
# 训练参数 - 使用最优损失函数配置
training_config:
  batch_size: 1024
  loss_type: "focal"
  pos_weight: 25.0
  use_month_weights: true
  focal_alpha: 0.3
  focal_gamma: 2.5

# 输入模块配置 - 优化时间窗口特征组合
input_modules:
  InputGeneral:
    features:
      # 核心时间特征 (8个) - 保持全部，重要性最高
      - user_create_days                    # 重要性#1: 12.5%
      - intention_intention_fail_days       # 重要性#2: 11.0%
      - app_search_intention_DSLA          # 重要性#3: 9.2%
      - intention_opportunity_create_days   # 重要性#4: 8.5%
      - user_core_unfirst_reg_leads_nio_DSLA # 重要性#5: 6.2%
      - user_core_first_reg_leads_nio_DSLA  # 重要性#6: 6.0%
      - intention_create_time_days          # 重要性#7: 5.4%
      - user_register_days                  # 重要性#8: 5.2%
      
      # 优化的访问行为特征 - 保留关键时间窗口
      - user_core_visit_nioapp_login_180d_cnt  # 重要性#9: 5.0% (长期趋势)
      - user_core_visit_nioapp_login_30d_cnt   # 重要性#13: 2.9% (短期活跃)
      - user_core_visit_nioapp_login_7d_cnt    # 重要性#16: 1.8% (近期活跃)
      # 去除: 90d_cnt, 60d_cnt, 1d_cnt (冗余或重要性低)
      
      # 优化的线索特征 - 保留关键时间窗口
      - user_core_unfirst_reg_leads_nio_180d_cnt # 重要性#12: 3.0% (长期)
      - user_core_unfirst_reg_leads_nio_30d_cnt  # 重要性#21: 1.0% (短期)
      # 去除: 90d_cnt, 60d_cnt, 7d_cnt (冗余)
      
      # 用户价值特征 (2个)
      - user_core_nio_value                 # 重要性#10: 4.6%
      - user_core_user_curr_credit_amount   # 重要性#15: 2.1%
      
      # 用户属性特征 (2个)
      - user_core_pred_has_other_vehicle    # 重要性#19: 1.0%
      - user_core_nio_has_inviter          # 重要性#23: 0.6%
      
      # 应用使用特征 - 保留核心
      - user_core_checkin_nioapp_90d_cnt    # 重要性#25: 0.4% (保留长期)
      # 去除: checkin_60d_cnt (冗余)
      
      # 同伴跟进特征 (3个) - 业务关键
      - fellow_follow_decision_maker
      - fellow_follow_intention_nio_confirm
      - fellow_follow_intention_test_drive
      
      # 用户画像特征 (5个) - 保留全部
      - user_core_user_gender
      - user_core_user_age_group
      - user_core_resident_city
      - user_core_pred_career_type
      - user_core_nio_user_identity
      
      # 意向状态特征 (2个) - 业务关键
      - intention_stage
      - intention_status

  InputScene:
    features: []

  InputSeqSet:
    Set: []
    SetInfo: {}

# 标签配置
labels:
  m_purchase_days_nio_new_car: {}
  d_purchase_days_nio_new_car: {}
  pos_flag: {}

# 特征处理配置 - 精简版本
features:
  # 核心时间特征 - 保持原有配置
  user_create_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5]

  intention_intention_fail_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]

  app_search_intention_DSLA:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]
    missing_rate: 0.732

  intention_opportunity_create_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]

  user_core_unfirst_reg_leads_nio_DSLA:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]

  user_core_first_reg_leads_nio_DSLA:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5]

  intention_create_time_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5]

  user_register_days:
    dtype: "Bucket"
    bin_boundarie: [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5, 4018.5, 4383.5, 4749.5]

  # 优化的访问行为特征 - 仅保留关键时间窗口
  user_core_visit_nioapp_login_180d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 100.5, 200.5]

  user_core_visit_nioapp_login_30d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]

  user_core_visit_nioapp_login_7d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5]

  # 优化的线索特征 - 仅保留关键时间窗口
  user_core_unfirst_reg_leads_nio_180d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5, 3.5, 5.5, 10.5]

  user_core_unfirst_reg_leads_nio_30d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 2.5]

  # 用户价值特征
  user_core_nio_value:
    dtype: "Bucket"
    bin_boundarie: [0.5, 100.5, 200.5, 300.5, 400.5, 500.5, 600.5, 700.5, 800.5, 900.5]

  user_core_user_curr_credit_amount:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1000.5, 5000.5, 10000.5, 20000.5, 50000.5, 100000.5]

  # 应用使用特征 - 仅保留长期
  user_core_checkin_nioapp_90d_cnt:
    dtype: "Bucket"
    bin_boundarie: [0.5, 1.5, 3.5, 7.5, 15.5, 30.5]

  # 分类特征 - 保持原有配置
  user_core_pred_has_other_vehicle:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "None"]

  user_core_nio_has_inviter:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "None"]

  fellow_follow_decision_maker:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]

  fellow_follow_intention_nio_confirm:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "LowFreq", "None"]

  fellow_follow_intention_test_drive:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "LowFreq", "None"]

  user_core_user_gender:
    dtype: "StringLookup"
    vocabulary: ["M", "F", "Unknown", "None"]

  user_core_user_age_group:
    dtype: "StringLookup"
    vocabulary: ["18-25", "26-30", "31-35", "36-40", "41-45", "46-50", "51+", "Unknown", "None"]

  user_core_resident_city:
    dtype: "StringLookup"
    vocabulary: ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Chengdu", "Nanjing", "Wuhan", "Xian", "Suzhou", "Other", "None"]

  user_core_pred_career_type:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "None"]

  user_core_nio_user_identity:
    dtype: "StringLookup"
    vocabulary: ["0", "1", "2", "3", "4", "5", "None"]

  intention_stage:
    dtype: "StringLookup"
    vocabulary: ["A", "I", "D", "O", "None"]

  intention_status:
    dtype: "StringLookup"
    vocabulary: ["Active", "Inactive", "Closed", "None"]
