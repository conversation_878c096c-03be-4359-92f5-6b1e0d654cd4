# 优化实验V8: 数据驱动的特征优化
# 基于深度数据分析，实施特征去冗余、缺失值优化、类别特征重编码
# 目标: 通过数据质量提升实现 Month_1 PR-AUC > 0.27

_metadata:
  version: "optimization_v8_data_driven"
  creation_date: "2025-06-16"
  base_config: "GOLDEN_CONFIG"
  hypothesis: "基于数据特点的特征优化可以显著提升模型性能和效率"
  target_improvement: "Month_1 PR-AUC > 0.27, 训练时间减少20%"
  data_insights: |
    核心数据洞察:
    1. 特征冗余严重: 时间窗口特征相关性0.7-0.93
    2. 缺失值问题: app_search_intention_DSLA缺失73%
    3. 首月效应明显: 购买行为高度集中在第1个月(1.29%)
    4. 类别特征稀疏: 居住城市409个值，长尾分布严重
    
    优化策略:
    - 特征去冗余: 移除高相关性时间窗口特征
    - 缺失值优化: 业务逻辑驱动的缺失值处理
    - 类别特征重编码: 长尾分布优化和频率编码
    - 首月专注: 增强第一个月预测权重

# 模型基础配置
model_config:
  network_name: "EPMMOENet"
  train_dates: ["20240430"]
  test_dates: ["20240531"]
  mask_label: null
  use_multitask: false
  
  # 首月专注优化
  default_embedding_dimension: 12    # 适度增加嵌入维度
  default_gru_dimension: 48          # 增强序列建模能力
  expert_num: 10                     # 增加专家数量
  use_cross_layer: true              # 保持特征交叉
  use_time_attention: true           # 保持时间注意力
  time_decay_factor: 0.01            # 更强的时间衰减，专注近期

# 训练参数 - 首月专注优化
training_config:
  batch_size: 1024
  loss_type: "focal"
  pos_weight: 25.0
  use_month_weights: true
  focal_alpha: 0.3
  focal_gamma: 2.5
  
  # 首月权重增强
  month_weights: [5.0, 2.0, 1.5, 1.0, 0.8, 0.5]  # 更强的首月权重

# 特征优化配置
feature_optimization:
  enable_optimization: true
  
  # 特征去冗余配置
  redundancy_removal:
    correlation_threshold: 0.8
    time_window_strategies:
      search_intention_cnt:
        keep: ["30d", "90d"]           # 保留短期和中期
        remove: ["1d", "7d", "14d", "60d", "180d"]
      login_count:
        keep: ["30d", "180d"]          # 保留短期和长期
        remove: ["60d", "90d"]
      leads_count:
        keep: ["60d", "180d"]          # 保留中期和长期
        remove: ["30d", "90d"]
  
  # 缺失值优化配置
  missing_value_optimization:
    business_logic_features:
      app_search_intention_DSLA:
        strategy: "business_logic"
        fill_value: 999
        description: "缺失表示用户无搜索行为"
      user_core_first_reg_leads_nio_DSLA:
        strategy: "median_by_group"
        group_by: "user_core_nio_user_identity"
      user_core_unfirst_reg_leads_nio_DSLA:
        strategy: "median_by_group"
        group_by: "user_core_nio_user_identity"
    
    high_missing_threshold: 0.7       # 高缺失率阈值
    default_numeric_strategy: "median"
    default_categorical_strategy: "mode"
  
  # 类别特征优化配置
  categorical_optimization:
    user_core_resident_city:
      top_k: 20                       # 保留前20个城市
      merge_others: true
      encoding: "frequency_based"
    user_core_pred_career_type:
      merge_none: true
      low_freq_threshold: 0.01        # 低频阈值1%
    user_core_nio_user_identity:
      keep_all: true                  # 重要业务特征，保持原样
  
  # 特征重要性筛选配置
  importance_selection:
    enable: true
    importance_threshold: 0.001       # 重要性阈值
    method: "mutual_info"             # 使用互信息

# 优化后的输入模块配置
input_modules:
  InputGeneral:
    features:
      # 核心时间特征 (8个) - 最高重要性，全部保留
      - user_create_days                    # 重要性#1: 12.5%
      - intention_intention_fail_days       # 重要性#2: 11.0%
      - app_search_intention_DSLA          # 重要性#3: 9.2% (优化缺失值)
      - intention_opportunity_create_days   # 重要性#4: 8.5%
      - user_core_unfirst_reg_leads_nio_DSLA # 重要性#5: 6.2% (优化缺失值)
      - user_core_first_reg_leads_nio_DSLA  # 重要性#6: 6.0% (优化缺失值)
      - intention_create_time_days          # 重要性#7: 5.4%
      - user_register_days                  # 重要性#8: 5.2%
      
      # 优化后的访问行为特征 - 去冗余后保留关键窗口
      - user_core_visit_nioapp_login_180d_cnt  # 长期趋势
      - user_core_visit_nioapp_login_30d_cnt   # 短期活跃
      
      # 优化后的搜索行为特征 - 去冗余后保留关键窗口
      - app_search_intention_cnt_30d_cnt    # 短期搜索
      - app_search_intention_cnt_90d_cnt    # 中期搜索
      
      # 优化后的线索特征 - 去冗余后保留关键窗口
      - user_core_unfirst_reg_leads_nio_60d_cnt  # 中期线索
      - user_core_unfirst_reg_leads_nio_180d_cnt # 长期线索
      
      # 核心用户价值特征
      - user_core_nio_value                 # 重要性#10: 4.6%
      - user_core_user_curr_credit_amount   # 信用额度
      - user_core_pred_has_other_vehicle    # 车辆拥有情况
      
      # 优化后的类别特征 (重编码后)
      - user_core_user_gender               # 性别 (保持原样)
      - user_core_user_age_group            # 年龄段 (保持原样)
      - user_core_resident_city             # 居住城市 (Top20+Others)
      - user_core_pred_career_type          # 职业类型 (合并低频)
      - user_core_nio_user_identity         # 用户身份 (重要业务特征)
      
      # 其他重要特征
      - user_core_is_nio_employee           # 是否员工
      - user_core_user_curr_points          # 当前积分

  InputScene:
    features:
      - intention_stage                     # 意向阶段
      - intention_status                    # 意向状态
      - fellow_follow_decision_maker        # 决策制定者
      - fellow_follow_nio_confirm           # NIO确认
      - fellow_follow_test_drive_intention  # 试驾意向
      - fellow_follow_test_drive_complete   # 试驾完成

  InputSeqSet:
    Set: ["UserCoreSequence", "UserCarSequence"]
    SetInfo:
      UserCoreSequence:
        features:
          - user_core_action_code_seq       # 用户行为序列
          - user_core_action_day_seq        # 用户行为时间序列
        gru_dimension: 48                   # 增强序列建模
        max_sequence_length: 50
      UserCarSequence:
        features:
          - user_car_core_action_code_seq   # 车辆行为序列
          - user_car_core_action_veh_model_seq # 车辆型号序列
          - user_car_core_action_day_seq    # 车辆行为时间序列
        gru_dimension: 32
        max_sequence_length: 50

# 数据预处理配置
preprocessing:
  # 特征缩放
  feature_scaling:
    method: "robust"                        # 使用鲁棒缩放，对异常值不敏感
    features: "numeric"                     # 仅对数值特征缩放
  
  # 序列特征处理
  sequence_processing:
    max_length: 50                          # 统一序列长度
    padding_value: "0"                      # 填充值
    truncation: "post"                      # 后截断
  
  # 验证配置
  validation:
    check_feature_leakage: true             # 检查特征泄露
    check_distribution_shift: true          # 检查分布漂移
    min_samples_per_class: 100              # 每类最小样本数

# 实验跟踪配置
experiment_tracking:
  track_feature_importance: true            # 跟踪特征重要性
  track_correlation_matrix: true           # 跟踪相关性矩阵
  track_missing_value_stats: true          # 跟踪缺失值统计
  track_categorical_distributions: true    # 跟踪类别分布
  
  # 对比基线
  baseline_config: "GOLDEN_CONFIG"
  comparison_metrics:
    - "Month_1_PR_AUC"
    - "Month_1_Recall_840"
    - "training_time"
    - "memory_usage"
    - "feature_count"

# 预期效果
expected_improvements:
  performance:
    Month_1_PR_AUC: ">0.27"               # 从0.2545提升到>0.27
    Month_1_Recall_840: ">0.55"           # 保持或提升召回率
  efficiency:
    training_time_reduction: "20%"         # 训练时间减少20%
    memory_usage_reduction: "25%"          # 内存使用减少25%
    feature_count_reduction: "30%"         # 特征数量减少30%
  quality:
    reduced_overfitting: true              # 减少过拟合
    improved_generalization: true          # 提升泛化能力
    enhanced_interpretability: true        # 增强可解释性
