"""
统一配置管理器 - 整合config_schema.py和config_manager_enhanced.py

设计目标：
1. 提供结构化的配置数据模型（来自config_schema.py）
2. 实现分层配置管理功能（来自config_manager_enhanced.py）
3. 统一YAML格式，废弃JSON配置
4. 支持配置验证、继承和版本管理
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field, asdict
import logging
from datetime import datetime

# =============================================
# 配置数据模型 (来自config_schema.py)
# =============================================

@dataclass
class FeatureConfig:
    """特征配置"""
    type: str  # "StringLookup", "Bucket", "Dense", "Embedding", "VarLen"
    dimension: Optional[int] = None
    vocabulary_size: Optional[int] = None
    boundaries: Optional[List[float]] = None
    max_length: Optional[int] = None
    description: str = ""
    
    def validate(self):
        """验证配置合理性"""
        if self.type == "StringLookup" and not self.vocabulary_size:
            raise ValueError(f"StringLookup feature requires vocabulary_size")
        if self.type == "Bucket" and not self.boundaries:
            raise ValueError(f"Bucket feature requires boundaries")

@dataclass 
class InputModuleConfig:
    """输入模块配置"""
    features: List[str]
    description: str = ""

@dataclass
class SequenceSetConfig:
    """序列集合配置"""
    features: List[str]
    max_length: int = 50
    gru_dimension: int = 32

@dataclass
class ModelArchConfig:
    """模型架构配置"""
    network_name: str
    output_dimension: int = 6
    output_activation: str = "sigmoid"
    default_embedding_dimension: int = 8
    default_gru_dimension: int = 32
    expert_num: int = 8
    use_cross_layer: bool = True
    use_multitask: bool = False
    use_time_attention: bool = True
    time_decay_factor: float = 0.05

@dataclass
class DataConfig:
    """数据配置"""
    train_dates: List[str]
    test_dates: List[str]
    mask_label: Optional[str] = None
    data_format: str = "parquet"
    partition_column: str = "datetime"
    data_root: str = "data"
    
    # 预处理配置
    missing_value_strategy: str = "fill_default"
    categorical_missing_token: str = "<UNK>"
    numerical_missing_value: float = 0.0
    remove_duplicates: bool = True
    outlier_detection: bool = True
    outlier_threshold: float = 3.0
    min_category_frequency: int = 10
    max_categories_per_feature: int = 10000
    numerical_scaling: str = "none"
    sequence_padding: str = "post"
    sequence_truncating: str = "post"
    default_sequence_length: int = 50
    validation_split: float = 0.2
    random_seed: int = 42

@dataclass
class ExperimentConfig:
    """实验配置"""
    experiment_name: str
    description: str = ""
    model_config: ModelArchConfig = field(default_factory=ModelArchConfig)
    data_config: DataConfig = field(default_factory=DataConfig)
    features: Dict[str, FeatureConfig] = field(default_factory=dict)
    input_modules: Dict[str, InputModuleConfig] = field(default_factory=dict)
    sequence_sets: Dict[str, SequenceSetConfig] = field(default_factory=dict)
    
    def validate(self):
        """验证整个配置的一致性"""
        # 验证特征配置
        for name, feature_config in self.features.items():
            feature_config.validate()
        
        # 验证特征引用一致性
        all_referenced_features = set()
        for module_config in self.input_modules.values():
            all_referenced_features.update(module_config.features)
        
        for seq_config in self.sequence_sets.values():
            all_referenced_features.update(seq_config.features)
        
        undefined_features = all_referenced_features - set(self.features.keys())
        if undefined_features:
            raise ValueError(f"Undefined features referenced: {undefined_features}")

# =============================================
# 特征组管理 (来自config_manager_enhanced.py)
# =============================================

@dataclass
class FeatureGroup:
    """特征组定义"""
    name: str
    description: str
    features: List[str]
    category: str  # "behavioral", "demographic", "contextual", "sequential"

class FeatureRegistry:
    """特征注册表 - 管理特征定义"""
    
    def __init__(self, registry_path: Optional[str] = None):
        self.registry_path = registry_path or "src/configs/features"
        self.logger = logging.getLogger(__name__)
        self.features = {}
        self.feature_groups = {}
        
        self._load_feature_registry()
    
    def _load_feature_registry(self):
        """加载特征注册表"""
        registry_dir = Path(self.registry_path)
        
        if not registry_dir.exists():
            self.logger.warning(f"Feature registry directory not found: {registry_dir}")
            return
        
        # 加载特征组定义
        groups_file = registry_dir / "feature_groups.yaml"
        if groups_file.exists():
            with open(groups_file, 'r', encoding='utf-8') as f:
                groups_data = yaml.safe_load(f)
                for group_name, group_info in groups_data.items():
                    self.feature_groups[group_name] = FeatureGroup(
                        name=group_name,
                        description=group_info.get('description', ''),
                        features=group_info.get('features', []),
                        category=group_info.get('category', 'unknown')
                    )
        
        self.logger.info(f"Loaded {len(self.feature_groups)} feature groups")
    
    def get_feature_group(self, group_name: str) -> Optional[FeatureGroup]:
        """获取特征组"""
        return self.feature_groups.get(group_name)
    
    def list_features_by_category(self, category: str) -> List[str]:
        """按类别列出特征"""
        features = []
        for group in self.feature_groups.values():
            if group.category == category:
                features.extend(group.features)
        return features

# =============================================
# 统一配置管理器
# =============================================

class ConfigurationError(Exception):
    """配置错误异常"""
    pass

class UnifiedConfigManager:
    """
    统一配置管理器 - 整合所有配置管理功能
    
    配置层次结构：
    1. 基础配置层：标准化的特征类型和处理方法
    2. 业务配置层：领域知识和业务规则  
    3. 实验配置层：具体实验的配置组合
    """
    
    def __init__(self, config_root: str = "src/configs"):
        self.config_root = Path(config_root)
        self.logger = logging.getLogger(__name__)
        
        # 统一的目录结构
        self.foundation_dir = self.config_root / "foundation"
        self.business_dir = self.config_root / "business"
        self.experiments_dir = self.config_root / "experiments"
        self.features_dir = self.config_root / "features"
        
        # 创建目录结构
        self._ensure_directories()
        
        # 初始化特征注册表
        self.feature_registry = FeatureRegistry(str(self.features_dir))
    
    def _ensure_directories(self):
        """确保配置目录存在"""
        for directory in [self.foundation_dir, self.business_dir, 
                         self.experiments_dir, self.features_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def load_experiment_config(self, experiment_name: str) -> ExperimentConfig:
        """加载实验配置"""
        exp_file = self.experiments_dir / f"{experiment_name}.yaml"
        
        if not exp_file.exists():
            raise ConfigurationError(f"Experiment config not found: {exp_file}")
        
        with open(exp_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return self._reconstruct_experiment_config(config_data)
    
    def save_experiment_config(self, config: ExperimentConfig, 
                             experiment_name: Optional[str] = None) -> Path:
        """保存实验配置"""
        exp_name = experiment_name or config.experiment_name
        exp_file = self.experiments_dir / f"{exp_name}.yaml"
        
        # 转换为字典并保存
        config_dict = asdict(config)
        
        with open(exp_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Experiment config saved: {exp_file}")
        return exp_file
    
    def _reconstruct_experiment_config(self, config_data: Dict[str, Any]) -> ExperimentConfig:
        """从字典重建实验配置对象"""
        # 重建嵌套对象
        model_config = ModelArchConfig(**config_data["model_config"])
        data_config = DataConfig(**config_data["data_config"])
        
        # 重建特征配置
        features = {}
        for name, feature_data in config_data["features"].items():
            features[name] = FeatureConfig(**feature_data)
        
        # 重建输入模块配置
        input_modules = {}
        for name, module_data in config_data.get("input_modules", {}).items():
            input_modules[name] = InputModuleConfig(**module_data)
        
        # 重建序列配置
        sequence_sets = {}
        for name, seq_data in config_data.get("sequence_sets", {}).items():
            sequence_sets[name] = SequenceSetConfig(**seq_data)
        
        return ExperimentConfig(
            experiment_name=config_data["experiment_name"],
            description=config_data.get("description", ""),
            model_config=model_config,
            data_config=data_config,
            features=features,
            input_modules=input_modules,
            sequence_sets=sequence_sets
        )
    
    def convert_legacy_json_config(self, legacy_config_path: str, 
                                 new_experiment_name: str) -> ExperimentConfig:
        """
        转换旧版JSON配置到新版YAML配置
        """
        self.logger.info(f"Converting legacy config: {legacy_config_path}")
        
        # 加载旧版配置
        with open(legacy_config_path, 'r', encoding='utf-8') as f:
            legacy_config = json.load(f)
        
        # 转换为新配置格式
        experiment_config = self._convert_from_legacy(legacy_config, new_experiment_name)
        
        # 保存新配置
        self.save_experiment_config(experiment_config, new_experiment_name)
        
        self.logger.info(f"Legacy config converted successfully: {new_experiment_name}")
        return experiment_config
    
    def _convert_from_legacy(self, legacy_config: dict, experiment_name: str) -> ExperimentConfig:
        """从旧版配置转换逻辑"""
        # 提取模型架构参数
        model_config = ModelArchConfig(
            network_name=legacy_config.get("network_name", "EPMMOENet"),
            output_dimension=legacy_config.get("output_dimension", 6),
            output_activation=legacy_config.get("output_activation", "sigmoid"),
            use_cross_layer=legacy_config.get("use_cross_layer", True),
            use_time_attention=legacy_config.get("use_time_attention", True)
        )
        
        # 提取数据配置
        data_config = DataConfig(
            train_dates=legacy_config.get("train_dates", ["20240430"]),
            test_dates=legacy_config.get("test_dates", ["20240531"]),
            mask_label=legacy_config.get("mask_label")
        )
        
        # 转换特征配置 - 处理不同的字段名格式
        features = {}
        
        # 处理RawFeature格式（旧版本）
        raw_features = legacy_config.get("RawFeature", {})
        for feature_name, feature_info in raw_features.items():
            features[feature_name] = self._normalize_feature_config(feature_info)
        
        # 处理feature_padding_dict格式（新版本）
        feature_padding = legacy_config.get("feature_padding_dict", {})
        for feature_name, feature_info in feature_padding.items():
            features[feature_name] = self._normalize_feature_config(feature_info)
        
        # 智能推断输入模块配置（如果原配置没有明确定义）
        input_modules = {}
        sequence_sets = {}
        
        # 首先尝试从原配置加载
        for module_name in ["InputGeneral", "InputScene"]:
            if module_name in legacy_config:
                input_modules[module_name] = InputModuleConfig(
                    features=legacy_config[module_name].get("features", [])
                )
        
        if "InputSeqSet" in legacy_config:
            seq_info = legacy_config["InputSeqSet"].get("SetInfo", {})
            for set_name, set_config in seq_info.items():
                sequence_sets[set_name] = SequenceSetConfig(
                    features=set_config.get("features", []),
                    max_length=set_config.get("max_length", 50)
                )
        
        # 如果没有明确的模块配置，则智能推断
        if not input_modules and not sequence_sets:
            self.logger.info("No explicit input modules found, inferring from features...")
            input_modules, sequence_sets = self._infer_input_modules_from_features(features)
        
        return ExperimentConfig(
            experiment_name=experiment_name,
            model_config=model_config,
            data_config=data_config,
            features=features,
            input_modules=input_modules,
            sequence_sets=sequence_sets
        )
    
    def _normalize_feature_config(self, feature_info: dict) -> FeatureConfig:
        """标准化特征配置，处理不同的字段名"""
        normalized_info = feature_info.copy()
        
        # 处理字段名不一致问题
        field_mappings = {
            'bin_boundarie': 'boundaries',  # 处理拼写错误
            'dtype': 'type',               # 统一类型字段名
        }
        
        for old_field, new_field in field_mappings.items():
            if old_field in normalized_info:
                normalized_info[new_field] = normalized_info.pop(old_field)
        
        # 确保必要字段存在
        if 'type' not in normalized_info:
            normalized_info['type'] = 'Dense'  # 默认类型
        
        # 过滤掉FeatureConfig不认识的字段
        valid_fields = {'type', 'dimension', 'vocabulary_size', 'boundaries', 'max_length', 'description'}
        filtered_info = {k: v for k, v in normalized_info.items() if k in valid_fields}
        
        return FeatureConfig(**filtered_info)
    
    def _infer_input_modules_from_features(self, features: Dict[str, FeatureConfig]):
        """智能推断输入模块配置"""
        self.logger.info(f"Inferring input modules from {len(features)} features...")
        
        general_features = []
        scene_features = []
        sequence_features = []
        
        # 基于特征名模式推断分组
        for feature_name, feature_config in features.items():
            feature_name_lower = feature_name.lower()
            
            # 序列特征模式
            if any(pattern in feature_name_lower for pattern in [
                '_seq', '_sequence', 'action_code', 'action_day', 'action_veh_model'
            ]):
                sequence_features.append(feature_name)
            
            # 场景特征模式
            elif any(pattern in feature_name_lower for pattern in [
                'intention_', 'fellow_follow_', 'product_type', 'stage', 'status', 
                'identity', 'user_gender', 'age_group', 'marriage_status', 'career_type',
                'is_nio_employee', 'nio_user_identity', 'nio_community_identity'
            ]):
                scene_features.append(feature_name)
            
            # 其他都归类为一般特征
            else:
                general_features.append(feature_name)
        
        # 构建输入模块
        input_modules = {}
        if general_features:
            input_modules["InputGeneral"] = InputModuleConfig(
                features=general_features,
                description="自动推断的一般特征模块"
            )
        
        if scene_features:
            input_modules["InputScene"] = InputModuleConfig(
                features=scene_features,
                description="自动推断的场景特征模块"
            )
        
        # 构建序列集合（按类型分组）
        sequence_sets = {}
        if sequence_features:
            # 按序列类型分组
            user_core_seq = [f for f in sequence_features if 'user_core_action' in f]
            user_car_seq = [f for f in sequence_features if 'user_car_core' in f]
            other_seq = [f for f in sequence_features if f not in user_core_seq + user_car_seq]
            
            if user_core_seq:
                sequence_sets["UserCoreSequence"] = SequenceSetConfig(
                    features=user_core_seq,
                    max_length=50
                )
            
            if user_car_seq:
                sequence_sets["UserCarSequence"] = SequenceSetConfig(
                    features=user_car_seq,
                    max_length=50
                )
            
            if other_seq:
                sequence_sets["OtherSequence"] = SequenceSetConfig(
                    features=other_seq,
                    max_length=50
                )
        
        self.logger.info(f"Inferred modules:")
        self.logger.info(f"  General features: {len(general_features)}")
        self.logger.info(f"  Scene features: {len(scene_features)}")
        self.logger.info(f"  Sequence features: {len(sequence_features)}")
        
        return input_modules, sequence_sets
    
    def cleanup_legacy_files(self):
        """清理旧版配置文件"""
        cleanup_patterns = [
            "*nio_v7_converted*",
            "*chunk*",
            "*.json"  # 如果确认不再需要JSON配置
        ]
        
        cleaned_files = []
        for pattern in cleanup_patterns:
            for file_path in self.config_root.rglob(pattern):
                if file_path.is_file():
                    cleaned_files.append(file_path)
                    # 备份重要文件而不是直接删除
                    backup_dir = self.config_root / "backup_legacy"
                    backup_dir.mkdir(exist_ok=True)
                    backup_path = backup_dir / file_path.name
                    file_path.rename(backup_path)
        
        self.logger.info(f"Cleaned up {len(cleaned_files)} legacy files")
        return cleaned_files

# 全局配置管理器实例
unified_config_manager = UnifiedConfigManager()

# 便捷函数
def load_experiment_config(experiment_name: str) -> ExperimentConfig:
    """便捷的实验配置加载函数"""
    return unified_config_manager.load_experiment_config(experiment_name)

def convert_legacy_config(legacy_path: str, new_name: str) -> ExperimentConfig:
    """便捷的配置转换函数"""
    return unified_config_manager.convert_legacy_json_config(legacy_path, new_name)