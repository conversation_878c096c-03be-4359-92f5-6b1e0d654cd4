"""
Enhanced Data Preprocessor - 集成特征优化的数据预处理器

主要功能：
1. 集成FeatureOptimizer进行智能特征优化
2. 业务逻辑驱动的数据预处理
3. 自动化的数据质量检查和修复
4. 支持配置驱动的预处理流程
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from src.data.preprocessor import DataPreprocessor
from src.features.optimizers import FeatureOptimizer


class EnhancedDataPreprocessor(DataPreprocessor):
    """
    增强版数据预处理器
    
    在原有DataPreprocessor基础上增加：
    - 特征优化集成
    - 数据质量检查
    - 业务逻辑验证
    - 自动化预处理流程
    """
    
    def __init__(self, 
                 feature_padding_dict=None, 
                 train_dates=None,
                 optimization_config=None):
        """
        初始化增强版预处理器
        
        Args:
            feature_padding_dict: 特征填充配置
            train_dates: 训练日期列表
            optimization_config: 特征优化配置
        """
        super().__init__(feature_padding_dict, train_dates)
        
        self.optimization_config = optimization_config or {}
        self.feature_optimizer = None
        
        # 初始化特征优化器
        if self.optimization_config.get('enable_optimization', False):
            self.feature_optimizer = FeatureOptimizer(
                self.optimization_config.get('optimizer_config', {})
            )
        
        self.logger = logging.getLogger(__name__)
        
        # 预处理统计信息
        self.preprocessing_stats = {
            'original_shape': None,
            'final_shape': None,
            'optimization_applied': False,
            'quality_checks': {},
            'processing_time': 0
        }
    
    def enhanced_preprocess(self, 
                           df: pd.DataFrame,
                           target_column: str = None,
                           feature_config: Dict[str, Any] = None,
                           apply_optimization: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        增强版预处理流程
        
        Args:
            df: 输入数据框
            target_column: 目标列名
            feature_config: 特征配置
            apply_optimization: 是否应用特征优化
            
        Returns:
            处理后的数据框和处理报告
        """
        import time
        start_time = time.time()
        
        self.logger.info("开始增强版数据预处理")
        self.preprocessing_stats['original_shape'] = df.shape
        
        processing_report = {
            'steps': [],
            'warnings': [],
            'errors': []
        }
        
        try:
            # 1. 数据质量检查
            df_checked, quality_report = self._perform_quality_checks(df)
            processing_report['steps'].append(('数据质量检查', quality_report))
            self.preprocessing_stats['quality_checks'] = quality_report
            
            # 2. 基础预处理
            df_basic = self.preprocess_features(df_checked, self.feature_padding_dict)
            processing_report['steps'].append(('基础预处理', {'completed': True}))
            
            # 3. 特征优化（如果启用）
            if apply_optimization and self.feature_optimizer and target_column:
                self.logger.info("应用特征优化")
                df_optimized, optimization_report = self.feature_optimizer.optimize_features(
                    df_basic, target_column, feature_config or {}
                )
                processing_report['steps'].append(('特征优化', optimization_report))
                self.preprocessing_stats['optimization_applied'] = True
                df_final = df_optimized
            else:
                df_final = df_basic
                processing_report['steps'].append(('特征优化', {'skipped': True}))
            
            # 4. 模型特征预处理
            if feature_config:
                df_final = self.preprocess_model_features(df_final, feature_config)
                processing_report['steps'].append(('模型特征预处理', {'completed': True}))
            
            # 5. 最终验证
            final_validation = self._final_validation(df_final)
            processing_report['steps'].append(('最终验证', final_validation))
            
            # 更新统计信息
            self.preprocessing_stats['final_shape'] = df_final.shape
            self.preprocessing_stats['processing_time'] = time.time() - start_time
            
            self.logger.info(f"增强版预处理完成: {df.shape} → {df_final.shape}, "
                           f"耗时{self.preprocessing_stats['processing_time']:.2f}秒")
            
            return df_final, processing_report
            
        except Exception as e:
            self.logger.error(f"增强版预处理失败: {e}")
            processing_report['errors'].append(str(e))
            raise
    
    def _perform_quality_checks(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        执行数据质量检查
        """
        self.logger.info("执行数据质量检查")
        
        quality_report = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'missing_value_analysis': {},
            'data_type_analysis': {},
            'outlier_analysis': {},
            'duplicate_analysis': {},
            'issues_found': []
        }
        
        df_cleaned = df.copy()
        
        # 1. 缺失值分析
        missing_analysis = {}
        for column in df.columns:
            try:
                column_str = str(column)  # 确保列名是字符串
                missing_count = df[column].isnull().sum()
                missing_rate = missing_count / len(df)
                missing_analysis[column_str] = {
                    'count': int(missing_count),
                    'rate': float(missing_rate)
                }

                if missing_rate > 0.5:  # 超过50%缺失
                    quality_report['issues_found'].append(f"高缺失率: {column_str} ({missing_rate:.1%})")
            except Exception as e:
                self.logger.warning(f"处理列{column}时出错: {e}")
                continue
        
        quality_report['missing_value_analysis'] = missing_analysis
        
        # 2. 数据类型分析
        dtype_analysis = {}
        for column in df.columns:
            try:
                column_str = str(column)
                dtype_analysis[column_str] = str(df[column].dtype)
            except Exception as e:
                self.logger.warning(f"分析列{column}数据类型时出错: {e}")
                continue
        quality_report['data_type_analysis'] = dtype_analysis
        
        # 3. 重复行检查
        duplicate_count = df.duplicated().sum()
        quality_report['duplicate_analysis'] = {
            'count': int(duplicate_count),
            'rate': float(duplicate_count / len(df))
        }
        
        if duplicate_count > 0:
            quality_report['issues_found'].append(f"发现{duplicate_count}行重复数据")
            # 移除重复行
            df_cleaned = df_cleaned.drop_duplicates()
        
        # 4. 数值特征异常值检查
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        outlier_analysis = {}
        
        for column in numeric_columns:
            try:
                column_str = str(column)
                if df[column].notna().sum() > 0:  # 确保有非空值
                    Q1 = df[column].quantile(0.25)
                    Q3 = df[column].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR

                    outliers = ((df[column] < lower_bound) | (df[column] > upper_bound)).sum()
                    outlier_rate = outliers / df[column].notna().sum()

                    outlier_analysis[column_str] = {
                        'count': int(outliers),
                        'rate': float(outlier_rate),
                        'bounds': [float(lower_bound), float(upper_bound)]
                    }

                    if outlier_rate > 0.1:  # 超过10%异常值
                        quality_report['issues_found'].append(f"高异常值率: {column_str} ({outlier_rate:.1%})")
            except Exception as e:
                self.logger.warning(f"分析列{column}异常值时出错: {e}")
                continue
        
        quality_report['outlier_analysis'] = outlier_analysis
        
        self.logger.info(f"数据质量检查完成: 发现{len(quality_report['issues_found'])}个问题")
        
        return df_cleaned, quality_report
    
    def _final_validation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        最终数据验证
        """
        validation_report = {
            'final_shape': df.shape,
            'final_columns': len(df.columns),
            'final_missing_rate': float(df.isnull().sum().sum() / (df.shape[0] * df.shape[1])),
            'data_types': df.dtypes.value_counts().to_dict(),
            'validation_passed': True,
            'issues': []
        }
        
        # 检查是否有完全空的列
        empty_columns = df.columns[df.isnull().all()].tolist()
        if empty_columns:
            validation_report['issues'].append(f"发现完全空的列: {empty_columns}")
            validation_report['validation_passed'] = False
        
        # 检查是否有完全空的行
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            validation_report['issues'].append(f"发现{empty_rows}行完全空的数据")
        
        # 检查数据框是否为空
        if df.empty:
            validation_report['issues'].append("数据框为空")
            validation_report['validation_passed'] = False
        
        return validation_report
    
    def apply_business_logic_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用业务逻辑预处理
        
        基于NIO业务特点的专门预处理逻辑
        """
        self.logger.info("应用业务逻辑预处理")
        
        df_business = df.copy()
        
        # 1. 用户身份标准化
        if 'user_core_nio_user_identity' in df_business.columns:
            # 标准化用户身份值
            identity_mapping = {
                '粉丝': 'Fan',
                '车主': 'Owner', 
                '准车主': 'ProspectiveOwner',
                'None': 'Unknown',
                '': 'Unknown'
            }
            df_business['user_core_nio_user_identity'] = df_business['user_core_nio_user_identity'].map(
                identity_mapping
            ).fillna('Unknown')
        
        # 2. 时间特征业务逻辑处理
        time_features = [col for col in df_business.columns if 'days' in col.lower() or 'dsla' in col.lower()]
        for feature in time_features:
            if feature in df_business.columns:
                # 负值处理：时间距离不应该为负
                df_business[feature] = df_business[feature].clip(lower=0)
                
                # 异常大值处理：超过3年的时间距离可能是异常值
                df_business[feature] = df_business[feature].clip(upper=1095)  # 3年
        
        # 3. 搜索行为特征业务逻辑
        search_features = [col for col in df_business.columns if 'search' in col.lower()]
        for feature in search_features:
            if feature in df_business.columns and 'cnt' in feature:
                # 搜索次数不应该为负
                df_business[feature] = df_business[feature].clip(lower=0)
                
                # 异常高搜索次数处理（每天超过100次搜索可能是异常）
                if '1d' in feature:
                    df_business[feature] = df_business[feature].clip(upper=100)
                elif '7d' in feature:
                    df_business[feature] = df_business[feature].clip(upper=700)
                elif '30d' in feature:
                    df_business[feature] = df_business[feature].clip(upper=3000)
        
        # 4. 用户价值特征处理
        if 'user_core_nio_value' in df_business.columns:
            # NIO价值评分应该在合理范围内
            df_business['user_core_nio_value'] = df_business['user_core_nio_value'].clip(lower=0, upper=1000)
        
        # 5. 信用额度特征处理
        if 'user_core_user_curr_credit_amount' in df_business.columns:
            # 信用额度不应该为负
            df_business['user_core_user_curr_credit_amount'] = df_business['user_core_user_curr_credit_amount'].clip(lower=0)
        
        self.logger.info("业务逻辑预处理完成")
        return df_business
    
    def get_preprocessing_summary(self) -> Dict[str, Any]:
        """
        获取预处理摘要信息
        """
        return {
            'preprocessing_stats': self.preprocessing_stats,
            'optimization_enabled': self.feature_optimizer is not None,
            'optimization_config': self.optimization_config
        }
