"""
Data loading module for conversion rate prediction models.
"""
import pandas as pd
import logging
import json
import os
from pathlib import Path


class DataLoader:
    """Data loader class that handles loading data from storage."""
    
    def __init__(self, dataset_code, dataset_config_path=None):
        """
        Initialize DataLoader with dataset information.
        
        Args:
            dataset_code (str): Dataset code/location.
            dataset_config_path (str, optional): Path to dataset configuration file.
        """
        self.dataset_code = dataset_code
        self.dataset_config_path = dataset_config_path or f"{dataset_code}.json"
        self.logger = logging.getLogger(__name__)
        self._load_config()
        
    def _load_config(self):
        """Load dataset configuration."""
        try:
            if not Path(self.dataset_config_path).exists():
                self.logger.error(f"Dataset configuration file doesn't exist: {self.dataset_config_path}")
                raise FileNotFoundError(f"Dataset configuration file doesn't exist: {self.dataset_config_path}")
                
            self.logger.info(f"Loading dataset configuration from: {self.dataset_config_path}")
            self.feature_padding_dict = json.load(open(self.dataset_config_path, 'r'))
        except Exception as e:
            self.logger.error(f"Failed to load dataset configuration: {e}")
            raise
    
    def load_dataset(self, dates, columns=None, extra_datasets=None):
        """
        Load dataset for specified dates.
        
        Args:
            dates (list): List of date partitions to load.
            columns (list, optional): Columns to load.
            extra_datasets (list, optional): Additional datasets to load.
            
        Returns:
            pd.DataFrame: Combined dataframe from all date partitions.
        """
        self.logger.info(f"Loading dataset from {self.dataset_code} for dates: {dates}")
        
        list_df = []
        for index, date in enumerate(dates):
            # Load main dataset partition
            file_path = os.path.join(self.dataset_code, f"datetime={date}")
            self.logger.info(f"Loading partition {index+1}/{len(dates)}: {file_path}")
            
            try:
                df_i = pd.read_parquet(file_path, columns=columns, engine='pyarrow').reset_index(drop=True)
            except Exception as e:
                self.logger.error(f"Error loading partition {file_path}: {e}")
                raise
            
            # Load extra datasets if specified
            if extra_datasets:
                df_i = self._load_extra_datasets(df_i, extra_datasets, date)
            
            list_df.append(df_i)
            self.logger.info(f"Loaded partition with {len(df_i)} records")
            
        df_combined = pd.concat(list_df)
        self.logger.info(f"Combined dataset has {len(df_combined)} records")
        return df_combined
    
    def _load_extra_datasets(self, df_base, extra_datasets, date):
        """
        Load and merge extra datasets.
        
        Args:
            df_base (pd.DataFrame): Base dataframe to merge with.
            extra_datasets (list): List of extra dataset definitions.
            date (str): Current date partition.
            
        Returns:
            pd.DataFrame: Merged dataframe with extra data.
        """
        for extra_dataset in extra_datasets:
            extra_path = extra_dataset.get("train_file_folder")
            extra_features = list(extra_dataset.get("RawFeature", {}).keys())
            
            if not extra_path or not extra_features:
                continue
            
            # 构建完整路径
            extra_file_path = os.path.join(extra_path, f"datetime={date}")    
            self.logger.info(f"Loading extra dataset from {extra_file_path}")
            
            try:
                df_extra = pd.read_parquet(extra_file_path, 
                                         columns=["user_id"] + extra_features).reset_index(drop=True)
            except Exception as e:
                self.logger.error(f"Error loading extra dataset {extra_file_path}: {e}")
                self.logger.warning(f"Skipping extra dataset {extra_path}")
                continue
            
            # Ensure user_id is string type for consistent merging
            df_extra["user_id"] = df_extra["user_id"].apply(lambda x: str(int(x)))
            
            # Merge with base dataframe
            df_base = pd.merge(df_base, df_extra, on="user_id", how="left")
            
            # Fill missing values with 0.0
            for feature in extra_features:
                df_base[feature] = df_base[feature].fillna(0.0)
                
            self.logger.info(f"Successfully merged extra dataset")
            
        return df_base
    
    def load_evaluation_data(self, evaluate_file, columns=None, extra_datasets=None):
        """
        Load evaluation dataset.
        
        Args:
            evaluate_file (str): Name of evaluation file.
            columns (list, optional): Columns to load.
            extra_datasets (list, optional): Additional datasets to load.
            
        Returns:
            pd.DataFrame: Evaluation dataframe.
        """
        # 构建完整的评估文件路径
        eval_file_path = os.path.join(self.dataset_code, evaluate_file)
        self.logger.info(f"Loading evaluation dataset from {eval_file_path}")
        
        try:
            df_evaluate = pd.read_parquet(eval_file_path, columns=columns)
        except Exception as e:
            self.logger.error(f"Error loading evaluation dataset {eval_file_path}: {e}")
            raise
            
        self.logger.info(f"Loaded evaluation dataset with {len(df_evaluate)} records")
        
        # Load extra datasets if specified
        if extra_datasets:
            for extra_dataset in extra_datasets:
                extra_path = extra_dataset.get("train_file_folder")
                extra_features = list(extra_dataset.get("RawFeature", {}).keys())
                
                if not extra_path or not extra_features:
                    continue
                
                # 构建完整路径 - 评估数据集使用固定日期
                extra_file_path = os.path.join(extra_path, "datetime=20240531")
                self.logger.info(f"Loading extra evaluation dataset from {extra_file_path}")
                
                try:
                    df_extra = pd.read_parquet(extra_file_path, 
                                            columns=["user_id"] + extra_features).reset_index(drop=True)
                except Exception as e:
                    self.logger.error(f"Error loading extra evaluation dataset {extra_file_path}: {e}")
                    self.logger.warning(f"Skipping extra evaluation dataset {extra_path}")
                    continue
                
                # Ensure user_id is string type for consistent merging
                df_extra["user_id"] = df_extra["user_id"].apply(lambda x: str(int(x)))
                
                # Merge with evaluation dataframe
                df_evaluate = pd.merge(df_evaluate, df_extra, on="user_id", how="left")
                
                # Fill missing values with 0.0
                for feature in extra_features:
                    df_evaluate[feature] = df_evaluate[feature].fillna(0.0)
                    
                self.logger.info(f"Successfully merged extra evaluation dataset")
        
        return df_evaluate 