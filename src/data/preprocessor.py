"""
Data preprocessing module for conversion rate prediction models.
"""
import numpy as np
import pandas as pd
import json
import logging


class DataPreprocessor:
    """Data preprocessor class for feature and label preparation."""
    
    def __init__(self, feature_padding_dict=None, train_dates=None):
        """
        Initialize preprocessor with feature padding configuration.
        
        Args:
            feature_padding_dict (dict, optional): Dictionary with feature padding configurations.
            train_dates (list, optional): List of dates to use for training data.
        """
        self.feature_padding_dict = feature_padding_dict
        self.train_dates = train_dates
        self.logger = logging.getLogger(__name__)
    
    def preprocess_features(self, df_base, feature_padding_dict=None):
        """
        Preprocess features according to padding configuration.
        
        Args:
            df_base (pd.DataFrame): Base dataframe with features.
            feature_padding_dict (dict, optional): Dictionary with feature padding configurations.
            
        Returns:
            pd.DataFrame: Dataframe with preprocessed features.
        """
        # Use instance padding dict if not provided
        if feature_padding_dict is None:
            feature_padding_dict = self.feature_padding_dict
            
        if feature_padding_dict is None:
            self.logger.warning("No feature padding dictionary provided")
            return df_base
            
        self.logger.info("Preprocessing features according to padding configuration")
        
        for feature, feature_info in feature_padding_dict.items():
            try:
                # Skip if feature not in dataframe
                if feature not in df_base.columns:
                    self.logger.debug(f"Feature {feature} from padding_dict not found in DataFrame. Skipping.")
                    continue
                    
                # Get parameters
                data_type = feature_info['data_type']  # Data type
                base_value = feature_info.get('base_value', None)  # Default value
                min_value = feature_info.get('min_value', None)  # Minimum value
                max_value = feature_info.get('max_value', None)  # Maximum value
                
                # Process string features
                if data_type == 'string':
                    df_base[feature] = df_base[feature].astype('string')
                    if base_value is not None:
                        df_base[feature] = df_base[feature].fillna(base_value)
                    else:
                        df_base[feature] = df_base[feature].fillna('None') # Consistent with some downstream expectations
                        
                # Process numeric features
                elif data_type in ['float', 'double']:
                    # Convert to numeric, coercing errors to NaN first for inspection if needed, then fillna
                    df_base[feature] = pd.to_numeric(df_base[feature], errors='coerce')
                    df_base[feature] = df_base[feature].astype('float') # astype float after to_numeric handles mixed types better

                    if base_value is not None and base_value != 'None':
                        df_base[feature] = df_base[feature].fillna(float(base_value))
                    else:
                        df_base[feature] = df_base[feature].fillna(0.0) # Use float 0.0
                        
                    # Apply min/max constraints
                    if min_value is not None and min_value != 'None':
                        df_base.loc[df_base[feature] < float(min_value), feature] = float(min_value)
                    if max_value is not None and max_value != 'None':
                        df_base.loc[df_base[feature] > float(max_value), feature] = float(max_value)
            except Exception as e:
                self.logger.error(f"Error processing feature '{feature}' in preprocess_features: {e}")
                self.logger.error(f"Feature info: {feature_info}")
                self.logger.error(f"Problematic data sample for {feature}: {df_base[feature].head().to_dict()}")
                raise # Re-raise the exception to stop execution as before, but after logging
                    
        return df_base
    
    def preprocess_model_features(self, df, dict_raw_features):
        """
        Preprocess model features according to model configuration.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            dict_raw_features (dict): Raw feature definitions from model configuration.
            
        Returns:
            pd.DataFrame: Dataframe with preprocessed model features.
        """
        self.logger.info("Preprocessing model features")
        
        for feature_name, feature_info in dict_raw_features.items():
            # Feature type (sequence or non-sequence)
            feature_type = feature_info.get("type", "table")
            assert feature_type in ["table", "VarLen"]
            
            # Feature column subtype
            feature_dtype = feature_info.get("dtype", "StringLookup")
            assert feature_dtype in ["StringLookup", "Bucket", "Dense", "Embedding", "VarLen"]
            
            # Category feature vocabulary
            vocabulary = feature_info.get("vocabulary", [])
            
            # Sequence feature padding value & max length
            padd_value = feature_info.get("padd_value", None)
            max_len = feature_info.get("max_len", None)
            
            # Process non-sequence features
            if feature_type == "table":
                if feature_dtype == "StringLookup":
                    # Convert to string type
                    df[feature_name] = df[feature_name].astype("str")
                    
                    # Auto-generate vocabulary from training data if not provided
                    if len(vocabulary) == 0:
                        # 这里与旧版逻辑保持一致 - 需要加入datetime过滤，以仅使用训练数据生成词表
                        if "datetime" in df.columns and hasattr(self, "train_dates"):
                            train_data = df[df["datetime"].isin(self.train_dates)]
                            category_counts = train_data[feature_name].value_counts(normalize=True)
                        else:
                            category_counts = df[feature_name].value_counts(normalize=True)
                        
                        # 频率低于万分之一的聚合为LowFreq
                        high_freq_values = list(category_counts[category_counts >= 1/1000].index)
                        vocabulary = sorted(list(set(high_freq_values + ["LowFreq"])))
                        
                        # 重要：将生成的词表写回到特征配置中
                        feature_info["vocabulary"] = vocabulary
                        
                        self.logger.info(f"Generated vocabulary for {feature_name} with {len(vocabulary)} items")
                    
                    # Apply low frequency category aggregation
                    df[feature_name] = np.where(~df[feature_name].isin(vocabulary), 
                                               'LowFreq', 
                                               df[feature_name])
                        
                elif feature_dtype == "Bucket":
                    # Ensure the column is numeric first
                    df[feature_name] = pd.to_numeric(df[feature_name], errors='coerce')
                    # Get bin boundaries from feature_info
                    bin_boundaries = feature_info.get("bin_boundarie", None)
                    if bin_boundaries and isinstance(bin_boundaries, list) and len(bin_boundaries) > 0:
                        # Convert values to bucket indices (0 to N)
                        # np.digitize returns indices from 1 to len(bins), so subtract 1 for 0-based indexing
                        # Bins are (edge1, edge2], (edge2, edge3] ...
                        # Values less than or equal to the first boundary go to bin 0.
                        # Values greater than the last boundary go to bin len(bin_boundaries).
                        df[feature_name] = np.digitize(df[feature_name].astype(float), bins=sorted(bin_boundaries))
                        self.logger.info(f"Applied bucketing for {feature_name}. Num bins: {len(bin_boundaries) + 1}")
                    else:
                        self.logger.warning(f"Feature {feature_name} is Bucket type but has no/invalid bin_boundarie. Leaving as numeric. Max value: {df[feature_name].max()}")
                        # Fallback: if no bins, ensure it's float, Keras model might treat it via Dense path if not caught by Embedding logic.
                        # Or, this could indicate a config error that needs fixing.
                        df[feature_name] = df[feature_name].astype("float") # Ensure it's float if not bucketed
                    
            # Process sequence features
            elif feature_type == "VarLen":
                # Ensure max_len and padd_value are specified for sequence features
                assert max_len is not None and padd_value is not None
                df[feature_name] = df[feature_name].apply(
                    lambda x: self.preprocess_varlen(x, max_len, padd_value, feature_dtype))
                
        return df
    
    def preprocess_varlen(self, X, sequence_length, default_value, data_type, no_split=0, sep=","):
        """
        Preprocess variable length sequences.
        
        Args:
            X: Input sequence (string or list).
            sequence_length (int): Max sequence length.
            default_value: Default padding value.
            data_type (str): Data type for conversion.
            no_split (int): Whether to split string.
            sep (str): Separator for string splitting.
            
        Returns:
            list: Processed sequence.
        """
        # Convert input to list
        if no_split == 0:
            value_list = [] if X in ['', 'None'] else X.split(sep)
        else:
            if X is None:
                value_list = []
            elif isinstance(X, float) and pd.isnull(X):
                value_list = []
            else:
                value_list = X.tolist()
        
        # Adjust sequence length through padding or truncation
        actual_sequence_length = len(value_list)
        if actual_sequence_length < sequence_length:
            value_list = [default_value] * (sequence_length - actual_sequence_length) + value_list
        elif actual_sequence_length > sequence_length:
            value_list = value_list[-sequence_length:]
        
        # Convert to appropriate data type
        if data_type == 'Bucket':
            value_list = [float(x) for x in value_list]
            
        return value_list
    
    def process_purchase_labels(self, df, source_column="purchase_days_nio_new_car_total", target_column="m_purchase_days_nio_new_car"):
        """
        Process purchase labels for model training.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            source_column (str): Source column name with purchase data.
            target_column (str): Target column name for processed labels.
            
        Returns:
            pd.DataFrame: Dataframe with processed labels.
        """
        self.logger.info(f"Processing purchase labels from {source_column} to {target_column}")
        df = df.copy()
        
        # For training data with array already in JSON
        if target_column in df.columns:
            df[target_column] = df[target_column].apply(json.loads)
        # For evaluation data that needs conversion
        else:
            df[target_column] = df[source_column].apply(lambda x: self.gene_purchase_array(x)[0])
            
        # Generate cumulative sum version for training
        df[f"{target_column}_consum"] = np.clip(
            np.cumsum(np.array(df[target_column].tolist()), axis=1), 0, 1).tolist()
            
        return df
        
    @staticmethod
    def gene_purchase_array(purchase_str):
        """
        Generate purchase array from purchase string.
        
        Args:
            purchase_str (str): Purchase string.
            
        Returns:
            tuple: (monthly purchase array, daily purchase array)
        """
        m_array = [0, 0, 0, 0, 0, 0]
        d_array = [0] * 180
        
        if pd.isna(purchase_str) or purchase_str == "" or purchase_str == "None":
            return m_array, d_array
        
        # Handle problematic string formats
        if isinstance(purchase_str, str):
            # First replace commas with periods
            purchase_str = purchase_str.replace(',', '.')
            
            # If there are multiple periods, keep only the first one
            parts = purchase_str.split('.')
            if len(parts) > 2:
                purchase_str = parts[0] + '.' + ''.join(parts[1:])
            
            # Remove any non-numeric characters (except the first period)
            clean_str = ''
            period_found = False
            
            for char in purchase_str:
                if char.isdigit():
                    clean_str += char
                elif char == '.' and not period_found:
                    clean_str += char
                    period_found = True
            
            purchase_str = clean_str if clean_str else '0'
            
        try:
            purchase_day = int(float(purchase_str))
        except (ValueError, TypeError):
            # If conversion fails, default to 0
            purchase_day = 0
        
        if purchase_day <= 0:
            # No purchase within time window
            return m_array, d_array
        elif purchase_day <= 180:
            # Purchase within time window
            d_array[purchase_day - 1] = 1
            m_month = (purchase_day - 1) // 30
            m_array[min(m_month, 5)] = 1
            return m_array, d_array
        else:
            # Purchase outside time window
            return m_array, d_array 