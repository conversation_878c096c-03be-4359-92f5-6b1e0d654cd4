"""
Feature builder module for conversion rate prediction models.
"""
import numpy as np
import tensorflow as tf
import logging


class FeatureBuilder:
    """Feature builder class to convert dataframes into model inputs."""
    
    def __init__(self):
        """Initialize the feature builder."""
        self.logger = logging.getLogger(__name__)
    
    def generate_dataset(self, 
                        df_input, 
                        raw_feature,
                        label=None, 
                        batch_size=8192,
                        mask_label=None,
                        predict_method="6m",
                        use_multitask=False):
        """
        Generate TensorFlow dataset from dataframe.
        
        Args:
            df_input (pd.DataFrame): Input dataframe.
            raw_feature (dict): Raw feature definitions.
            label (str, optional): Label column name.
            batch_size (int): Batch size for training.
            mask_label (str, optional): Mask label column name.
            predict_method (str): Prediction method ("1m" or "6m").
            
        Returns:
            tf.data.Dataset or dict: Training dataset or feature dict for inference.
        """
        self.logger.info(f"Generating {'inference features' if label is None else 'training dataset'}")
        
        # 获取序列特征列表
        sequence_features = self._get_sequence_features(raw_feature)
        
        # Create feature dictionary
        feature_dict = {}
        for feature_name, feature_info in raw_feature.items():
            feature_type = feature_info.get("type", "table")
            feature_dtype = feature_info.get("dtype", "StringLookup")
            
            # Handle sequence features specially
            if feature_name in sequence_features:
                # 序列特征需要保持其序列结构
                if feature_name in df_input.columns:
                    raw_sequences = df_input[feature_name].tolist()
                    # 转换为固定长度的序列，填充或截断
                    processed_sequences = self._process_sequences(raw_sequences)
                    feature_dict[feature_name] = np.array(processed_sequences)
                else:
                    # 如果序列特征不存在，创建默认序列
                    default_seq = np.full((len(df_input), 50), '0', dtype=object)  # 50是默认序列长度
                    feature_dict[feature_name] = default_seq
            # Handle different types of other features
            elif feature_type == "VarLen" or feature_dtype in ["Dense", "Embedding"]:
                feature_dict[feature_name] = np.array(df_input[feature_name].tolist())
            else:
                feature_dict[feature_name] = np.array(df_input[feature_name])
        
        # Ensure user_id is included if present in the DataFrame, as it's needed for embeddings
        if 'user_id' in df_input.columns and 'user_id' not in feature_dict:
            self.logger.info("Adding 'user_id' to feature_dict for embedding lookup.")
            # user_id is typically an identifier, keep its original type from the DataFrame for the py_function
            # The py_function in trainer.py will handle conversion to string/int for dict lookup
            feature_dict['user_id'] = df_input['user_id'].values
        
        # For inference, return just the feature dictionary
        if label is None:
            return feature_dict
        
        # For training, create full dataset with labels
        else:
            # Convert labels to numpy array
            label_array = np.array(df_input[label].tolist()).astype(np.float32)
            
            # Create appropriate dataset based on mask presence and prediction method
            # 智能条件判断：只有真正启用多任务时才拼接mask_label
            # use_multitask参数从函数调用传入
            if mask_label is None or predict_method == "1m" or not use_multitask:
                # 单任务模式：只使用主标签
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, label_array))
                self.logger.info(f"Single-task mode (use_multitask={use_multitask}): label shape {label_array.shape}")
            else:
                # 多任务模式：拼接主标签和mask标签
                mask_array = np.array(df_input[mask_label].tolist()).astype(np.float32)
                combined_labels = np.concatenate([label_array, mask_array], axis=-1)
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, combined_labels))
                self.logger.info(f"Multi-task mode: label shape {label_array.shape} + mask shape {mask_array.shape} = {combined_labels.shape}")
            
            self.logger.info("Dataset creation complete")
            
            # Apply batching and prefetching for optimal performance
            ds = ds.batch(batch_size)
            ds = ds.prefetch(batch_size)
            
            return ds
    
    def split_train_test(self, df, test_dates):
        """
        Split dataset into training and test sets based on dates.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            test_dates (list): List of dates to use for testing.
            
        Returns:
            tuple: (Training dataframe, Testing dataframe)
        """
        self.logger.info(f"Splitting dataset using test dates: {test_dates}")
        
        # Shuffle training data
        df_shuffled = df.sample(frac=1) 
        
        # Split based on dates
        df_train = df_shuffled[~df_shuffled["datetime"].isin(test_dates)]
        df_test = df_shuffled[df_shuffled["datetime"].isin(test_dates)]
        
        # Sort training data by date for temporal consistency
        df_train = df_train.sort_values("datetime", ascending=True)
        
        self.logger.info(f"Split complete. Training set: {len(df_train)}, Test set: {len(df_test)}")
        
        return df_train, df_test 
    
    def _get_sequence_features(self, model_config):
        """
        从模型配置中提取序列特征名称
        
        Args:
            model_config (dict): 模型配置，可能是RawFeature或完整配置
            
        Returns:
            set: 序列特征名称集合
        """
        sequence_features = set()
        
        # 检查是否是完整的模型配置（包含InputSeqSet）
        if "InputSeqSet" in model_config:
            seq_set_info = model_config["InputSeqSet"].get("SetInfo", {})
            for set_name, set_config in seq_set_info.items():
                if "features" in set_config:
                    sequence_features.update(set_config["features"])
        
        # 也检查RawFeature中名称包含"seq"的特征
        raw_features = model_config.get("RawFeature", model_config)
        for feature_name in raw_features:
            if "seq" in feature_name.lower():
                sequence_features.add(feature_name)
        
        return sequence_features
    
    def _process_sequences(self, raw_sequences, max_length=50):
        """
        处理序列数据，确保统一长度
        
        Args:
            raw_sequences (list): 原始序列列表
            max_length (int): 最大序列长度
            
        Returns:
            np.ndarray: 处理后的序列数组
        """
        processed_sequences = []
        
        for seq in raw_sequences:
            if isinstance(seq, (list, tuple)):
                # 如果已经是序列，进行填充或截断
                if len(seq) > max_length:
                    processed_seq = seq[:max_length]  # 截断
                else:
                    processed_seq = seq + ['0'] * (max_length - len(seq))  # 填充
            elif isinstance(seq, str):
                # 如果是字符串，尝试转换
                if seq in ['None', '', 'null', 'nan']:
                    # 空值或None，使用默认序列
                    processed_seq = ['0'] * max_length
                else:
                    try:
                        # 尝试按逗号分割
                        if ',' in seq:
                            seq_list = [s.strip() for s in seq.split(',')]
                        else:
                            # 尝试eval
                            seq_list = eval(seq)
                        
                        # 转换为字符串（保持原始值）或数字索引
                        if len(seq_list) > max_length:
                            processed_seq = seq_list[:max_length]
                        else:
                            processed_seq = seq_list + ['0'] * (max_length - len(seq_list))
                            
                    except:
                        # 转换失败，使用默认序列
                        processed_seq = ['0'] * max_length
            elif pd.isna(seq) or seq is None:
                # 空值处理
                processed_seq = ['0'] * max_length
            else:
                # 单个值，转换为长度为1的序列
                processed_seq = [str(seq)] + ['0'] * (max_length - 1)
            
            processed_sequences.append(processed_seq)
        
        # 对于字符串序列，返回object类型的数组
        return np.array(processed_sequences, dtype=object)