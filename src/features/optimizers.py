"""
特征优化模块 - 基于数据分析的特征质量提升

主要功能：
1. 特征去冗余 - 移除高相关性特征
2. 缺失值优化 - 业务逻辑驱动的缺失值处理
3. 类别特征重编码 - 长尾分布优化
4. 特征重要性分析 - 数据驱动的特征选择
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import LabelEncoder
import json


class FeatureOptimizer:
    """
    特征优化器 - 基于数据特点的智能特征优化
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化特征优化器
        
        Args:
            config: 优化配置字典
        """
        self.config = config or self._get_default_config()
        self.logger = logging.getLogger(__name__)
        
        # 特征相关性缓存
        self.correlation_cache = {}
        # 特征重要性缓存
        self.importance_cache = {}
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认优化配置"""
        return {
            'correlation_threshold': 0.8,
            'importance_threshold': 0.001,
            'missing_threshold': 0.7,
            'categorical_top_k': 20,
            'low_freq_threshold': 0.01
        }
    
    def optimize_features(self, 
                         df: pd.DataFrame, 
                         target_column: str,
                         feature_config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        执行完整的特征优化流程
        
        Args:
            df: 输入数据框
            target_column: 目标列名
            feature_config: 特征配置
            
        Returns:
            优化后的数据框和优化报告
        """
        self.logger.info("开始特征优化流程")
        optimization_report = {
            'original_features': len(df.columns),
            'steps': []
        }
        
        # 1. 特征去冗余
        df_dedup, dedup_report = self.remove_redundant_features(df, target_column)
        optimization_report['steps'].append(('去冗余', dedup_report))
        
        # 2. 缺失值优化
        df_missing, missing_report = self.optimize_missing_values(df_dedup, feature_config)
        optimization_report['steps'].append(('缺失值优化', missing_report))
        
        # 3. 类别特征重编码
        df_categorical, categorical_report = self.optimize_categorical_features(df_missing)
        optimization_report['steps'].append(('类别特征优化', categorical_report))
        
        # 4. 特征重要性筛选
        df_final, importance_report = self.select_important_features(df_categorical, target_column)
        optimization_report['steps'].append(('重要性筛选', importance_report))
        
        optimization_report['final_features'] = len(df_final.columns)
        optimization_report['reduction_rate'] = 1 - len(df_final.columns) / len(df.columns)
        
        self.logger.info(f"特征优化完成: {len(df.columns)} → {len(df_final.columns)} "
                        f"(减少{optimization_report['reduction_rate']:.1%})")
        
        return df_final, optimization_report
    
    def remove_redundant_features(self, 
                                 df: pd.DataFrame, 
                                 target_column: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        移除冗余特征
        
        基于特征相关性分析，移除高度相关的特征对中重要性较低的特征
        """
        self.logger.info("开始特征去冗余")
        
        # 获取数值特征
        numeric_features = df.select_dtypes(include=[np.number]).columns.tolist()
        if target_column in numeric_features:
            numeric_features.remove(target_column)
        
        # 计算特征相关性矩阵
        correlation_matrix = df[numeric_features].corr().abs()
        
        # 找到高相关性特征对
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if corr_value > self.config['correlation_threshold']:
                    feature1 = correlation_matrix.columns[i]
                    feature2 = correlation_matrix.columns[j]
                    high_corr_pairs.append((feature1, feature2, corr_value))
        
        # 基于重要性选择保留的特征
        features_to_remove = set()
        for feature1, feature2, corr_value in high_corr_pairs:
            if feature1 not in features_to_remove and feature2 not in features_to_remove:
                # 计算特征重要性
                importance1 = self._calculate_feature_importance(df, feature1, target_column)
                importance2 = self._calculate_feature_importance(df, feature2, target_column)
                
                # 移除重要性较低的特征
                if importance1 < importance2:
                    features_to_remove.add(feature1)
                else:
                    features_to_remove.add(feature2)
        
        # 应用时间窗口特征优化策略
        time_window_removals = self._optimize_time_window_features(df.columns.tolist())
        features_to_remove.update(time_window_removals)
        
        # 移除冗余特征
        df_optimized = df.drop(columns=list(features_to_remove))
        
        report = {
            'high_corr_pairs': len(high_corr_pairs),
            'removed_features': len(features_to_remove),
            'removed_feature_list': list(features_to_remove)
        }
        
        self.logger.info(f"去冗余完成: 移除{len(features_to_remove)}个特征")
        return df_optimized, report
    
    def _optimize_time_window_features(self, feature_names: List[str]) -> List[str]:
        """
        优化时间窗口特征 - 基于业务逻辑的时间窗口选择
        """
        features_to_remove = []
        
        # 时间窗口优化策略
        time_window_strategies = {
            'search_intention_cnt': {
                'keep': ['30d', '90d'],
                'remove': ['1d', '7d', '14d', '60d', '180d']
            },
            'login_count': {
                'keep': ['30d', '180d'],
                'remove': ['60d', '90d']
            },
            'leads_count': {
                'keep': ['60d', '180d'],
                'remove': ['30d', '90d']
            }
        }
        
        for pattern, strategy in time_window_strategies.items():
            for feature_name in feature_names:
                if pattern in feature_name:
                    for remove_window in strategy['remove']:
                        if remove_window in feature_name:
                            features_to_remove.append(feature_name)
        
        return features_to_remove
    
    def optimize_missing_values(self, 
                               df: pd.DataFrame, 
                               feature_config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        优化缺失值处理 - 业务逻辑驱动的缺失值策略
        """
        self.logger.info("开始缺失值优化")
        
        df_optimized = df.copy()
        missing_strategies = {}
        
        # 业务逻辑驱动的缺失值处理策略
        business_missing_strategies = {
            'app_search_intention_DSLA': {
                'strategy': 'business_logic',
                'fill_value': 999,  # 特殊值表示无搜索行为
                'description': '缺失表示用户无搜索行为'
            },
            'user_core_first_reg_leads_nio_DSLA': {
                'strategy': 'median_by_group',
                'group_by': 'user_core_nio_user_identity',
                'description': '按用户身份分组填充中位数'
            },
            'user_core_unfirst_reg_leads_nio_DSLA': {
                'strategy': 'median_by_group', 
                'group_by': 'user_core_nio_user_identity',
                'description': '按用户身份分组填充中位数'
            }
        }
        
        for column in df_optimized.columns:
            missing_rate = df_optimized[column].isnull().sum() / len(df_optimized)
            
            if missing_rate > 0:
                if column in business_missing_strategies:
                    strategy = business_missing_strategies[column]
                    missing_strategies[column] = strategy
                    
                    if strategy['strategy'] == 'business_logic':
                        df_optimized[column] = df_optimized[column].fillna(strategy['fill_value'])
                    elif strategy['strategy'] == 'median_by_group':
                        if strategy['group_by'] in df_optimized.columns:
                            df_optimized[column] = df_optimized.groupby(strategy['group_by'])[column].transform(
                                lambda x: x.fillna(x.median())
                            )
                        else:
                            df_optimized[column] = df_optimized[column].fillna(df_optimized[column].median())
                
                elif missing_rate > self.config['missing_threshold']:
                    # 高缺失率特征考虑移除
                    missing_strategies[column] = {
                        'strategy': 'consider_removal',
                        'missing_rate': missing_rate
                    }
                else:
                    # 默认策略：数值特征用中位数，类别特征用众数
                    if df_optimized[column].dtype in ['object', 'category']:
                        mode_value = df_optimized[column].mode()
                        fill_value = mode_value[0] if len(mode_value) > 0 else 'Unknown'
                        df_optimized[column] = df_optimized[column].fillna(fill_value)
                        missing_strategies[column] = {'strategy': 'mode', 'fill_value': fill_value}
                    else:
                        median_value = df_optimized[column].median()
                        df_optimized[column] = df_optimized[column].fillna(median_value)
                        missing_strategies[column] = {'strategy': 'median', 'fill_value': median_value}
        
        report = {
            'processed_columns': len(missing_strategies),
            'strategies': missing_strategies
        }
        
        self.logger.info(f"缺失值优化完成: 处理{len(missing_strategies)}个特征")
        return df_optimized, report
    
    def optimize_categorical_features(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        优化类别特征 - 长尾分布处理和重编码
        """
        self.logger.info("开始类别特征优化")
        
        df_optimized = df.copy()
        categorical_optimizations = {}
        
        # 类别特征优化策略
        categorical_strategies = {
            'user_core_resident_city': {
                'top_k': 20,
                'merge_others': True,
                'encoding': 'frequency_based'
            },
            'user_core_pred_career_type': {
                'merge_none': True,
                'low_freq_threshold': 0.01
            }
        }
        
        categorical_columns = df_optimized.select_dtypes(include=['object', 'category']).columns
        
        for column in categorical_columns:
            if column in categorical_strategies:
                strategy = categorical_strategies[column]
                categorical_optimizations[column] = strategy
                
                value_counts = df_optimized[column].value_counts()
                
                if strategy.get('top_k'):
                    # 保留前K个高频类别，其他合并为"Others"
                    top_values = value_counts.head(strategy['top_k']).index.tolist()
                    df_optimized[column] = df_optimized[column].apply(
                        lambda x: x if x in top_values else 'Others'
                    )
                
                if strategy.get('merge_none'):
                    # 合并None/空值类别
                    df_optimized[column] = df_optimized[column].replace(['None', '', np.nan], 'Unknown')
                
                if strategy.get('low_freq_threshold'):
                    # 合并低频类别
                    threshold = strategy['low_freq_threshold']
                    low_freq_values = value_counts[value_counts / len(df_optimized) < threshold].index.tolist()
                    df_optimized[column] = df_optimized[column].apply(
                        lambda x: 'LowFreq' if x in low_freq_values else x
                    )
        
        report = {
            'optimized_columns': len(categorical_optimizations),
            'strategies': categorical_optimizations
        }
        
        self.logger.info(f"类别特征优化完成: 优化{len(categorical_optimizations)}个特征")
        return df_optimized, report
    
    def select_important_features(self, 
                                 df: pd.DataFrame, 
                                 target_column: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        基于重要性选择特征
        """
        self.logger.info("开始特征重要性筛选")
        
        if target_column not in df.columns:
            self.logger.warning(f"目标列{target_column}不存在，跳过重要性筛选")
            return df, {'skipped': True}
        
        # 获取数值特征进行重要性分析
        numeric_features = df.select_dtypes(include=[np.number]).columns.tolist()
        if target_column in numeric_features:
            numeric_features.remove(target_column)
        
        if len(numeric_features) == 0:
            return df, {'no_numeric_features': True}
        
        # 计算特征重要性
        feature_importance = {}
        for feature in numeric_features:
            importance = self._calculate_feature_importance(df, feature, target_column)
            feature_importance[feature] = importance
        
        # 选择重要特征
        important_features = [
            feature for feature, importance in feature_importance.items()
            if importance > self.config['importance_threshold']
        ]
        
        # 保留重要特征和目标列
        features_to_keep = important_features + [target_column]
        # 同时保留非数值特征（类别特征等）
        non_numeric_features = df.select_dtypes(exclude=[np.number]).columns.tolist()
        features_to_keep.extend(non_numeric_features)
        
        df_selected = df[features_to_keep]
        
        report = {
            'total_features_analyzed': len(numeric_features),
            'important_features_selected': len(important_features),
            'feature_importance': feature_importance,
            'threshold': self.config['importance_threshold']
        }
        
        self.logger.info(f"重要性筛选完成: {len(numeric_features)} → {len(important_features)}个重要特征")
        return df_selected, report
    
    def _calculate_feature_importance(self, df: pd.DataFrame, feature: str, target: str) -> float:
        """
        计算特征重要性 - 使用互信息
        """
        if feature in self.importance_cache:
            return self.importance_cache[feature]
        
        try:
            # 处理缺失值
            feature_data = df[feature].fillna(df[feature].median() if df[feature].dtype in ['int64', 'float64'] else 'Unknown')
            target_data = df[target]
            
            # 如果是类别特征，进行标签编码
            if feature_data.dtype == 'object':
                le = LabelEncoder()
                feature_data = le.fit_transform(feature_data.astype(str))
            
            # 计算互信息
            importance = mutual_info_regression(
                feature_data.values.reshape(-1, 1), 
                target_data, 
                random_state=42
            )[0]
            
            self.importance_cache[feature] = importance
            return importance
            
        except Exception as e:
            self.logger.warning(f"计算特征{feature}重要性失败: {e}")
            return 0.0
