"""
特征适配器 - 用于动态扩展特征类型，避免模型代码重复
"""
import tensorflow as tf
import numpy as np
from typing import Dict, Optional, List

class EmbeddingFeatureAdapter(tf.keras.layers.Layer):
    """
    嵌入特征适配器 - 将预训练嵌入向量集成到现有模型中
    
    这样做的好处：
    1. 不需要复制整个模型类
    2. 可以动态添加/移除嵌入特征
    3. 保持现有模型代码不变
    """
    
    def __init__(self, 
                 embedding_dim: int,
                 feature_name: str = "user_embedding",
                 trainable: bool = False,
                 **kwargs):
        super().__init__(**kwargs)
        self.embedding_dim = embedding_dim
        self.feature_name = feature_name
        self.trainable = trainable
        
        # 用于处理缺失嵌入的默认向量
        self.default_embedding = self.add_weight(
            name="default_embedding",
            shape=(embedding_dim,),
            initializer="zeros",
            trainable=self.trainable
        )
    
    def call(self, inputs, training=None):
        """
        处理嵌入特征输入
        
        Args:
            inputs: 包含嵌入向量的字典，格式 {user_id: embedding_vector}
            
        Returns:
            处理后的嵌入特征tensor
        """
        if self.feature_name in inputs:
            return inputs[self.feature_name]
        else:
            # 如果没有嵌入特征，返回默认向量
            batch_size = tf.shape(list(inputs.values())[0])[0]
            return tf.tile(
                tf.expand_dims(self.default_embedding, 0), 
                [batch_size, 1]
            )

class DynamicFeatureComposer(tf.keras.layers.Layer):
    """
    动态特征组合器 - 根据可用特征动态组合输入
    
    这解决了配置管理和特征扩展的问题
    """
    
    def __init__(self, base_features: List[str], optional_features: List[str] = None, **kwargs):
        super().__init__(**kwargs)
        self.base_features = base_features
        self.optional_features = optional_features or []
        
    def call(self, inputs, training=None):
        """组合可用的特征"""
        features = []
        
        # 添加基础特征
        for feature_name in self.base_features:
            if feature_name in inputs:
                features.append(inputs[feature_name])
        
        # 添加可选特征（如嵌入向量）
        for feature_name in self.optional_features:
            if feature_name in inputs:
                features.append(inputs[feature_name])
        
        if features:
            return tf.concat(features, axis=-1)
        else:
            raise ValueError("No valid features found in inputs")

class ModelVersionManager:
    """
    模型版本管理器 - 用于管理不同版本的模型配置
    
    这解决了配置文件过于庞大的问题
    """
    
    @staticmethod
    def create_embedding_config(base_config: dict, embedding_features: List[str]) -> dict:
        """
        基于基础配置创建支持嵌入特征的配置
        
        Args:
            base_config: 基础模型配置
            embedding_features: 嵌入特征列表
            
        Returns:
            扩展后的配置
        """
        config = base_config.copy()
        
        # 在InputGeneral中添加嵌入特征
        if "InputGeneral" not in config:
            config["InputGeneral"] = {"features": []}
        
        config["InputGeneral"]["features"].extend(embedding_features)
        
        # 为嵌入特征添加RawFeature配置
        if "RawFeature" not in config:
            config["RawFeature"] = {}
        
        for feature in embedding_features:
            config["RawFeature"][feature] = {
                "type": "Dense",  # 直接使用Dense层，不需要Embedding
                "dimension": 16   # 可配置的维度
            }
        
        return config