"""
Custom layer implementations for conversion rate estimation models.
"""
import tensorflow as tf


class CrossLayer(tf.keras.layers.Layer):
    """Feature cross layer to enhance feature interactions."""
    
    def __init__(self, layer_size=64, l2_reg=0.01):
        """
        Initialize cross layer.
        
        Args:
            layer_size (int): Size of the output layer.
            l2_reg (float): L2 regularization factor.
        """
        super(CrossLayer, self).__init__()
        self.layer_size = layer_size
        self.l2_reg = l2_reg
        
    def build(self, input_shape):
        """
        Build layer weights.
        
        Args:
            input_shape: Shape of input tensor.
        """
        self.input_dim = int(input_shape[-1])
        self.w = self.add_weight(
            name='cross_w',
            shape=(self.input_dim, self.layer_size),
            initializer='glorot_normal',
            regularizer=tf.keras.regularizers.l2(self.l2_reg),
            trainable=True
        )
        self.b = self.add_weight(
            name='cross_b',
            shape=(self.layer_size,),
            initializer='zeros',
            trainable=True
        )
        # 不使用tf.function装饰以避免图捕获问题
        # self.call = tf.function(self.call)
        super(CrossLayer, self).build(input_shape)
        
    def call(self, inputs):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor.
            
        Returns:
            Tensor: Cross layer output.
        """
        # More efficient implementation avoiding multiple expand_dims
        x_w = tf.matmul(inputs, self.w)  # (batch_size, layer_size)
        return x_w + self.b  # (batch_size, layer_size)


class TimeSeriesAttention(tf.keras.layers.Layer):
    """Time series attention layer with time decay weights."""
    
    def __init__(self, attention_dim=32, time_decay_factor=0.1, supports_masking=True):
        """
        Initialize time series attention layer.
        
        Args:
            attention_dim (int): Attention mechanism dimension.
            time_decay_factor (float): Factor controlling time decay.
            supports_masking (bool): Whether layer supports masking.
        """
        super(TimeSeriesAttention, self).__init__()
        self.attention_dim = attention_dim
        self.time_decay_factor = time_decay_factor
        self.supports_masking = supports_masking
        
    def build(self, input_shape):
        """
        Build layer weights.
        
        Args:
            input_shape: Shape of input tensor.
        """
        # Build attention weight matrices
        self.seq_len = input_shape[1]
        self.feature_dim = input_shape[2]
        
        self.W = self.add_weight(
            name='attention_W',
            shape=(self.feature_dim, self.attention_dim),
            initializer='glorot_normal',
            regularizer=tf.keras.regularizers.l2(0.01),
            trainable=True
        )
        self.u = self.add_weight(
            name='attention_u',
            shape=(self.attention_dim, 1),
            initializer='glorot_normal',
            regularizer=tf.keras.regularizers.l2(0.01),
            trainable=True
        )
        
        # 不使用tf.function装饰以避免图捕获问题
        # self.call = tf.function(self.call)
        
        super(TimeSeriesAttention, self).build(input_shape)
        
    def call(self, inputs, mask=None):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor.
            mask: Mask tensor.
            
        Returns:
            Tensor: Attention layer output.
        """
        # Get input data type
        dtype = inputs.dtype
        
        # Calculate attention scores with time decay
        uit = tf.tanh(tf.tensordot(inputs, self.W, axes=1))
        ait = tf.tensordot(uit, self.u, axes=1)
        ait = tf.squeeze(ait, -1)
        
        # Time decay weights - recent behavior gets higher weight
        time_decay_range = tf.range(self.seq_len, 0, -1, dtype=tf.float32)
        time_weights = tf.exp(-self.time_decay_factor * time_decay_range)
        # Convert to same data type as inputs
        time_weights = tf.cast(time_weights, dtype)
        
        # Apply decay weights and mask
        attention_weights = ait * time_weights
        
        if mask is not None:
            # Ensure mask has correct data type
            mask_float = tf.cast(mask, dtype)
            attention_weights += (1.0 - mask_float) * tf.cast(-1e9, dtype)
            
        # Add numerical stability measures
        attention_weights = tf.nn.softmax(attention_weights, axis=1)
        
        # Prevent NaN values
        attention_weights = tf.where(
            tf.math.is_nan(attention_weights),
            tf.zeros_like(attention_weights),
            attention_weights
        )
        
        # Weighted sum
        return tf.reduce_sum(inputs * tf.expand_dims(attention_weights, -1), axis=1) 


class TransformerEncoder(tf.keras.layers.Layer):
    """
    Transformer Encoder layer optimized for sequence modeling in recommendation systems.
    
    Features:
    - Multi-head self-attention mechanism
    - Feed-forward neural network
    - Residual connections and layer normalization
    - Positional encoding with time decay
    - Dropout for regularization
    """
    
    def __init__(self, 
                d_model=64, 
                num_heads=4, 
                ff_dim=128, 
                dropout_rate=0.1, 
                time_decay_factor=0.1,
                use_positional_encoding=True,
                supports_masking=True):
        """
        Initialize Transformer Encoder layer.
        
        Args:
            d_model (int): Dimensionality of the model
            num_heads (int): Number of attention heads
            ff_dim (int): Dimensionality of feed-forward network
            dropout_rate (float): Dropout rate for regularization
            time_decay_factor (float): Factor controlling time decay in attention
            use_positional_encoding (bool): Whether to use positional encoding
            supports_masking (bool): Whether layer supports masking
        """
        super(TransformerEncoder, self).__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        self.dropout_rate = dropout_rate
        self.time_decay_factor = time_decay_factor
        self.use_positional_encoding = use_positional_encoding
        self.supports_masking = supports_masking
        self.pos_encoding = None
    
    def build(self, input_shape):
        """
        Build layer weights.
        
        Args:
            input_shape: Shape of input tensor.
        """
        self.seq_len = input_shape[1]
        self.feature_dim = input_shape[2]
        
        # Projection layer to match dimensions if needed
        if self.feature_dim != self.d_model:
            self.projection = tf.keras.layers.Dense(
                self.d_model, 
                activation=None,
                kernel_regularizer=tf.keras.regularizers.l2(0.01)
            )
        else:
            self.projection = None
        
        # Multi-head attention with residual connection and layer normalization
        self.mha = tf.keras.layers.MultiHeadAttention(
            num_heads=self.num_heads, 
            key_dim=self.d_model // self.num_heads,
            dropout=self.dropout_rate
        )
        self.layernorm1 = tf.keras.layers.LayerNormalization(epsilon=1e-6)
        
        # Feed-forward network with residual connection and layer normalization
        self.ffn = tf.keras.Sequential([
            tf.keras.layers.Dense(
                self.ff_dim, 
                activation='relu',
                kernel_regularizer=tf.keras.regularizers.l2(0.01)
            ),
            tf.keras.layers.Dropout(self.dropout_rate),
            tf.keras.layers.Dense(
                self.d_model,
                kernel_regularizer=tf.keras.regularizers.l2(0.01)
            )
        ])
        self.layernorm2 = tf.keras.layers.LayerNormalization(epsilon=1e-6)
        
        # Dropout layer
        self.dropout = tf.keras.layers.Dropout(self.dropout_rate)
        
        # Pooling mechanism to convert sequence to vector
        self.global_pool = tf.keras.layers.GlobalAveragePooling1D()
        
        # Time decay attention layer for final pooling
        self.time_attention = TimeSeriesAttention(
            attention_dim=self.d_model // 2,
            time_decay_factor=self.time_decay_factor
        )
        
        # 不使用tf.function装饰以避免图捕获问题
        # self.call = tf.function(self.call)
        
        super(TransformerEncoder, self).build(input_shape)
    
    def _positional_encoding(self, length, depth):
        """
        Create positional encoding for transformer.
        
        Args:
            length (int): Sequence length
            depth (int): Feature dimension
            
        Returns:
            tf.Tensor: Positional encoding
        """
        positions = tf.range(length, dtype=tf.float32)[:, tf.newaxis]
        depths = tf.range(depth, dtype=tf.float32)[tf.newaxis, :] / depth
        
        angle_rates = 1 / (10000**depths)
        angle_rads = positions * angle_rates
        
        # Apply sin to even indices and cos to odd indices
        pos_encoding = tf.concat(
            [tf.sin(angle_rads[:, 0::2]), tf.cos(angle_rads[:, 1::2])], 
            axis=-1
        )
        
        return tf.cast(pos_encoding, tf.float32)
    
    def call(self, inputs, training=None, mask=None):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor
            training: Boolean indicating training mode
            mask: Optional mask tensor
            
        Returns:
            tf.Tensor: Encoded sequence representation
        """
        # Project inputs to d_model dimensions if needed
        if self.projection is not None:
            seq = self.projection(inputs)
        else:
            seq = inputs
        
        # Add positional encoding - create dynamically during call to avoid scope issues
        if self.use_positional_encoding:
            # Generate positional encoding during each call to avoid TF scope issues
            # Using eager execution friendly approach to avoid graph capture problems
            batch_size = tf.shape(seq)[0]
            pos_encoding = self._positional_encoding(self.seq_len, self.d_model)
            pos_encoding = tf.expand_dims(pos_encoding, 0)  # [1, seq_len, d_model]
            pos_encoding = tf.repeat(pos_encoding, batch_size, axis=0)  # [batch_size, seq_len, d_model]
            seq = seq + pos_encoding
        
        # Apply custom time decay attention mask
        # Generate time decay weights for attention mechanism
        time_decay_range = tf.range(self.seq_len, 0, -1, dtype=tf.float32)
        time_weights = tf.exp(-self.time_decay_factor * time_decay_range)
        
        # Create causal mask and apply time decay
        # This encourages the model to pay more attention to recent events
        causal_mask = tf.linalg.band_part(
            tf.ones((self.seq_len, self.seq_len)), -1, 0)
        time_mask = tf.einsum('i,j->ij', time_weights, tf.ones(self.seq_len, dtype=tf.float32))
        attention_mask = causal_mask * time_mask
        
        # Self-attention block with residual connection
        attn_output = self.mha(
            query=seq, 
            key=seq, 
            value=seq,
            attention_mask=attention_mask if mask is None else mask,
            training=training
        )
        attn_output = self.dropout(attn_output, training=training)
        out1 = self.layernorm1(seq + attn_output)
        
        # Feed-forward block with residual connection
        ffn_output = self.ffn(out1, training=training)
        ffn_output = self.dropout(ffn_output, training=training)
        out2 = self.layernorm2(out1 + ffn_output)
        
        # Use time-aware attention to aggregate sequence
        sequence_vector = self.time_attention(out2)
        
        return sequence_vector 