"""
模型工厂 - 统一管理不同版本的模型，解决代码重复问题

主要功能：
1. 统一的模型创建接口
2. 向后兼容现有代码
3. 支持动态特征扩展
4. 简化模型版本管理
"""

import logging
from typing import Dict, Any, Optional, Union, List
import tensorflow as tf

from src.models.networks.EPMMOENet_enhanced import EnhancedEPMMOENet
from src.models.networks.EPMMOENet_original import EPMMOENet_Model

class ModelFactory:
    """
    模型工厂类 - 负责创建和管理不同版本的模型
    """
    
    SUPPORTED_MODELS = {
        "EPMMOENet": EPMMOENet_Model,  # 恢复原始高性能版本！
        "EPMMOENet_Transformer": EnhancedEPMMOENet,
        "EPMMOENet_Enhanced": EnhancedEPMMOENet,
        "EPMMOENet_with_embeddings": EnhancedEPMMOENet
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def create_model(self, 
                    model_name: str,
                    model_config: Dict[str, Any],
                    embedding_features: Optional[Dict[str, Any]] = None,
                    **kwargs) -> tf.keras.Model:
        """
        创建模型实例
        
        Args:
            model_name: 模型名称
            model_config: 模型配置
            embedding_features: 嵌入特征配置（可选）
            **kwargs: 其他模型参数
            
        Returns:
            模型实例
        """
        
        if model_name not in self.SUPPORTED_MODELS:
            raise ValueError(f"Unsupported model: {model_name}. "
                           f"Supported models: {list(self.SUPPORTED_MODELS.keys())}")
        
        self.logger.info(f"Creating model: {model_name}")
        
        # 处理嵌入特征版本（重要：这里统一了所有嵌入特征需求）
        if (model_name == "EPMMOENet_with_embeddings" or 
            embedding_features or 
            self._has_embedding_features(model_config)):
            
            return self._create_enhanced_model_with_embeddings(
                model_config, embedding_features, **kwargs)
        
        # 处理标准模型
        elif model_name == "EPMMOENet_Enhanced":
            return EnhancedEPMMOENet(
                model_config=model_config,
                architecture_type="standard",
                **kwargs
            )
        
        elif model_name == "EPMMOENet_Transformer":
            return self._create_transformer_model(model_config, **kwargs)
        
        elif model_name == "EPMMOENet":
            return self._create_standard_model(model_config, **kwargs)
        
        else:
            raise ValueError(f"Model creation not implemented for: {model_name}")
    
    def _create_enhanced_model_with_embeddings(self, 
                                             model_config: Dict[str, Any],
                                             embedding_features: Optional[Dict[str, Any]] = None,
                                             **kwargs) -> EnhancedEPMMOENet:
        """
        创建支持嵌入特征的增强模型
        
        这个方法替代了原来的EPMMOENet_with_embeddings.py
        """
        # 如果没有显式提供嵌入特征，尝试从配置中提取
        if not embedding_features:
            embedding_features = self._extract_embedding_features(model_config)
        
        self.logger.info(f"Creating enhanced model with {len(embedding_features or {})} embedding features")
        
        return EnhancedEPMMOENet(
            model_config=model_config,
            feature_extensions=embedding_features,
            architecture_type="standard",
            **kwargs
        )
    
    def _create_transformer_model(self, model_config: Dict[str, Any], **kwargs) -> EnhancedEPMMOENet:
        """创建Transformer版本的模型"""
        return EnhancedEPMMOENet(
            model_config=model_config,
            architecture_type="transformer",
            **kwargs
        )
    
    def _create_standard_model(self, model_config: Dict[str, Any], **kwargs) -> EPMMOENet_Model:
        """创建标准EPMMOENet模型（向后兼容）"""
        # 过滤掉原始模型不支持的参数
        filtered_kwargs = {}
        supported_params = {
            'output_dimension', 'output_activation', 'default_embedding_dimension', 
            'default_gru_dimension', 'expert_num', 'use_cross_layer', 'use_mixed_precision',
            'use_time_attention', 'time_decay_factor'
        }
        
        for key, value in kwargs.items():
            if key in supported_params:
                filtered_kwargs[key] = value
        
        # 使用原始的高性能EPMMOENet_Model
        return EPMMOENet_Model(
            feature_column=model_config,
            **filtered_kwargs
        )
    
    def _has_embedding_features(self, model_config: Dict[str, Any]) -> bool:
        """检查配置中是否包含嵌入特征"""
        raw_features = model_config.get("RawFeature", {})
        
        for feature_name, feature_info in raw_features.items():
            feature_type = feature_info.get("dtype", feature_info.get("type", ""))
            if feature_type == "Dense" and "embedding" in feature_name.lower():
                return True
        
        return False
    
    def _extract_embedding_features(self, model_config: Dict[str, Any]) -> Dict[str, Any]:
        """从配置中提取嵌入特征"""
        embedding_features = {}
        raw_features = model_config.get("RawFeature", {})
        
        for feature_name, feature_info in raw_features.items():
            feature_type = feature_info.get("dtype", feature_info.get("type", ""))
            if (feature_type == "Dense" and 
                ("embedding" in feature_name.lower() or 
                 feature_info.get("is_embedding", False))):
                embedding_features[feature_name] = feature_info
        
        return embedding_features
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        if model_name not in self.SUPPORTED_MODELS:
            raise ValueError(f"Unknown model: {model_name}")
        
        info = {
            "name": model_name,
            "supports_embeddings": model_name in ["EPMMOENet_Enhanced", "EPMMOENet_with_embeddings"],
            "supports_transformer": model_name in ["EPMMOENet_Transformer", "EPMMOENet_Enhanced"],
            "is_legacy": model_name in ["EPMMOENet", "EPMMOENet_Transformer"]
        }
        
        return info

class BackwardCompatibilityWrapper:
    """
    向后兼容包装器 - 确保现有代码无需修改即可使用新架构
    """
    
    def __init__(self):
        self.factory = ModelFactory()
        self.logger = logging.getLogger(__name__)
    
    def EPMMOENet_Model(self, feature_column, **kwargs):
        """向后兼容：原EPMMOENet_Model接口"""
        return EPMMOENet_Model(feature_column=feature_column, **kwargs)
    
    def EPMMOENet_TransformerModel(self, model_config, **kwargs):
        """向后兼容：原EPMMOENet_TransformerModel接口"""
        return self.factory.create_model("EPMMOENet_Transformer", model_config, **kwargs)
    
    def EPMMOENet_with_embeddings_Model(self, config, feature_dict_len=None, **kwargs):
        """
        向后兼容：原EPMMOENet_with_embeddings.py接口
        
        这个方法可以直接替换原来的类，无需修改调用代码
        """
        self.logger.info("Using backward compatibility wrapper for EPMMOENet_with_embeddings")
        
        # 从kwargs中提取嵌入相关参数
        embedding_dim = kwargs.pop('embedding_dim', 16)
        
        # 构造嵌入特征配置
        embedding_features = self._construct_embedding_features_from_legacy(
            config, feature_dict_len, embedding_dim)
        
        return self.factory.create_model(
            "EPMMOENet_with_embeddings", 
            config, 
            embedding_features=embedding_features,
            **kwargs)
    
    def _construct_embedding_features_from_legacy(self, 
                                                 config: Dict[str, Any], 
                                                 feature_dict_len: Optional[Dict] = None,
                                                 embedding_dim: int = 16) -> Dict[str, Any]:
        """从旧版参数构造嵌入特征配置"""
        embedding_features = {}
        
        # 如果feature_dict_len中有嵌入相关特征，添加它们
        if feature_dict_len:
            for feature_name, length in feature_dict_len.items():
                if "embedding" in feature_name.lower():
                    embedding_features[feature_name] = {
                        "type": "Dense",
                        "dimension": embedding_dim,
                        "is_embedding": True
                    }
        
        # 检查配置中是否已有嵌入特征定义
        raw_features = config.get("RawFeature", {})
        for feature_name, feature_info in raw_features.items():
            if feature_info.get("dtype") == "Dense" and "embedding" in feature_name.lower():
                embedding_features[feature_name] = feature_info
        
        return embedding_features

# 全局工厂实例
model_factory = ModelFactory()
backward_compatibility = BackwardCompatibilityWrapper()

def create_model(model_name: str, 
                model_config: Dict[str, Any], 
                **kwargs) -> tf.keras.Model:
    """
    便捷的模型创建函数
    
    Args:
        model_name: 模型名称
        model_config: 模型配置
        **kwargs: 其他参数
        
    Returns:
        模型实例
    """
    return model_factory.create_model(model_name, model_config, **kwargs)

def migrate_to_enhanced_model(legacy_model_name: str, 
                            model_config: Dict[str, Any],
                            **kwargs) -> EnhancedEPMMOENet:
    """
    迁移助手：将旧版模型迁移到增强版
    
    Args:
        legacy_model_name: 旧版模型名称
        model_config: 模型配置
        **kwargs: 其他参数
        
    Returns:
        增强版模型实例
    """
    migration_map = {
        "EPMMOENet": "standard",
        "EPMMOENet_Transformer": "transformer", 
        "EPMMOENet_with_embeddings": "standard"
    }
    
    architecture_type = migration_map.get(legacy_model_name, "standard")
    
    # 检查是否需要嵌入特征
    embedding_features = None
    if legacy_model_name == "EPMMOENet_with_embeddings":
        embedding_features = model_factory._extract_embedding_features(model_config)
    
    return EnhancedEPMMOENet(
        model_config=model_config,
        feature_extensions=embedding_features,
        architecture_type=architecture_type,
        **kwargs
    )

# 为了完全向后兼容，我们可以在这里重新导出旧的类名
# 这样现有的import语句无需修改
# EPMMOENet_Model = backward_compatibility.EPMMOENet_Model  # 注释掉，直接使用原始导入
EPMMOENet_TransformerModel = backward_compatibility.EPMMOENet_TransformerModel

class EPMMOENet_with_embeddings_Model:
    """完全向后兼容的类接口"""
    
    def __init__(self, config, feature_dict_len=None, **kwargs):
        self.model = backward_compatibility.EPMMOENet_with_embeddings_Model(
            config, feature_dict_len, **kwargs)
        
        # 转发所有方法调用
        self.build_model = self.model.build_model if hasattr(self.model, 'build_model') else self._build_model_wrapper
        self.config = config
        self.feature_dict_len = feature_dict_len
        self.with_embeddings = True
    
    def _build_model_wrapper(self, **kwargs):
        """如果需要build_model方法的包装"""
        return self.model
    
    def __call__(self, *args, **kwargs):
        return self.model(*args, **kwargs)
    
    def __getattr__(self, name):
        """转发所有其他属性访问到实际模型"""
        return getattr(self.model, name)