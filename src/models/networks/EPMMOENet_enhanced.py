"""
Enhanced EPMMOENet - 统一的可扩展模型架构

解决的核心问题：
1. 消除模型代码重复
2. 支持动态特征扩展  
3. 统一不同版本的模型接口
4. 保持向后兼容性
"""

import tensorflow as tf
import logging
from typing import Dict, List, Optional, Any, Union
from src.models.layers.layers import CrossLayer, TimeSeriesAttention, TransformerEncoder
from src.models.layers.feature_adapters import EmbeddingFeatureAdapter, DynamicFeatureComposer

class EnhancedEPMMOENet(tf.keras.Model):
    """
    增强版EPMMOENet - 统一支持多种特征类型和扩展
    
    主要改进：
    - 动态特征扩展能力
    - 统一的特征处理接口
    - 支持嵌入特征而无需复制代码
    - 更清晰的架构分层
    """
    
    def __init__(self, 
                 model_config: Dict[str, Any],
                 feature_extensions: Optional[Dict[str, Any]] = None,
                 architecture_type: str = "standard",  # "standard", "transformer"
                 **kwargs):
        """
        初始化增强版EPMMOENet
        
        Args:
            model_config: 模型配置字典
            feature_extensions: 特征扩展配置（如嵌入特征）
            architecture_type: 架构类型（标准版或Transformer版）
            **kwargs: 其他配置参数
        """
        super().__init__()
        
        self.model_config = model_config
        self.feature_extensions = feature_extensions or {}
        self.architecture_type = architecture_type
        self.logger = logging.getLogger(__name__)
        
        # 解析配置参数
        self._parse_config(**kwargs)
        
        # 合并基础特征和扩展特征
        self.unified_features = self._merge_feature_configs()
        
        # 初始化特征处理组件
        self._initialize_feature_processors()
        
        # 初始化模型架构组件
        self._initialize_model_architecture()
        
        self.logger.info(f"Initialized EnhancedEPMMOENet with {len(self.unified_features)} features")
    
    def _parse_config(self, **kwargs):
        """解析配置参数"""
        # 基础参数
        self.default_embedding_dimension = kwargs.get('default_embedding_dimension', 8)
        self.default_gru_dimension = kwargs.get('default_gru_dimension', 32)
        self.expert_num = kwargs.get('expert_num', 8)
        
        # 架构开关
        self.use_cross_layer = kwargs.get('use_cross_layer', True)
        self.use_multitask = kwargs.get('use_multitask', False)
        self.use_mixed_precision = kwargs.get('use_mixed_precision', True)
        self.use_time_attention = kwargs.get('use_time_attention', True)
        self.time_decay_factor = kwargs.get('time_decay_factor', 0.05)
        
        # 输出配置
        self.output_dimension = self.model_config.get("output_dimension", 6)
        self.output_activation = self.model_config.get("output_activation", "sigmoid")
        
        # 多任务学习调整
        if self.use_multitask and self.model_config.get("mask_label"):
            self.output_dimension = self.output_dimension * 2
            self.logger.info(f"Multitask learning enabled: output_dimension = {self.output_dimension}")
        
        # 混合精度训练
        if self.use_mixed_precision:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            self.logger.info("Mixed precision training enabled")
    
    def _merge_feature_configs(self) -> Dict[str, Any]:
        """合并基础特征配置和扩展特征配置"""
        # 获取基础特征配置
        base_features = self.model_config.get("RawFeature", {}).copy()
        
        # 添加扩展特征
        for feature_name, feature_config in self.feature_extensions.items():
            if feature_name not in base_features:
                base_features[feature_name] = feature_config
                self.logger.info(f"Added extension feature: {feature_name}")
            else:
                self.logger.warning(f"Extension feature {feature_name} conflicts with base feature")
        
        return base_features
    
    def _initialize_feature_processors(self):
        """初始化特征处理器"""
        # 解析特征分组
        self.input_general_features = self.model_config.get("InputGeneral", {}).get("features", [])
        self.input_seqset_features = self.model_config.get("InputSeqSet", {}).get("Set", [])
        self.input_seqset_infos = self.model_config.get("InputSeqSet", {}).get("SetInfo", {})
        self.input_scene_features = self.model_config.get("InputScene", {}).get("features", [])
        
        # 计算应用的特征（用于优化）
        input_seq_features = []
        for set_name in self.input_seqset_features:
            input_seq_features.extend(self.input_seqset_infos[set_name]["features"])
        
        applied_features = (self.input_general_features + 
                          self.input_scene_features + 
                          input_seq_features)
        
        # 过滤未使用的特征
        self.active_features = {k: v for k, v in self.unified_features.items() 
                               if k in applied_features}
        
        # 创建特征处理层
        self.embedding_layers = {}
        self.input_general_dimensions = 0
        self.input_seqset_dimensions = 0
        
        self._create_embedding_layers()
    
    def _create_embedding_layers(self):
        """创建特征嵌入层"""
        for feature_name, feature_info in self.active_features.items():
            feature_type = feature_info.get("dtype", feature_info.get("type", "unknown"))
            embedding_layer = self._create_single_embedding_layer(feature_name, feature_info, feature_type)
            
            if embedding_layer is not None:
                self.embedding_layers[feature_name] = embedding_layer
                
                # 计算维度（用于后续层的输入维度计算）
                if feature_name in self.input_general_features:
                    embedding_dim = feature_info.get("embedding_dimension", 
                                                   feature_info.get("dimension", 
                                                                   self.default_embedding_dimension))
                    # 确保embedding_dim不为None
                    if embedding_dim is not None:
                        self.input_general_dimensions += embedding_dim
                    else:
                        self.input_general_dimensions += self.default_embedding_dimension
    
    def _create_single_embedding_layer(self, feature_name: str, feature_info: Dict, feature_type: str):
        """创建单个特征的嵌入层"""
        try:
            if feature_type == "StringLookup":
                return self._create_string_lookup_layer(feature_name, feature_info)
            elif feature_type == "Bucket":
                return self._create_bucket_layer(feature_name, feature_info)
            elif feature_type == "Dense":
                return self._create_dense_layer(feature_name, feature_info)
            elif feature_type == "Embedding":
                return self._create_embedding_passthrough_layer(feature_name, feature_info)
            else:
                self.logger.warning(f"Unknown feature type {feature_type} for {feature_name}")
                return None
        except Exception as e:
            self.logger.error(f"Failed to create embedding layer for {feature_name}: {e}")
            return None
    
    def _create_string_lookup_layer(self, feature_name: str, feature_info: Dict):
        """创建StringLookup类型特征层"""
        vocabulary = feature_info.get("vocabulary", [])
        embedding_dimension = feature_info.get("embedding_dimension", self.default_embedding_dimension)
        
        if not vocabulary:
            raise ValueError(f"Empty vocabulary for feature {feature_name}")
        
        return tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=vocabulary, mask_token=None),
            tf.keras.layers.Embedding(
                len(vocabulary) + 1,
                embedding_dimension,
                embeddings_initializer=tf.keras.initializers.HeNormal()
            )
        ], name=f"string_lookup_{feature_name}")
    
    def _create_bucket_layer(self, feature_name: str, feature_info: Dict):
        """创建Bucket类型特征层"""
        bin_boundaries = feature_info.get("bin_boundarie", feature_info.get("boundaries", []))
        embedding_dimension = feature_info.get("embedding_dimension", self.default_embedding_dimension)
        
        if not bin_boundaries:
            raise ValueError(f"Missing boundaries for feature {feature_name}")
        
        # 确保至少有两个边界
        if len(bin_boundaries) == 1:
            bin_boundaries = bin_boundaries + [float('inf')]
        
        return tf.keras.Sequential([
            tf.keras.layers.Discretization(bin_boundaries),
            tf.keras.layers.Embedding(
                len(bin_boundaries) + 1,
                embedding_dimension,
                embeddings_initializer=tf.keras.initializers.HeNormal()
            )
        ], name=f"bucket_{feature_name}")
    
    def _create_dense_layer(self, feature_name: str, feature_info: Dict):
        """创建Dense类型特征层（用于嵌入特征）"""
        embedding_dimension = feature_info.get("embedding_dimension", 
                                              feature_info.get("dimension", 128))
        
        return tf.keras.Sequential([
            tf.keras.layers.Dense(512, 
                                activation="relu", 
                                kernel_regularizer=tf.keras.regularizers.l2(0.005)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(rate=0.2),
            tf.keras.layers.Dense(embedding_dimension)
        ], name=f"dense_{feature_name}")
    
    def _create_embedding_passthrough_layer(self, feature_name: str, feature_info: Dict):
        """创建Embedding直通层"""
        return tf.keras.layers.Lambda(lambda x: x, name=f"passthrough_{feature_name}")
    
    def _initialize_model_architecture(self):
        """初始化模型架构组件"""
        # 序列处理层
        self._initialize_sequence_layers()
        
        # 特征交叉层
        if self.use_cross_layer:
            self.cross_layer = CrossLayer(128)
        
        # 场景加权层
        if self.input_scene_features:
            self._initialize_scene_layers()
        
        # 预测层
        self._initialize_prediction_layers()
    
    def _initialize_sequence_layers(self):
        """初始化序列处理层"""
        self.sequence_layers = {}
        self.time_attention_layers = {}
        self.mask_feature_names = []
        
        for set_name, set_info in self.input_seqset_infos.items():
            if set_name in self.input_seqset_features:
                gru_dimension = set_info.get("gru_dimension", self.default_gru_dimension)
                
                if self.architecture_type == "transformer":
                    # 使用Transformer架构
                    self.sequence_layers[set_name] = TransformerEncoder(
                        model_dim=gru_dimension,
                        num_heads=4,
                        ff_dim=gru_dimension * 2
                    )
                else:
                    # 使用标准GRU架构
                    if self.use_time_attention:
                        self.sequence_layers[set_name] = tf.keras.layers.GRU(
                            gru_dimension, return_sequences=True)
                        self.time_attention_layers[set_name] = TimeSeriesAttention(
                            attention_dim=gru_dimension//2,
                            time_decay_factor=self.time_decay_factor
                        )
                    else:
                        self.sequence_layers[set_name] = tf.keras.Sequential([
                            tf.keras.layers.GRU(gru_dimension, return_sequences=False)
                        ])
                
                self.mask_feature_names.append(set_info["features"][0])
                self.input_seqset_dimensions += gru_dimension
    
    def _initialize_scene_layers(self):
        """初始化场景加权层"""
        scene_dimension = len(self.input_scene_features) * self.default_embedding_dimension
        total_feature_dimension = self.input_general_dimensions + self.input_seqset_dimensions
        
        self.scene_layers = tf.keras.Sequential([
            tf.keras.layers.Dense(scene_dimension, activation="relu"),
            tf.keras.layers.Dense(total_feature_dimension, activation="sigmoid")
        ], name="scene_weighting")
    
    def _initialize_prediction_layers(self):
        """初始化预测层"""
        if self.use_multitask:
            # 多任务学习架构
            self.shared_layers = tf.keras.Sequential([
                tf.keras.layers.Dense(256, activation="relu"),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.Dense(128, activation="relu"),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.2)
            ], name="shared_features")
            
            # 为每个输出创建独立的预测塔
            self.task_towers = []
            for i in range(self.output_dimension):
                tower = tf.keras.Sequential([
                    tf.keras.layers.Dense(64, activation="relu"),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Dropout(0.2),
                    tf.keras.layers.Dense(1, activation=self.output_activation)
                ], name=f"task_tower_{i}")
                self.task_towers.append(tower)
        else:
            # 单任务架构
            self.tower_layers = tf.keras.Sequential([
                tf.keras.layers.Dense(256, activation="relu"),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.Dense(128, activation="relu"),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(self.output_dimension, activation=self.output_activation)
            ], name="prediction_tower")
    
    def call(self, inputs, training=None):
        """模型前向传播"""
        # 处理一般特征（非序列）
        general_embeddings = self._process_general_features(inputs)
        
        # 处理序列特征
        sequence_embeddings = self._process_sequence_features(inputs)
        
        # 组合特征
        combined_features = self._combine_features(general_embeddings, sequence_embeddings)
        
        # 应用场景加权
        if hasattr(self, 'scene_layers'):
            combined_features = self._apply_scene_weighting(inputs, combined_features)
        
        # 应用特征交叉
        if self.use_cross_layer:
            cross_features = self.cross_layer(combined_features)
            final_features = tf.concat([combined_features, cross_features], axis=-1)
        else:
            final_features = combined_features
        
        # 生成预测
        return self._generate_predictions(final_features)
    
    def _process_general_features(self, inputs):
        """处理一般特征"""
        embeddings = []
        for feature_name in self.input_general_features:
            if feature_name in inputs and feature_name in self.embedding_layers:
                embedding = self.embedding_layers[feature_name](inputs[feature_name])
                embeddings.append(embedding)
        
        if embeddings:
            return tf.concat(embeddings, axis=-1)
        else:
            return None
    
    def _process_sequence_features(self, inputs):
        """处理序列特征"""
        sequence_outputs = []
        
        for set_name in self.input_seqset_features:
            set_info = self.input_seqset_infos[set_name]
            set_features = set_info["features"]
            
            # 获取序列特征嵌入
            seq_embeddings = []
            for feature_name in set_features:
                if feature_name in inputs and feature_name in self.embedding_layers:
                    embedding = self.embedding_layers[feature_name](inputs[feature_name])
                    seq_embeddings.append(embedding)
            
            if seq_embeddings:
                seq_concat = tf.concat(seq_embeddings, axis=-1)
                
                # 检查序列特征维度，确保GRU得到正确的3D输入
                if len(seq_concat.shape) == 2:
                    # 如果是2D，需要扩展为3D: (batch_size, 1, features)
                    seq_concat = tf.expand_dims(seq_concat, axis=1)
                    self.logger.warning(f"序列特征 {set_name} 从2D扩展为3D: {seq_concat.shape}")
                
                # 应用序列处理
                if self.architecture_type == "transformer":
                    seq_output = self.sequence_layers[set_name](seq_concat)
                    # Transformer输出需要聚合
                    seq_output = tf.reduce_mean(seq_output, axis=1)
                else:
                    if self.use_time_attention:
                        gru_output = self.sequence_layers[set_name](seq_concat)
                        seq_output = self.time_attention_layers[set_name](gru_output)
                    else:
                        seq_output = self.sequence_layers[set_name](seq_concat)
                
                sequence_outputs.append(seq_output)
        
        if sequence_outputs:
            return tf.concat(sequence_outputs, axis=-1)
        else:
            return None
    
    def _combine_features(self, general_embeddings, sequence_embeddings):
        """组合不同类型的特征"""
        features = []
        
        if general_embeddings is not None:
            features.append(general_embeddings)
        
        if sequence_embeddings is not None:
            features.append(sequence_embeddings)
        
        if not features:
            raise ValueError("No valid features to process")
        
        return tf.concat(features, axis=-1)
    
    def _apply_scene_weighting(self, inputs, combined_features):
        """应用场景加权"""
        scene_embeddings = []
        for feature_name in self.input_scene_features:
            if feature_name in inputs and feature_name in self.embedding_layers:
                embedding = self.embedding_layers[feature_name](inputs[feature_name])
                scene_embeddings.append(embedding)
        
        if scene_embeddings:
            scene_concat = tf.concat(scene_embeddings, axis=-1)
            scene_weights = self.scene_layers(scene_concat)
            return combined_features * scene_weights
        else:
            return combined_features
    
    def _generate_predictions(self, final_features):
        """生成最终预测"""
        if self.use_multitask:
            # 多任务预测
            shared_features = self.shared_layers(final_features)
            task_outputs = []
            
            has_mask = self.output_dimension > 6
            
            for i, tower in enumerate(self.task_towers):
                # 为第一个月（Month_1）添加特殊处理
                if i == 0 or (has_mask and i == self.output_dimension // 2):
                    enhanced_features = shared_features * 1.5
                    task_output = tower(enhanced_features)
                else:
                    task_output = tower(shared_features)
                task_outputs.append(task_output)
            
            output = tf.concat(task_outputs, axis=1)
            
            # 应用单调性约束
            return self._apply_monotonicity_constraint(output, has_mask)
        else:
            # 单任务预测
            return self.tower_layers(final_features)
    
    def _apply_monotonicity_constraint(self, output, has_mask):
        """应用单调性约束"""
        if has_mask:
            # 分别处理原始标签和累计标签
            mid_point = self.output_dimension // 2
            original_output = output[:, :mid_point]
            cumsum_output = output[:, mid_point:]
            
            # 对原始标签应用单调性约束
            original_constrained = self._apply_monotonic_constraint_to_sequence(original_output)
            
            # 对累计标签应用单调性约束
            cumsum_constrained = self._apply_monotonic_constraint_to_sequence(cumsum_output)
            
            return tf.concat([original_constrained, cumsum_constrained], axis=1)
        else:
            return self._apply_monotonic_constraint_to_sequence(output)
    
    def _apply_monotonic_constraint_to_sequence(self, sequence):
        """对序列应用单调性约束"""
        cummax = sequence[:, 0:1]
        outputs_list = [cummax]
        
        for i in range(1, sequence.shape[1]):
            cummax = tf.maximum(cummax, sequence[:, i:i+1])
            outputs_list.append(cummax)
        
        return tf.concat(outputs_list, axis=1)