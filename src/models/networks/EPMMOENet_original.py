"""
EPMMOENet model implementation for conversion rate prediction.

This model implements a multimodal architecture with time series attention
and feature cross enhancement.
"""
import tensorflow as tf
import logging
from src.models.layers.layers import Cross<PERSON>ayer, TimeSeriesAttention


class EPMMOENet_Model(tf.keras.Model):
    """
    EPMMOENet model for conversion rate prediction.
    
    Features:
    - Multiple input types (categorical, numerical, sequential)
    - Optional feature crossing
    - Time series attention with time decay for sequential features
    - Mixed precision training option
    """
    
    def __init__(self, 
                feature_column, 
                output_dimension=6,
                output_activation="sigmoid",
                default_embedding_dimension=8, 
                default_gru_dimension=32,
                expert_num=8,
                use_cross_layer=True,
                use_mixed_precision=True,
                use_time_attention=True,
                time_decay_factor=0.05
                ):
        """
        Initialize the EPMMOENet model.
        
        Args:
            feature_column (dict): Feature column configuration.
            output_dimension (int): Output dimension.
            output_activation (str): Output activation function.
            default_embedding_dimension (int): Default embedding dimension.
            default_gru_dimension (int): Default GRU dimension.
            expert_num (int): Number of experts for MoE layers.
            use_cross_layer (bool): Whether to use feature cross layer.
            use_mixed_precision (bool): Whether to use mixed precision training.
            use_time_attention (bool): Whether to use time decay attention.
            time_decay_factor (float): Time decay factor for attention.
        """
        super().__init__()
        
        # Store configuration
        self.use_cross_layer = use_cross_layer
        self.use_time_attention = use_time_attention
        self.time_decay_factor = time_decay_factor
        self.logger = logging.getLogger(__name__)
        
        # Enable mixed precision training if requested
        if use_mixed_precision:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            self.logger.info("Mixed precision training enabled")
        
        # Parse feature configuration
        # 1: Input layer parameters
        RawFeature = feature_column.get("RawFeature", {}).copy()
        
        # 2: Feature layer parameters (general + sequence branches)
        self.InputGeneral_features = feature_column.get("InputGeneral", {}).get("features", [])
        self.InputSeqSet_features = feature_column.get("InputSeqSet", {}).get("Set", [])
        self.InputSeqSet_infos = feature_column.get("InputSeqSet", {}).get("SetInfo", {})
        
        # 3: Scene parameters for cross layer
        self.InputScene_features = feature_column.get("InputScene", {}).get("features", [])
        
        # Optimize input layer parameters to skip unused embeddings
        InputSeq_features = []
        for set_name in self.InputSeqSet_features:
            InputSeq_features.extend(self.InputSeqSet_infos[set_name]["features"])
            
        Applied_features = self.InputGeneral_features + self.InputScene_features + InputSeq_features
        self.RawFeature = {k: v for k, v in RawFeature.items() if k in Applied_features}
        
        # Initialize dimensions tracking
        self.InputGeneral_dimensions, self.InputSeqSet_dimensions = 0, 0
        
        # Initialize embedding layers dictionary
        self.Embedding_layers = {}
        
        # Create embedding layers for each feature
        for feature_name, feature_info in self.RawFeature.items():
            # Get feature data type
            feature_dtype = feature_info["dtype"]
            assert feature_dtype in ["StringLookup", "Bucket", "Dense", "Embedding"]
            
            # Create embedding layers based on feature type
            if feature_dtype == "StringLookup":
                vocabulary = feature_info.get("vocabulary", [])
                embedding_dimension = feature_info.get("embedding_dimension", default_embedding_dimension)
                
                # 确保有一个有效的词表 - 注意，这里我们依赖预处理阶段已经为空词表生成了合适的值
                if not vocabulary:
                    raise ValueError(f"Empty vocabulary for feature {feature_name}. Preprocessing should have generated a vocabulary.")
                    
                self.Embedding_layers[feature_name] = tf.keras.Sequential([
                    tf.keras.layers.StringLookup(
                        vocabulary=vocabulary, mask_token=None),
                    tf.keras.layers.Embedding(
                        len(vocabulary) + 1,
                        embedding_dimension,
                        embeddings_initializer=tf.keras.initializers.HeNormal()
                    )
                ])
                
            elif feature_dtype == "Bucket":
                bin_boundarie = feature_info.get("bin_boundarie")
                embedding_dimension = feature_info.get("embedding_dimension", default_embedding_dimension)
                
                # 确保bin_boundarie存在
                if bin_boundarie is None:
                    raise ValueError(f"Missing bin_boundarie for feature {feature_name}.")
                
                # 如果只有一个元素，添加另一个远大于任何可能值的元素
                if len(bin_boundarie) == 1:
                    bin_boundarie = bin_boundarie + [float('inf')]
                
                self.Embedding_layers[feature_name] = tf.keras.Sequential([
                    tf.keras.layers.Discretization(bin_boundarie),
                    tf.keras.layers.Embedding(
                        len(bin_boundarie) + 1,
                        embedding_dimension,
                        embeddings_initializer=tf.keras.initializers.HeNormal()
                    )
                ])
                
            elif feature_dtype == "Dense":
                embedding_dimension = feature_info.get("embedding_dimension", 128)
                self.Embedding_layers[feature_name] = tf.keras.Sequential([
                    tf.keras.layers.Dense(512, 
                                         activation="relu", 
                                         kernel_regularizer=tf.keras.regularizers.l2(0.01)),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Dropout(rate=0.3),
                    tf.keras.layers.Dense(embedding_dimension)
                ])
                
            elif feature_dtype == "Embedding":
                # Direct passthrough
                embedding_dimension = feature_info.get("embedding_dimension", 16)
                self.Embedding_layers[feature_name] = lambda x: x
                
            # Track dimensions for non-sequential branch
            if feature_name in self.InputGeneral_features:
                self.InputGeneral_dimensions += embedding_dimension
        
        # Initialize sequence layer dictionaries
        self.Sequence_layers = {}
        self.TimeAttention_layers = {}
        self.list_mask_feature_name = []
        
        # Create sequence processing layers
        for set_name, set_info in self.InputSeqSet_infos.items():
            if set_name in self.InputSeqSet_features:
                assert len(set_info) > 0
                gru_dimension = set_info.get("gru_dimension", default_gru_dimension)
                
                # Use GRU + time decay attention for sequence features
                if self.use_time_attention:
                    self.Sequence_layers[set_name] = tf.keras.layers.GRU(
                        gru_dimension, return_sequences=True)
                    self.TimeAttention_layers[set_name] = TimeSeriesAttention(
                        attention_dim=gru_dimension//2,
                        time_decay_factor=self.time_decay_factor
                    )
                else:
                    self.Sequence_layers[set_name] = tf.keras.Sequential([
                        tf.keras.layers.GRU(gru_dimension, return_sequences=False)
                    ])
                    
                self.list_mask_feature_name.append(set_info["features"][0])
                
                # Track sequence branch dimensions
                self.InputSeqSet_dimensions += gru_dimension
        
        # Initialize cross layer if enabled
        if self.use_cross_layer:
            self.cross_layer = CrossLayer(64)
            
        # Initialize scene weight network
        if len(self.InputScene_features) > 0:
            self.Scene_layers = tf.keras.models.Sequential([
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.3),
                tf.keras.layers.Dense(
                    256, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.01)),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.3),
                tf.keras.layers.Dense(
                    self.InputGeneral_dimensions + self.InputSeqSet_dimensions, 
                    activation="sigmoid", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.01))
            ])
            
        # Initialize prediction branch
        self.OutputTask_feature = feature_column.get("OutputTask", {})
        if len(self.OutputTask_feature) > 0:
            # Multi-task model (not implemented in this version)
            self.logger.warning("Multi-task model configuration not fully implemented")
            
        # Final prediction layers
        self.Tower_layers = tf.keras.models.Sequential([
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(rate=0.3),
            tf.keras.layers.Dense(
                512, 
                activation="relu", 
                kernel_regularizer=tf.keras.regularizers.l2(0.01)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(rate=0.3),
            tf.keras.layers.Dense(
                256, 
                activation="relu", 
                kernel_regularizer=tf.keras.regularizers.l2(0.01)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(rate=0.2),
            tf.keras.layers.Dense(output_dimension, activation=output_activation)
        ])
        
    def call(self, inputs):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor dictionary.
            
        Returns:
            Tensor: Model predictions.
        """
        # Process general (non-sequence) inputs
        embedding_general_features = []
        for feature_name in self.InputGeneral_features:
            feature_i = inputs[feature_name]
            embedding_i = self.Embedding_layers[feature_name](feature_i)
            embedding_general_features.append(embedding_i)
            
        # Concatenate general features if they exist
        if len(embedding_general_features) > 0:
            embedding_general_all = tf.keras.layers.Concatenate(axis=-1)(embedding_general_features)
        else:
            embedding_general_all = None
            
        # Process sequence inputs
        embedding_seqset_all = []
        for set_name in self.InputSeqSet_features:
            # Get features for this sequence set
            set_info = self.InputSeqSet_infos[set_name]
            set_features = set_info["features"]
            
            # Process each feature in the sequence set
            embedding_seq_features = []
            for feature_name in set_features:
                feature_i = inputs[feature_name]
                embedding_i = self.Embedding_layers[feature_name](feature_i)
                embedding_seq_features.append(embedding_i)
                
            # Concatenate sequence features within this set
            embedding_seq_all = tf.keras.layers.Concatenate(axis=-1)(embedding_seq_features)
            
            # Apply sequence processing (GRU + attention or just GRU)
            if self.use_time_attention:
                gru_output = self.Sequence_layers[set_name](embedding_seq_all)
                attention_output = self.TimeAttention_layers[set_name](gru_output)
                embedding_seqset_all.append(attention_output)
            else:
                sequence_output = self.Sequence_layers[set_name](embedding_seq_all)
                embedding_seqset_all.append(sequence_output)
                
        # Concatenate all sequence outputs if they exist
        if len(embedding_seqset_all) > 0:
            embedding_seqset_concat = tf.keras.layers.Concatenate(axis=-1)(embedding_seqset_all)
        else:
            embedding_seqset_concat = None
            
        # Process scene features for weighting if they exist
        if len(self.InputScene_features) > 0:
            # Get scene features
            embedding_scene_features = []
            for feature_name in self.InputScene_features:
                feature_i = inputs[feature_name]
                embedding_i = self.Embedding_layers[feature_name](feature_i)
                embedding_scene_features.append(embedding_i)
                
            # Apply scene weighting
            scene_concat = tf.keras.layers.Concatenate(axis=-1)(embedding_scene_features)
            scene_weight = self.Scene_layers(scene_concat)
            
            # Combine general and sequence features with scene weights
            if embedding_general_all is not None and embedding_seqset_concat is not None:
                embedding_all = tf.keras.layers.Concatenate(axis=-1)([
                    embedding_general_all, embedding_seqset_concat
                ])
                embedding_all = embedding_all * scene_weight
            elif embedding_general_all is not None:
                embedding_all = embedding_general_all * scene_weight[:, :self.InputGeneral_dimensions]
            elif embedding_seqset_concat is not None:
                embedding_all = embedding_seqset_concat * scene_weight[:, self.InputGeneral_dimensions:]
            else:
                raise ValueError("No valid features to process")
        else:
            # No scene weighting, just concatenate features
            if embedding_general_all is not None and embedding_seqset_concat is not None:
                embedding_all = tf.keras.layers.Concatenate(axis=-1)([
                    embedding_general_all, embedding_seqset_concat
                ])
            elif embedding_general_all is not None:
                embedding_all = embedding_general_all
            elif embedding_seqset_concat is not None:
                embedding_all = embedding_seqset_concat
            else:
                raise ValueError("No valid features to process")
                
        # Apply feature crossing if enabled
        if self.use_cross_layer:
            embedding_cross = self.cross_layer(embedding_all)
            embedding_final = tf.keras.layers.Concatenate(axis=-1)([embedding_all, embedding_cross])
        else:
            embedding_final = embedding_all
            
        # Apply final prediction layers
        output = self.Tower_layers(embedding_final)
        
        return output 