#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Main training script for conversion rate prediction models.
"""
import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import numpy as np
import tensorflow as tf

# Add src to path
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# Import modules
from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor
from src.features.builder import FeatureBuilder
from src.training.enhanced_trainer import ModelTrainer
from src.evaluation.evaluator import ModelEvaluator
from src.utils.config_utils import ConfigManager


def setup_logging(log_level=logging.INFO, log_file=None):
    """
    Set up logging configuration.
    
    Args:
        log_level (int): Logging level.
        log_file (str, optional): Path to log file.
    """
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Create formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Configure file handler if log file is provided
    if log_file:
        Path(os.path.dirname(log_file)).mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Silence third-party loggers
    logging.getLogger('qcloud_cos').setLevel(logging.WARNING)


def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description='Train conversion rate prediction model')
    
    # Basic parameters
    parser.add_argument('--model_code', default='sample_20250311_v7-20250311',
                        help='Model code, corresponds to model parameter file name')
    parser.add_argument('--run_name', default=f"{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        help='Run code, example: 20241101_v1')
    parser.add_argument('--dataset_code', default='dataset_nio_new_car_v15',
                        help='Dataset location (fixed negative sampling factor)')
    parser.add_argument('--evaluate_file', default='20240531_随机采样1%.parquet',
                        help='Evaluation dataset filename (random sampling)')
    
    # Additional parameters
    parser.add_argument('--epochs', type=int, default=50,
                        help='Maximum number of training epochs')
    parser.add_argument('--batch_size', type=int, default=4096,
                        help='Training batch size')
    parser.add_argument('--patience', type=int, default=10,
                        help='Early stopping patience')
    parser.add_argument('--use_cross_layer', type=bool, default=True,
                        help='Whether to use cross layer')
    parser.add_argument('--use_time_attention', type=bool, default=True,
                        help='Whether to use time attention mechanism')
    parser.add_argument('--use_transformer', action='store_true', default=False,
                        help='Whether to use Transformer architecture instead of GRU')
    parser.add_argument('--time_decay_factor', type=float, default=0.02,
                        help='Time decay factor for attention mechanism')
    parser.add_argument('--log_file', default=None,
                        help='Log file path')
    parser.add_argument('--data_dir', default='.',
                        help='Data directory path')
    parser.add_argument('--output_dir', default='logs/experiments',
                        help='Output directory for evaluation results')
    
    # New parameters for loss function
    parser.add_argument('--loss_type', default='focal', choices=['standard', 'weighted', 'focal'],
                        help='Type of loss function: standard, weighted, or focal')
    parser.add_argument('--pos_weight', type=float, default=20.0,
                        help='Weight multiplier for positive class (to improve recall)')
    parser.add_argument('--use_month_weights', action='store_true', default=True,
                        help='Whether to use month-specific weights in loss function')
    parser.add_argument('--use_multitask', action='store_true', default=False,
                        help='Whether to use multitask learning architecture')
    
    # 在parse_args函数中添加嵌入特征相关的参数
    parser.add_argument('--embedding_model', default=None,
                        help='使用支持嵌入特征的模型，例如EPMMOENet_with_embeddings')
    parser.add_argument('--embedding_dir', default=None,
                        help='嵌入向量目录路径，例如logs/my_test_run_028')
    
    return parser.parse_args()


def main():
    """Main execution function."""
    # Parse arguments
    args = parse_args()
    
    # Setup logging
    log_file = args.log_file or f"logs/experiments/{args.run_name}/training.log"
    setup_logging(log_file=log_file)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting training run: {args.run_name}")
    logger.info(f"Model code: {args.model_code}")
    logger.info(f"Dataset code: {args.dataset_code}")
    
    try:
        # Load model configuration
        config_manager = ConfigManager()
        
        # 首先尝试从configs目录读取配置文件，支持YAML和JSON格式
        model_param_path = None
        for ext in ['.yaml', '.json']:
            # 先尝试experiments目录
            test_path = f"src/configs/models/experiments/{args.model_code}{ext}"
            if os.path.exists(test_path):
                model_param_path = test_path
                break
            # 再尝试主目录
            test_path = f"src/configs/models/{args.model_code}{ext}"
            if os.path.exists(test_path):
                model_param_path = test_path
                break
        
        if not model_param_path:
            # 如果都找不到，尝试从项目根目录读取
            for ext in ['.yaml', '.json']:
                test_path = f"{args.model_code}{ext}"
                if os.path.exists(test_path):
                    model_param_path = test_path
                    break
        
        if not model_param_path:
            logger.error(f"Model parameter file not found: {args.model_code}.yaml/json")
            return 1
                
        logger.info(f"Loading model configuration from: {model_param_path}")
        model_config = config_manager.load_config(model_param_path)
        
        # 同样方式处理数据集配置
        dataset_config_path = f"src/configs/datasets/{args.dataset_code}.json"
        if not os.path.exists(dataset_config_path):
            dataset_config_path = f"{args.dataset_code}.json"
            if not os.path.exists(dataset_config_path):
                logger.error(f"Dataset configuration file not found: {dataset_config_path}")
                return 1
                
        logger.info(f"Loading dataset configuration from: {dataset_config_path}")
        
        # 兼容新YAML配置结构
        if 'model_config' in model_config:
            # 新YAML结构
            yaml_config = model_config
            model_config = yaml_config.get('model_config', {})
            training_config = yaml_config.get('training_config', {})
            
            # 合并训练配置
            for key, value in training_config.items():
                if key not in model_config:
                    model_config[key] = value
                    
            # 提取其他配置部分
            input_modules = yaml_config.get('input_modules', {})
            features_config = yaml_config.get('features', {})
            labels_config = yaml_config.get('labels', {})
            
            # 构建兼容的配置结构
            if 'InputGeneral' in input_modules:
                model_config['InputGeneral'] = input_modules['InputGeneral']
            if 'InputScene' in input_modules:
                model_config['InputScene'] = input_modules['InputScene']
            if 'InputSeqSet' in input_modules:
                model_config['InputSeqSet'] = input_modules['InputSeqSet']
            if features_config:
                model_config['RawFeature'] = features_config
            if labels_config:
                model_config['RawLabel'] = labels_config
        
        # Override some config parameters with command line arguments
        model_config['batch_size'] = args.batch_size
        model_config['loss_type'] = args.loss_type
        model_config['pos_weight'] = args.pos_weight
        model_config['use_month_weights'] = args.use_month_weights
        # 只在配置文件中没有设置时才使用命令行参数
        if 'use_multitask' not in model_config:
            model_config['use_multitask'] = args.use_multitask
        
        # Get model parameters
        network_name = model_config.get("network_name")
        predict_method = model_config.get("predict_method", "6m")
        train_dates = model_config.get("train_dates", [])
        test_dates = model_config.get("test_dates", [])
        mask_label = model_config.get("mask_label", None)
        dict_raw_features = model_config.get("RawFeature", {})
        extra_datasets = model_config.get("Extra", [])
        
        logger.info(f"Network name: {network_name}")
        logger.info(f"Prediction method: {predict_method}")
        logger.info(f"Train dates: {train_dates}")
        logger.info(f"Test dates: {test_dates}")
        
        # Prepare column lists
        list_raw_features = list(dict_raw_features.keys())
        list_raw_labels = ["purchase_days_nio_new_car_total"]
        
        logger.info(f"Feature count: {len(list_raw_features)}")
        logger.info(f"Label count: {len(list_raw_labels)}")
        
        # Determine columns to read
        read_columns = ["user_id", "datetime"] + list_raw_features + list_raw_labels
        dataset_read_columns = read_columns + ["m_purchase_days_nio_new_car"]
        
        if mask_label:
            dataset_read_columns.append(mask_label)
        
        # 1. Data Loading
        logger.info("Step 1: Loading data")
        # 构建完整的数据集路径
        dataset_path = os.path.join(args.data_dir, args.dataset_code)
        data_loader = DataLoader(dataset_path, dataset_config_path)
        
        # Load all date partitions for train/test
        dataset_dates = sorted(list(set(train_dates + test_dates)))
        df_raw = data_loader.load_dataset(
            dates=dataset_dates,
            columns=dataset_read_columns,
            extra_datasets=extra_datasets
        )
        
        # Load evaluation dataset
        df_evaluate = data_loader.load_evaluation_data(
            evaluate_file=args.evaluate_file,
            columns=read_columns,
            extra_datasets=extra_datasets
        )
        
        # 2. Data Preprocessing
        logger.info("Step 2: Data preprocessing")
        preprocessor = DataPreprocessor(data_loader.feature_padding_dict, train_dates=train_dates)
        
        # Preprocess features based on dataset configuration
        df_raw = preprocessor.preprocess_features(df_raw)
        df_evaluate = preprocessor.preprocess_features(df_evaluate)
        
        # Preprocess features based on model configuration
        df_raw = preprocessor.preprocess_model_features(df_raw, dict_raw_features)
        df_evaluate = preprocessor.preprocess_model_features(df_evaluate, dict_raw_features)
        
        # Process labels
        df_raw = preprocessor.process_purchase_labels(df_raw)
        df_evaluate = preprocessor.process_purchase_labels(
            df_evaluate, 
            source_column="purchase_days_nio_new_car_total", 
            target_column="m_purchase_days_nio_new_car"
        )
        
        # 3. Feature Engineering
        logger.info("Step 3: Feature engineering")
        feature_builder = FeatureBuilder()
        
        # Split data into train and test sets
        df_train_m, df_test_m = feature_builder.split_train_test(df_raw, test_dates)
        
        # Generate datasets for training
        label_name = "m_purchase_days_nio_new_car_consum"
        ds_train_m = feature_builder.generate_dataset(
            df_train_m, 
            dict_raw_features, 
            label=label_name, 
            batch_size=args.batch_size, 
            mask_label=mask_label, 
            predict_method=predict_method,
            use_multitask=model_config.get("use_multitask", False)
        )
        
        ds_test_m = feature_builder.generate_dataset(
            df_test_m, 
            dict_raw_features, 
            label=label_name, 
            batch_size=args.batch_size, 
            mask_label=mask_label, 
            predict_method=predict_method,
            use_multitask=model_config.get("use_multitask", False)
        )
        
        # 检查是否需要加载预训练的嵌入向量
        embedding_data = None
        if args.embedding_dir:
            try:
                logger.info(f"Loading embeddings from {args.embedding_dir}")
                import pickle
                
                # 加载训练集和测试集的嵌入向量
                train_embeddings_path = f"{args.embedding_dir}/user_embeddings_20240430.pkl"
                test_embeddings_path = f"{args.embedding_dir}/user_embeddings_20240531.pkl"
                
                if os.path.exists(train_embeddings_path) and os.path.exists(test_embeddings_path):
                    with open(train_embeddings_path, 'rb') as f:
                        train_embeddings = pickle.load(f)
                    
                    with open(test_embeddings_path, 'rb') as f:
                        test_embeddings = pickle.load(f)
                    
                    # 合并嵌入向量
                    embedding_data = {**train_embeddings, **test_embeddings}
                    
                    logger.info(f"Loaded embeddings: train={len(train_embeddings)}, test={len(test_embeddings)}")
                    logger.info(f"Combined embedding data: {len(embedding_data)} users")
                else:
                    logger.warning(f"Embedding files not found in {args.embedding_dir}")
            except Exception as e:
                logger.error(f"Error loading embeddings: {e}")
                
        # 4. Model Training
        logger.info("Step 4: Model training")
        trainer = ModelTrainer(model_config, args.run_name, output_dir=f"{args.output_dir}/{args.run_name}")
        
        if embedding_data and args.embedding_model:
            logger.info("Enhancing feature data with embeddings")
            
            # 创建一个添加嵌入向量的函数
            def add_embedding_to_inputs(inputs, embeddings, embedding_dim=16):
                """为每个输入样本添加对应的嵌入向量"""
                # 首先获取用户ID - 假设每个批次的第一个样本包含user_id
                # 实际中可能需要根据具体数据结构调整
                user_ids = []
                for key in inputs:
                    if key.lower() == 'user_id':
                        user_ids = inputs[key].numpy().tolist()
                        break
                
                if not user_ids:
                    # 如果没有找到user_id，尝试使用其他唯一标识符
                    logger.warning("No user_id found in inputs, using default zero embeddings")
                    
                # 创建一个默认的零向量，用于没有嵌入的用户
                default_embedding = np.zeros(embedding_dim)
                
                # 创建嵌入特征数组
                batch_size = next(iter(inputs.values())).shape[0]
                embedding_array = np.zeros((batch_size, embedding_dim))
                
                # 为每个用户添加嵌入向量
                for i, user_id in enumerate(user_ids):
                    if user_id in embeddings:
                        embedding_array[i] = embeddings[user_id]
                
                # 添加到输入字典
                inputs["user_embedding"] = embedding_array
                return inputs
            
            # 确保TensorFlow能够处理Python函数
            def tf_add_embeddings(x, y):
                # 将TensorFlow张量转换为NumPy数组再处理
                inputs_dict = {k: v.numpy() for k, v in x.items()}
                
                # 获取用户ID - 假设存在于inputs中的某个位置
                user_ids = []
                if "user_id" in inputs_dict:
                    user_ids = inputs_dict["user_id"]
                
                # 创建一个默认的零向量，用于没有嵌入的用户
                default_embedding = np.zeros(16)  # 假设嵌入维度为16
                
                # 创建嵌入特征数组
                batch_size = next(iter(inputs_dict.values())).shape[0]
                embedding_array = np.zeros((batch_size, 16))
                
                # 为每个用户添加嵌入向量
                for i in range(batch_size):
                    if i < len(user_ids) and user_ids[i] in embedding_data:
                        embedding_array[i] = embedding_data[user_ids[i]]
                
                # 添加到输入字典
                x["user_embedding"] = tf.convert_to_tensor(embedding_array, dtype=tf.float32)
                return x, y
            
            # 我们将直接在模型训练之前添加嵌入特征
            # 不使用dataset.map，因为可能会有序列化问题
            logger.info("Will add embedding features during model training")
        
        # Build model
        model = trainer.build_model(
            use_cross_layer=args.use_cross_layer,
            use_time_attention=args.use_time_attention,
            time_decay_factor=args.time_decay_factor,
            use_transformer=args.use_transformer,
            embedding_model=args.embedding_model
        )
        
        # Train model
        trainer.train(
            ds_train_m,
            ds_test_m,
            epochs=args.epochs,
            patience=args.patience  # 修正参数名
        )
        
        # 5. Model Inference
        logger.info("Step 5: Model inference")
        # Run inference on test set
        test_predictions = trainer.inference(df_test_m, dict_raw_features)
        df_test_m["result"] = list(test_predictions)
        
        # Run inference on evaluation set
        evaluate_predictions = trainer.inference(df_evaluate, dict_raw_features)
        df_evaluate["result"] = list(evaluate_predictions)
        
        # 6. Model Evaluation
        logger.info("Step 6: Model evaluation")
        # 构建评估器期望的完整输出路径
        evaluation_output_path = os.path.join(args.output_dir, args.run_name)
        evaluator = ModelEvaluator(evaluation_output_path)
        
        evaluation_results = evaluator.evaluate_model(
            df_test_m=df_test_m,
            df_evaluate=df_evaluate,
            label_column="m_purchase_days_nio_new_car",
            pred_column="result",
            mask_label_column=mask_label,
            model_code=args.model_code
        )
        
        logger.info("Training pipeline completed successfully")
        return 0
        
    except Exception as e:
        logger.exception(f"Error in training pipeline: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 