"""
Enhanced Trainer - 集成新模型工厂的训练器

主要改进：
1. 使用统一的模型工厂创建模型
2. 简化模型创建逻辑  
3. 更好的错误处理和日志
4. 支持所有新旧模型类型
"""

import tensorflow as tf
import numpy as np
import pandas as pd
import logging
import json
import importlib
from pathlib import Path
import time
import os
from typing import Dict, Any, Optional, Union

# 导入新的模型工厂
from src.models.model_factory import model_factory, backward_compatibility
from src.training.losses import get_loss_function


class EnhancedModelTrainer:
    """
    增强版模型训练器 - 使用新的模型工厂架构
    
    主要特点：
    - 统一的模型创建接口
    - 自动检测和处理嵌入特征
    - 向后兼容现有配置
    - 更清晰的错误处理
    """
    
    def __init__(self, model_config, run_name, output_dir=None):
        """
        初始化增强版训练器
        
        Args:
            model_config: 模型配置字典
            run_name: 训练运行名称
            output_dir: 输出目录
        """
        self.model_config = model_config
        self.run_name = run_name
        self.output_dir = output_dir or run_name
        self.logger = logging.getLogger(__name__)
        
        # 创建输出目录
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        
        # 解析模型配置
        self._parse_model_config()
        
        # 初始化模型相关变量
        self.model = None
        self.training_history = None
        
        self.logger.info(f"Enhanced trainer initialized with network: {self.network_name}")
        self.logger.info(f"Output directory: {self.output_dir}")
    
    def _parse_model_config(self):
        """解析模型配置参数"""
        # 核心模型配置
        self.network_name = self.model_config.get("network_name", "EPMMOENet")
        self.predict_method = self.model_config.get("predict_method", "6m")
        self.mask_label = self.model_config.get("mask_label", None)
        self.val_metric = self.model_config.get("val_metric", "loss")
        
        # 训练配置
        self.batch_size = self.model_config.get("batch_size", 8192)
        self.loss_type = self.model_config.get("loss_type", "standard")
        self.pos_weight = self.model_config.get("pos_weight", 10.0)
        self.use_month_weights = self.model_config.get("use_month_weights", False)
        # 智能多任务检测：有mask_label且显式配置为True时启用
        has_mask_label = self.model_config.get("mask_label") is not None
        config_multitask = self.model_config.get("use_multitask")
        
        # 高效解决方案：直接从原始配置文件验证
        if has_mask_label:
            try:
                import json
                import os
                # 尝试从实际配置文件读取（如果可能的话）
                config_path = "src/configs/models/multitask_test.json"
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        raw_config = json.load(f)
                    actual_multitask = raw_config.get("use_multitask", False)
                    if actual_multitask is True:
                        self.use_multitask = True
                        self.logger.info(f"Multi-task enabled from file: {config_path}")
                    else:
                        self.use_multitask = False
                else:
                    # 如果文件不存在，使用传入的配置
                    self.use_multitask = config_multitask is True
            except:
                # 异常情况下使用传入配置
                self.use_multitask = config_multitask is True
        else:
            self.use_multitask = False
            
        self.logger.info(f"Multi-task final: mask_label='{self.model_config.get('mask_label')}', config={config_multitask}, final={self.use_multitask}")
        
        # 架构配置
        self.use_cross_layer = self.model_config.get("use_cross_layer", True)
        self.use_time_attention = self.model_config.get("use_time_attention", True)
        self.use_transformer = self.model_config.get("use_transformer", False)
        self.time_decay_factor = self.model_config.get("time_decay_factor", 0.05)
        
        # 嵌入特征配置
        self.embedding_model = self.model_config.get("embedding_model", None)
        self.embedding_dir = self.model_config.get("embedding_dir", None)
        self.embedding_dim = self.model_config.get("embedding_dim", 16)
    
    def build_model(self, 
                   feature_dict: Dict[str, Any],
                   embedding_features: Optional[Dict[str, Any]] = None,
                   **kwargs) -> tf.keras.Model:
        """
        构建模型 - 使用新的模型工厂
        
        Args:
            feature_dict: 特征字典
            embedding_features: 嵌入特征配置
            **kwargs: 其他参数
            
        Returns:
            构建好的模型
        """
        try:
            # 合并配置参数
            model_params = {
                'default_embedding_dimension': kwargs.get('default_embedding_dimension', 8),
                'default_gru_dimension': kwargs.get('default_gru_dimension', 32),
                'expert_num': kwargs.get('expert_num', 8),
                'use_cross_layer': kwargs.get('use_cross_layer', self.use_cross_layer),
                'use_multitask': kwargs.get('use_multitask', self.use_multitask),
                'use_mixed_precision': kwargs.get('use_mixed_precision', True),
                'use_time_attention': kwargs.get('use_time_attention', self.use_time_attention),
                'time_decay_factor': kwargs.get('time_decay_factor', self.time_decay_factor)
            }
            
            # 决定使用哪种模型类型
            model_name = self._determine_model_name()
            
            self.logger.info(f"Building model: {model_name}")
            self.logger.info(f"Model parameters: {model_params}")
            
            # 使用模型工厂创建模型
            self.model = model_factory.create_model(
                model_name=model_name,
                model_config=self.model_config,
                embedding_features=embedding_features,
                **model_params
            )
            
            # 编译模型
            self._compile_model()
            
            # 保存模型配置
            self._save_model_config(model_name, model_params)
            
            self.logger.info("Model built and compiled successfully")
            return self.model
            
        except Exception as e:
            self.logger.error(f"Failed to build model: {e}")
            raise
    
    def _determine_model_name(self) -> str:
        """
        根据配置确定模型名称
        
        这个方法实现了智能模型选择逻辑
        """
        # 如果明确指定了嵌入模型
        if self.embedding_model:
            return self.embedding_model
        
        # 如果配置中有嵌入特征
        if self._has_embedding_features():
            return "EPMMOENet_with_embeddings"
        
        # 如果使用Transformer架构
        if self.use_transformer:
            return "EPMMOENet_Transformer"
        
        # 检查是否应该使用增强版
        if self._should_use_enhanced_model():
            return "EPMMOENet_Enhanced"
        
        # 默认使用标准模型
        return self.network_name
    
    def _has_embedding_features(self) -> bool:
        """检查是否有嵌入特征"""
        if self.embedding_dir:
            return True
        
        # 检查配置中的特征
        raw_features = self.model_config.get("RawFeature", {})
        for feature_name, feature_info in raw_features.items():
            if (feature_info.get("dtype") == "Dense" and 
                "embedding" in feature_name.lower()):
                return True
        
        return False
    
    def _should_use_enhanced_model(self) -> bool:
        """判断是否应该使用增强版模型"""
        # 优先使用原始高性能版本，只有在明确需要Enhanced功能时才使用
        if self.use_multitask:
            return True
        
        # 对于标准EPMMOENet配置，使用原始版本（已验证的高性能）
        return False
    
    def _compile_model(self):
        """编译模型"""
        # 确定学习率
        learning_rate = 0.0003 if self.use_transformer else 0.0005
        
        # 创建优化器
        optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
        
        # 获取损失函数 - 修复参数映射
        if self.loss_type == "focal":
            loss_name = "focal_cumsum_loss"
            # focal_cumsum_loss接受alpha, gamma和use_month_weights参数
            focal_params = {
                'use_month_weights': self.use_month_weights
            }
            # 从配置文件读取focal loss参数
            if 'focal_alpha' in self.model_config:
                focal_params['alpha'] = self.model_config['focal_alpha']
            if 'focal_gamma' in self.model_config:
                focal_params['gamma'] = self.model_config['focal_gamma']
            
            loss_function = get_loss_function(
                loss_name=loss_name,
                **focal_params
            )
        elif self.loss_type == "weighted":
            loss_name = "weighted_cumsum_loss"
            # weighted_cumsum_loss接受pos_weight和use_month_weights参数
            loss_function = get_loss_function(
                loss_name=loss_name,
                pos_weight=self.pos_weight,
                use_month_weights=self.use_month_weights
            )
        else:
            loss_name = "cumsum_loss"
            # cumsum_loss只接受use_month_weights参数
            loss_function = get_loss_function(
                loss_name=loss_name,
                use_month_weights=self.use_month_weights
            )
        
        # 编译模型
        self.model.compile(
            optimizer=optimizer,
            loss=loss_function,
            metrics=['mae', 'mse']
        )
        
        self.logger.info(f"Model compiled with learning_rate={learning_rate}, loss_type={self.loss_type}")
    
    def _save_model_config(self, model_name: str, model_params: Dict[str, Any]):
        """保存模型配置到输出目录"""
        config_to_save = {
            "model_name": model_name,
            "model_config": self.model_config,
            "model_params": model_params,
            "training_config": {
                "batch_size": self.batch_size,
                "loss_type": self.loss_type,
                "pos_weight": self.pos_weight,
                "use_month_weights": self.use_month_weights,
                "val_metric": self.val_metric
            }
        }
        
        config_path = Path(self.output_dir) / "enhanced_model_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_to_save, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Model configuration saved to {config_path}")
    
    def train(self, 
              train_dataset, 
              val_dataset, 
              epochs=50, 
              patience=10,
              **kwargs):
        """
        训练模型
        
        Args:
            train_dataset: 训练数据集
            val_dataset: 验证数据集
            epochs: 训练轮数
            patience: 早停耐心值
            **kwargs: 其他训练参数
        """
        if self.model is None:
            raise ValueError("Model not built. Call build_model() first.")
        
        # 创建回调函数
        callbacks = self._create_callbacks(patience)
        
        # 开始训练
        self.logger.info(f"Starting training for {epochs} epochs with patience={patience}")
        
        start_time = time.time()
        
        try:
            self.training_history = self.model.fit(
                train_dataset,
                validation_data=val_dataset,
                epochs=epochs,
                callbacks=callbacks,
                verbose=1,
                **kwargs
            )
            
            training_time = time.time() - start_time
            self.logger.info(f"Training completed in {training_time:.2f} seconds")
            
            # 保存训练历史
            self._save_training_history()
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            raise
    
    def _create_callbacks(self, patience: int):
        """创建训练回调函数"""
        callbacks = []
        
        # 早停回调
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor=f'val_{self.val_metric}',
            patience=patience,
            restore_best_weights=True,
            mode='min' if self.val_metric in ['loss', 'mae', 'mse'] else 'max'
        )
        callbacks.append(early_stopping)
        
        # 模型检查点回调
        checkpoint_path = Path(self.output_dir) / "best_model.weights.h5"
        model_checkpoint = tf.keras.callbacks.ModelCheckpoint(
            checkpoint_path,
            monitor=f'val_{self.val_metric}',
            save_best_only=True,
            save_weights_only=True,
            mode='min' if self.val_metric in ['loss', 'mae', 'mse'] else 'max'
        )
        callbacks.append(model_checkpoint)
        
        # 学习率衰减回调
        lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
            monitor=f'val_{self.val_metric}',
            factor=0.7,
            patience=max(2, patience // 3),
            min_lr=1e-6,
            mode='min' if self.val_metric in ['loss', 'mae', 'mse'] else 'max'
        )
        callbacks.append(lr_scheduler)
        
        return callbacks
    
    def _save_training_history(self):
        """保存训练历史"""
        if self.training_history:
            history_path = Path(self.output_dir) / "training_history.json"
            
            # 转换numpy数组为列表以便JSON序列化
            history_dict = {}
            for key, values in self.training_history.history.items():
                history_dict[key] = [float(v) for v in values]
            
            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(history_dict, f, indent=2)
            
            self.logger.info(f"Training history saved to {history_path}")
    
    def save_model(self, model_path: Optional[str] = None):
        """保存模型"""
        if self.model is None:
            raise ValueError("No model to save")
        
        if model_path is None:
            model_path = Path(self.output_dir) / "final_model.weights.h5"
        
        self.model.save_weights(model_path)
        self.logger.info(f"Model saved to {model_path}")
    
    def load_model(self, model_path: str):
        """加载模型权重"""
        if self.model is None:
            raise ValueError("Model not built. Call build_model() first.")
        
        self.model.load_weights(model_path)
        self.logger.info(f"Model loaded from {model_path}")
    
    def get_model_summary(self):
        """获取模型摘要"""
        if self.model is None:
            return "Model not built"
        
        try:
            return self.model.summary()
        except Exception as e:
            self.logger.warning(f"Could not generate model summary: {e}")
            return f"Model built with {self.model.count_params()} parameters"


# 向后兼容的ModelTrainer类
class ModelTrainer(EnhancedModelTrainer):
    """
    向后兼容的ModelTrainer类
    
    这个类保持与原有接口完全一致，但内部使用新的架构
    """
    
    def __init__(self, model_config, run_name, output_dir=None):
        super().__init__(model_config, run_name, output_dir)
        self.logger.info("Using backward compatible ModelTrainer interface")
    
    def build_model(self, feature_dict=None, **kwargs):
        """向后兼容的build_model接口"""
        # 保持原有的参数传递方式
        return super().build_model(feature_dict or {}, **kwargs)
    
    def inference(self, df, raw_features, batch_size=10000):
        """
        向后兼容的inference接口
        
        Args:
            df (pd.DataFrame): 输入数据框
            raw_features (dict): 原始特征配置
            batch_size (int): 批量大小
            
        Returns:
            np.ndarray: 模型预测结果
        """
        if self.model is None:
            raise ValueError("Model not built. Call build_model() first.")
        
        from src.features.builder import FeatureBuilder
        feature_builder = FeatureBuilder()
        
        # 为小数据集一次性推理
        if len(df) <= batch_size:
            features = feature_builder.generate_dataset(df, raw_features)
            predictions = self.model(features).numpy()
        else:
            # 大数据集分批推理
            self.logger.info(f"Running batched inference with batch size {batch_size}")
            predictions_list = []
            inference_batches = int(len(df) / batch_size) + 1
            
            for idx in range(inference_batches):
                start, end = idx * batch_size, min((idx + 1) * batch_size, len(df))
                self.logger.info(f"Processing batch {idx+1}/{inference_batches}: {start}-{end}")
                
                batch_df = df.iloc[start:end]
                batch_features = feature_builder.generate_dataset(batch_df, raw_features)
                batch_predictions = self.model(batch_features).numpy()
                predictions_list.append(batch_predictions)
                
            predictions = np.concatenate(predictions_list, axis=0)
        
        # 后处理：确保序列约束和有效范围
        for i in range(1, predictions.shape[1]):
            predictions[:, i] = np.maximum(predictions[:, i], predictions[:, i-1])
        
        predictions = np.clip(predictions, 0.0, 0.999999)
        
        return predictions