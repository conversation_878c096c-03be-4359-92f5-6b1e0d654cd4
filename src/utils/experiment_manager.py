"""
实验管理器 - 统一管理模型优化实验

主要功能：
1. 自动化实验配置和执行
2. 实验结果跟踪和对比
3. 最优模型选择和保存
4. 实验复现保证
5. 性能监控和报告生成
"""

import os
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np

from src.configs.unified_config_manager import UnifiedConfigManager
from src.data.enhanced_preprocessor import EnhancedDataPreprocessor
from src.data.loader import DataLoader
from src.features.builder import FeatureBuilder
from src.training.enhanced_trainer import EnhancedModelTrainer
from src.evaluation.evaluator import ModelEvaluator


class ExperimentManager:
    """
    实验管理器 - 统一管理优化实验的完整生命周期
    """
    
    def __init__(self, 
                 base_config_name: str = "GOLDEN_CONFIG",
                 experiment_dir: str = "logs/experiments"):
        """
        初始化实验管理器
        
        Args:
            base_config_name: 基线配置名称
            experiment_dir: 实验结果保存目录
        """
        self.base_config_name = base_config_name
        self.experiment_dir = Path(experiment_dir)
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        self.config_manager = UnifiedConfigManager()
        self.logger = logging.getLogger(__name__)
        
        # 实验历史记录
        self.experiment_history = []
        self.best_experiment = None
        
        # 加载基线配置
        try:
            # 首先尝试从models目录加载YAML配置
            models_dir = Path("src/configs/models")
            base_config_file = models_dir / f"{base_config_name}.yaml"

            if base_config_file.exists():
                import yaml
                with open(base_config_file, 'r', encoding='utf-8') as f:
                    self.base_config = yaml.safe_load(f)
                self.logger.info(f"基线配置加载成功: {base_config_name}")
            else:
                self.logger.warning(f"基线配置文件不存在: {base_config_file}")
                self.base_config = None
        except Exception as e:
            self.logger.error(f"基线配置加载失败: {e}")
            self.base_config = None
    
    def run_optimization_experiment(self, 
                                   experiment_config_name: str,
                                   epochs: int = 3,
                                   quick_test: bool = False) -> Dict[str, Any]:
        """
        运行优化实验
        
        Args:
            experiment_config_name: 实验配置名称
            epochs: 训练轮数
            quick_test: 是否快速测试模式
            
        Returns:
            实验结果字典
        """
        self.logger.info(f"开始优化实验: {experiment_config_name}")
        
        experiment_start_time = time.time()
        experiment_id = f"{experiment_config_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # 1. 加载实验配置
            models_dir = Path("src/configs/models")
            config_file = models_dir / f"{experiment_config_name}.yaml"

            if not config_file.exists():
                raise FileNotFoundError(f"实验配置文件不存在: {config_file}")

            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                experiment_config = yaml.safe_load(f)
            
            # 2. 执行实验
            experiment_results = self._execute_single_experiment(
                experiment_config, 
                experiment_id,
                epochs,
                quick_test
            )
            
            # 3. 与基线对比
            if self.base_config:
                comparison_results = self._compare_with_baseline(experiment_results)
                experiment_results['baseline_comparison'] = comparison_results
            
            # 4. 保存实验结果
            self._save_experiment_results(experiment_id, experiment_results)
            
            # 5. 更新最优实验
            self._update_best_experiment(experiment_results)
            
            # 6. 记录实验历史
            self.experiment_history.append({
                'experiment_id': experiment_id,
                'config_name': experiment_config_name,
                'timestamp': datetime.now().isoformat(),
                'duration': time.time() - experiment_start_time,
                'success': True,
                'key_metrics': experiment_results.get('metrics', {})
            })
            
            self.logger.info(f"实验完成: {experiment_id}")
            return experiment_results
            
        except Exception as e:
            self.logger.error(f"实验失败: {experiment_id}, 错误: {e}")
            
            # 记录失败的实验
            self.experiment_history.append({
                'experiment_id': experiment_id,
                'config_name': experiment_config_name,
                'timestamp': datetime.now().isoformat(),
                'duration': time.time() - experiment_start_time,
                'success': False,
                'error': str(e)
            })
            
            raise
    
    def _execute_single_experiment(self, 
                                  config: Dict[str, Any],
                                  experiment_id: str,
                                  epochs: int,
                                  quick_test: bool) -> Dict[str, Any]:
        """
        执行单个实验
        """
        self.logger.info(f"执行实验: {experiment_id}")
        
        experiment_results = {
            'experiment_id': experiment_id,
            'config': config,
            'timestamp': datetime.now().isoformat(),
            'stages': {}
        }
        
        # 阶段1: 数据加载
        stage_start = time.time()
        data_loader = DataLoader("data/dataset_nio_new_car_v15")
        
        train_dates = config['model_config']['train_dates']
        test_dates = config['model_config']['test_dates']
        
        if quick_test:
            # 快速测试模式：只加载部分数据
            train_data = data_loader.load_dataset(train_dates[:1])  # 只加载第一个日期
            test_data = data_loader.load_dataset(test_dates[:1])
            train_data = train_data.sample(n=min(1000, len(train_data)))  # 最多1000条
            test_data = test_data.sample(n=min(500, len(test_data)))  # 最多500条
        else:
            train_data = data_loader.load_dataset(train_dates)
            test_data = data_loader.load_dataset(test_dates)
        
        experiment_results['stages']['data_loading'] = {
            'duration': time.time() - stage_start,
            'train_shape': train_data.shape,
            'test_shape': test_data.shape
        }
        
        # 阶段2: 数据预处理
        stage_start = time.time()
        
        # 使用增强版预处理器
        optimization_config = config.get('feature_optimization', {})
        preprocessor = EnhancedDataPreprocessor(
            optimization_config=optimization_config
        )
        
        # 预处理训练数据
        train_processed, train_report = preprocessor.enhanced_preprocess(
            train_data,
            target_column='m_purchase_days_nio_new_car_consum',
            feature_config=config.get('features', {}),
            apply_optimization=optimization_config.get('enable_optimization', False)
        )
        
        # 预处理测试数据（应用相同的变换但不重新计算策略）
        test_processed, test_report = preprocessor.enhanced_preprocess(
            test_data,
            target_column='m_purchase_days_nio_new_car_consum',
            feature_config=config.get('features', {}),
            apply_optimization=False  # 测试数据不重新优化
        )
        
        experiment_results['stages']['preprocessing'] = {
            'duration': time.time() - stage_start,
            'train_report': train_report,
            'test_report': test_report,
            'optimization_applied': optimization_config.get('enable_optimization', False)
        }
        
        # 阶段3: 特征构建
        stage_start = time.time()
        feature_builder = FeatureBuilder()
        
        train_dataset = feature_builder.generate_dataset(
            train_processed,
            config.get('features', {}),
            label='m_purchase_days_nio_new_car_consum',
            batch_size=config['training_config']['batch_size']
        )
        
        test_features = feature_builder.generate_dataset(
            test_processed,
            config.get('features', {}),
            label=None
        )
        
        experiment_results['stages']['feature_building'] = {
            'duration': time.time() - stage_start,
            'feature_count': len(config.get('features', {}))
        }
        
        # 阶段4: 模型训练
        stage_start = time.time()
        trainer = EnhancedModelTrainer(config, experiment_id)
        
        # 构建模型
        trainer.build_model(train_dataset.take(1))
        
        # 训练模型
        trainer.train(train_dataset, epochs=epochs, patience=10)
        
        training_duration = time.time() - stage_start
        
        experiment_results['stages']['training'] = {
            'duration': training_duration,
            'epochs': epochs,
            'model_params': trainer.model.count_params(),
            'training_history': trainer.training_history.history if trainer.training_history else None
        }
        
        # 阶段5: 模型评估
        stage_start = time.time()
        evaluator = ModelEvaluator()
        
        # 预测
        predictions = trainer.model.predict(test_features)
        
        # 评估
        true_labels = test_processed['m_purchase_days_nio_new_car_consum'].tolist()
        metrics = evaluator.evaluate_predictions(true_labels, predictions)
        
        experiment_results['stages']['evaluation'] = {
            'duration': time.time() - stage_start,
            'metrics': metrics
        }
        
        # 汇总结果
        experiment_results['metrics'] = metrics
        experiment_results['total_duration'] = sum(
            stage['duration'] for stage in experiment_results['stages'].values()
        )
        
        return experiment_results
    
    def _compare_with_baseline(self, experiment_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        与基线实验对比
        """
        if not self.base_config:
            return {'error': 'No baseline config available'}
        
        # 这里可以实现与历史基线结果的对比
        # 为简化，我们使用预设的基线指标
        baseline_metrics = {
            'month_1_pr_auc': 0.2545,  # GOLDEN_CONFIG的已知性能
            'month_1_recall_840': 0.5462,
            'training_time': 23.51
        }
        
        experiment_metrics = experiment_results.get('metrics', {})
        
        comparison = {
            'baseline_metrics': baseline_metrics,
            'experiment_metrics': experiment_metrics,
            'improvements': {}
        }
        
        # 计算改进
        for metric in baseline_metrics:
            if metric in experiment_metrics:
                baseline_value = baseline_metrics[metric]
                experiment_value = experiment_metrics[metric]
                
                if baseline_value != 0:
                    improvement = (experiment_value - baseline_value) / baseline_value
                    comparison['improvements'][metric] = {
                        'absolute': experiment_value - baseline_value,
                        'relative': improvement,
                        'percentage': improvement * 100
                    }
        
        return comparison
    
    def _save_experiment_results(self, experiment_id: str, results: Dict[str, Any]):
        """
        保存实验结果
        """
        results_file = self.experiment_dir / f"{experiment_id}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"实验结果已保存: {results_file}")
    
    def _update_best_experiment(self, experiment_results: Dict[str, Any]):
        """
        更新最优实验记录
        """
        current_pr_auc = experiment_results.get('metrics', {}).get('month_1_pr_auc', 0)
        
        if self.best_experiment is None or current_pr_auc > self.best_experiment.get('metrics', {}).get('month_1_pr_auc', 0):
            self.best_experiment = experiment_results
            self.logger.info(f"发现新的最优实验: {experiment_results['experiment_id']}, "
                           f"Month_1 PR-AUC: {current_pr_auc:.4f}")
    
    def get_experiment_summary(self) -> Dict[str, Any]:
        """
        获取实验摘要
        """
        successful_experiments = [exp for exp in self.experiment_history if exp['success']]
        failed_experiments = [exp for exp in self.experiment_history if not exp['success']]
        
        summary = {
            'total_experiments': len(self.experiment_history),
            'successful_experiments': len(successful_experiments),
            'failed_experiments': len(failed_experiments),
            'success_rate': len(successful_experiments) / len(self.experiment_history) if self.experiment_history else 0,
            'best_experiment': self.best_experiment,
            'experiment_history': self.experiment_history
        }
        
        return summary
    
    def run_batch_experiments(self, 
                             experiment_configs: List[str],
                             epochs: int = 3) -> Dict[str, Any]:
        """
        批量运行实验
        """
        self.logger.info(f"开始批量实验: {len(experiment_configs)}个配置")
        
        batch_results = {
            'batch_id': f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'configs': experiment_configs,
            'results': {},
            'summary': {}
        }
        
        for config_name in experiment_configs:
            try:
                result = self.run_optimization_experiment(config_name, epochs)
                batch_results['results'][config_name] = result
            except Exception as e:
                self.logger.error(f"批量实验中的配置{config_name}失败: {e}")
                batch_results['results'][config_name] = {'error': str(e)}
        
        # 生成批量摘要
        batch_results['summary'] = self.get_experiment_summary()
        
        # 保存批量结果
        batch_file = self.experiment_dir / f"{batch_results['batch_id']}.json"
        with open(batch_file, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"批量实验完成，结果保存至: {batch_file}")
        return batch_results
